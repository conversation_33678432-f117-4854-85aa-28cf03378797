# AI任务管理系统

一个完整的AI任务管理系统，支持项目管理、需求管理、任务管理、执行控制和文件预览等功能。

## 🚀 快速开始

### 1. 系统要求

- Python 3.7+
- Flask
- Werkzeug

### 2. 安装依赖

```bash
pip install flask werkzeug
```

### 3. 系统检查

运行系统健康检查，确保所有组件正常：

```bash
python debug/system_check.py
```

### 4. 启动系统

```bash
python run_app.py
```

默认访问地址：http://localhost:5000

### 5. 启动参数

```bash
python run_app.py --help
```

可用参数：
- `--host`: 服务器主机地址（默认：0.0.0.0）
- `--port`: 服务器端口（默认：5000）
- `--debug`: 启用调试模式
- `--data-dir`: 数据存储目录（默认：./data）

## 📋 功能特性

### 🏗️ 项目管理
- ✅ 创建、查看、删除项目
- ✅ 项目目录管理
- ✅ 项目统计信息
- ✅ 项目文件浏览

### 📝 需求管理
- ✅ 创建、查看、删除需求
- ✅ 需求优先级管理
- ✅ 需求状态跟踪
- ✅ 需求与任务关联

### ⚡ 任务管理
- ✅ AI自动任务生成
- ✅ 任务依赖关系管理
- ✅ 任务优先级设置
- ✅ 任务状态管理
- ✅ 批量任务操作

### 🎯 执行控制
- ✅ 并行/顺序执行模式
- ✅ 任务执行控制（启动/停止）
- ✅ 实时进度监控
- ✅ 执行参数配置
- ✅ 快速任务功能

### 📊 日志管理
- ✅ 全程执行日志记录
- ✅ 日志级别筛选
- ✅ 日志搜索功能
- ✅ 日志导出功能
- ✅ 实时日志显示

### 📁 文件预览
- ✅ 项目文件浏览
- ✅ 多格式文件预览
- ✅ AI生成文件标记
- ✅ 文件下载功能
- ✅ 文件统计信息

## 🏗️ 系统架构

```
AI任务管理系统/
├── src/                    # 核心源码
│   ├── project_manager.py  # 项目管理
│   ├── task_manager.py     # 任务管理
│   ├── log_manager.py      # 日志管理
│   ├── file_manager.py     # 文件管理
│   └── web_app.py          # Web应用
├── templates/              # HTML模板
│   ├── base.html          # 基础模板
│   ├── index.html         # 首页
│   ├── projects.html      # 项目列表
│   ├── project_detail.html # 项目详情
│   ├── requirement_detail.html # 需求详情
│   ├── task_management.html # 任务管理
│   ├── task_execution.html # 执行管理
│   └── file_browser.html  # 文件浏览
├── debug/                 # 调试和测试
├── data/                  # 数据存储
└── run_app.py            # 启动脚本
```

## 💻 使用指南

### 1. 创建项目

1. 访问系统首页
2. 点击"新建项目"
3. 填写项目名称、描述和工作目录
4. 点击"创建"完成

### 2. 添加需求

1. 进入项目详情页面
2. 点击"新建需求"
3. 填写需求标题、描述和优先级
4. 点击"创建"完成

### 3. 生成任务

1. 进入需求详情页面
2. 点击"任务管理"
3. 点击"AI生成任务"
4. 设置生成参数
5. 点击"生成"完成

### 4. 执行任务

1. 进入需求详情页面
2. 点击"执行管理"
3. 配置执行参数
4. 选择执行模式（并行/顺序）
5. 点击"开始执行"

### 5. 查看结果

1. 在执行管理页面查看实时进度
2. 查看执行日志
3. 在文件浏览器中查看生成的文件

## 🔧 开发指南

### 运行测试

```bash
python debug/system_test.py
```

### 模块说明

#### ProjectManager
- 项目和需求的CRUD操作
- 数据持久化存储
- 项目目录管理

#### TaskManager
- 任务的CRUD操作
- 依赖关系管理
- 任务执行控制

#### LogManager
- 执行日志记录
- 日志查询和筛选
- 日志持久化存储

#### FileManager
- 文件系统操作
- 文件预览功能
- AI文件检测

### API接口

#### 项目管理API
```
GET  /api/projects              # 获取项目列表
POST /api/projects              # 创建项目
GET  /api/projects/<id>         # 获取项目详情
PUT  /api/projects/<id>         # 更新项目
DELETE /api/projects/<id>       # 删除项目
```

#### 需求管理API
```
POST /api/projects/<id>/requirements    # 创建需求
GET  /api/requirements/<id>             # 获取需求详情
PUT  /api/requirements/<id>             # 更新需求
DELETE /api/requirements/<id>           # 删除需求
```

#### 任务管理API
```
POST /api/requirements/<id>/generate_tasks  # 生成任务
GET  /api/requirements/<id>/tasks          # 获取任务列表
POST /api/requirements/<id>/run_tasks      # 执行任务
POST /api/requirements/<id>/stop_tasks     # 停止任务
```

#### 文件管理API
```
GET /api/projects/<id>/files               # 获取文件列表
GET /api/projects/<id>/files/preview       # 预览文件
GET /api/projects/<id>/files/download      # 下载文件
```

## 🐛 故障排除

### 常见问题

1. **导入错误**
   - 检查Python版本（需要3.7+）
   - 安装必要依赖：`pip install flask werkzeug`

2. **文件权限错误**
   - 确保数据目录有写入权限
   - 检查项目目录权限

3. **端口占用**
   - 使用不同端口：`python run_app.py --port 8000`
   - 检查端口占用：`netstat -an | grep 5000`

4. **模块找不到**
   - 确保在项目根目录运行
   - 检查文件结构完整性

### 日志查看

系统日志保存在 `data/logs/` 目录下，可以查看详细的错误信息。

### 重置系统

删除 `data/` 目录可以重置所有数据：

```bash
rm -rf data/
```

## 📄 许可证

本项目采用MIT许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📞 支持

如果遇到问题，请：

1. 运行系统检查：`python debug/system_check.py`
2. 查看系统日志
3. 提交Issue描述问题

---

**AI任务管理系统** - 让AI任务管理更简单、更高效！
