{"projects": [{"project_id": "fd23027e-aaf1-4d2b-ac63-ce93c0d5a354", "name": "俄罗斯方块", "work_dir": "/mnt/d/agent/auto-claude-tasks/demo", "description": "", "requirement": "使用html生成一个俄罗斯方块游戏", "design": "设计一个俄罗斯方块游戏", "provider": "<PERSON><PERSON><PERSON>", "rules_constraint": "游戏规则", "task_type": "新功能", "created_at": "2025-09-25T20:29:17.275019", "updated_at": "2025-10-02T22:25:15.056561", "run_state": false, "tasks_generated": true, "task_manager_session": null}, {"project_id": "1758977931726", "name": "csdk项目C语言重构", "work_dir": "/mnt/d/aicode/csdkc/", "description": "csdk项目C语言重构", "requirement": "# 需求\n 1. 使用纯C语言重构当前项目的src目录下的所有cpp代码\n 2. 重构完成后，修改CMakeLists.txt文件，使用纯C语言的源文件目录编译，在build目录使用cmake -DWITH_TESTS=ON ..编译确保编译成功\n 3. 并运行自动化测试：cd build/tests && ./auto_CCSP 1，分析测试失败的用例，并解决\n# 核心思路\n 1. 先让代码能“以 C 的方式编译”，\n 2. 再逐步把 C++ 专属实现换成 C 实现，\n 3. 最后把 CMake 侧彻底改成 C 工程。\n\n# 增量迭代修改\n逐个修改src目录下的.cpp及相关的.h，按照按照规则要求重构为C语言实现。\n每修改完一个.cpp文件后，修改CMakefile把修改后的.c包含进来，编译通过后，在build/tests目录下，运行./auto_CCSP 1，确保所有没有“测试失败”的用例。", "design": "", "provider": "local", "rules_constraint": "## “C++ 语法”变成“C 语法”的规则\n1.文件改名\n把src/目录下的所有 .cpp/.cxx/.cc全部改成 .c，头文件如果里面只有宏/声明可以保持 .h。\n2. 去掉 extern \"C\"\n原来为了 C/C++ 混编写的全部删掉，只剩纯 C 声明。\n3.把 C++ 关键字替换掉\n - bool → C99 <stdbool.h> 的 bool\n - true/false → 同上\n - new/delete → malloc/free（或自己写的对象池） \n - 把 class/struct 混用的地方全部改成 struct；成员函数指针改成“前置声明 + 全局函数”或者“把 this 显式当第一个参数”。\n \n4. 把 C++ 标准库替换成 C 等价物，示例如下：\n- `std::string` :  直接 `char *` + 长度，或者自己写 `struct string { char *data; size_t len; }`\n- `std::vector<T>`: 宏模板“伪泛型” (utarray, utlist 等),或者 `T *arr; size_t cap; size_t len;`\n- `std::map/`unordered\\_map ： `uthash`（单头文件哈希）\n- `std::shared_ptr`: 引用计数裸指针：`struct obj { int ref; ... };`\n- `std::exception`: 返回错误码 `int/errno`<br>, `jmp_buf/longjmp` 做非局部跳转 \n- `std::thread`:`pthread.h` (`pthread_create/join`)\n- `std::mutex`: `pthread_mutex_t`\n- `iostream`: `stdio.h`\n5.把函数重载改掉：用“函数名前缀”或“带后缀”的方式\n6.把名字空间改成前缀\n7.模板/泛型，如果原来用了简单模板（如 vector<int> 、 vector<double> ）, 可直接写死两份如： int_array_t、double_array_t\n## 代码复用要求\n重构过程中，需要检查是否已经有类似的代码可以复用，如：int_array_t、struct定义等。\n## 文件重构的范围\n只重构src/目录下的cpp代码。", "task_type": "代码重构", "created_at": "2025-09-27T20:58:51.730570", "updated_at": "2025-09-29T23:48:31.544936", "run_state": false, "tasks_generated": true, "task_manager_session": null}, {"project_id": "1759193419029", "name": "禅道项目重构", "work_dir": "/mnt/d/aicode/zentaopms", "description": "", "requirement": "/mnt/d/aicode/zentaopms/javapms/zentao-java目录下是已经根据该项目的PHP重构后的文件，现在有如下问题：\n1.在zentao-java目录启动项目失败：\nmvn springboot:run\n[INFO] Scanning for projects...\nDownloading from central: https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-metadata.xml\nDownloading from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/maven-metadata.xml\n[WARNING] Could not transfer metadata org.apache.maven.plugins/maven-metadata.xml from/to central (https://repo.maven.apache.org/maven2): Transfer failed for https://repo.maven.apache.org/maven2/org/apache/maven/plugins/maven-metadata.xml\n[WARNING] Could not transfer metadata org.codehaus.mojo/maven-metadata.xml from/to central (https://repo.maven.apache.org/maven2): Transfer failed for https://repo.maven.apache.org/maven2/org/codehaus/mojo/maven-metadata.xml\n[INFO] ------------------------------------------------------------------------\n[INFO] BUILD FAILURE\n[INFO] ------------------------------------------------------------------------\n[INFO] Total time:  13.463 s\n[INFO] Finished at: 2025-10-02T21:48:11+08:00\n[INFO] ------------------------------------------------------------------------\n[ERROR] No plugin found for prefix 'springboot' in the current project and in the plugin groups [org.apache.maven.plugins, org.codehaus.mojo] available from the repositories [local (/home/<USER>/.m2/repository), central (https://repo.maven.apache.org/maven2)] -> [Help 1]\n[ERROR] \n[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.\n[ERROR] Re-run Maven using the -X switch to enable full debug logging.\n[ERROR] \n[ERROR] For more information about the errors and possible solutions, please read the following articles:\n[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/NoPluginFoundForPrefixException\n2.有很多功能的界面缺失\n请解决以上问题，确保项目可成功启动，并检查。", "design": "", "provider": "claude", "rules_constraint": "## 项目背景\n我正在进行当前项目的重构，原项目使用PHP语言，请按照Spring Boot最佳实践进行重构。\n技术栈：后端使用springboot,前端使用bootstrap5+jquery+ajax实现\n\n## 重构目标：\n- 功能对等迁移，不新增功能\n- 保持API接口兼容性\n- 采用分层架构：Controller-Service-Repository\n- 使用Spring Data JPA进行数据访问\n\n## 代码转换指导原则\n- PHP关联数组 → Java Map或DTO对象\n- PHP类属性 → Java私有字段 + Getter/Setter\n- PHP include/require → Spring依赖注入\n- PHP session → Spring Session或Redis\n- PHP mysqli → Spring Data JPA\n\n## 架构模式指导\n- 单一职责原则：每个类只负责一个明确的功能\n- 依赖倒置：通过接口抽象依赖关系\n- 使用DTO进行层间数据传输\n- 业务逻辑集中在Service层\n- 数据访问通过Repository接口\n- 控制器保持轻量，只处理HTTP相关逻辑\n\n\n## 重构后的java项目目录\n/mnt/d/aicode/zentaopms/javapms/zentao-java\n\n## 当前项目的核心架构\n- **framework/**: Core framework classes (control, model, router, helper)\n- **module/**: Modular architecture with each module containing:\n  - `control.php` - Controller logic\n  - `model.php` - Data layer and business logic\n  - `zen.php` - New architecture layer (when present)\n  - `tao.php` - Extended business logic layer (when present)\n  - `config/` - Module-specific configuration\n  - `lang/` - Internationalization files\n  - `view/` - Traditional view templates\n  - `ui/` - Modern UI components\n  - `css/` and `js/` - Frontend assets\n- **lib/**: Third-party libraries and utility classes\n- **config/**: Global configuration files\n- **www/**: Web entry point and public assets\n- **db/**: Database schemas and migration scripts\n- **extension/**: Extension system for customization", "task_type": "代码重构", "created_at": "2025-09-30T08:50:19.031700", "updated_at": "2025-10-03T00:00:44.694370", "run_state": false, "tasks_generated": true, "task_manager_session": null}, {"project_id": "1759223401732", "name": "测试项目(更新)", "work_dir": "/tmp/test_project", "description": "这是一个更新后的测试项目", "requirement": "实现一个简单的功能", "design": "", "provider": "local", "rules_constraint": "", "task_type": "新功能", "created_at": "2025-09-30T17:10:01.732320", "updated_at": "2025-09-30T17:15:25.220631", "run_state": false, "tasks_generated": true, "task_manager_session": null}, {"project_id": "1759445570260", "name": "知识库测试项目_1759445570", "work_dir": "/mnt/hgfs/ddriver/agent/auto-claude-tasks/debug/../data/test_kb_project", "description": "用于测试知识库功能的项目", "requirement": "测试知识库的创建、文档添加和检索功能", "design": "", "provider": "local", "rules_constraint": "", "task_type": "新功能", "created_at": "2025-10-03T06:52:50.264967", "updated_at": "2025-10-03T06:52:50.264967", "run_state": false, "tasks_generated": false, "task_manager_session": null}, {"project_id": "1759445606715", "name": "知识库测试项目_1759445606", "work_dir": "/mnt/hgfs/ddriver/agent/auto-claude-tasks/debug/../data/test_kb_project", "description": "用于测试知识库功能的项目", "requirement": "测试知识库的创建、文档添加和检索功能", "design": "", "provider": "local", "rules_constraint": "", "task_type": "新功能", "created_at": "2025-10-03T06:53:26.716289", "updated_at": "2025-10-03T06:53:26.716289", "run_state": false, "tasks_generated": false, "task_manager_session": null}, {"project_id": "1759445648808", "name": "知识库测试项目_1759445648", "work_dir": "/mnt/hgfs/ddriver/agent/auto-claude-tasks/debug/../data/test_kb_project", "description": "用于测试知识库功能的项目", "requirement": "测试知识库的创建、文档添加和检索功能", "design": "", "provider": "local", "rules_constraint": "", "task_type": "新功能", "created_at": "2025-10-03T06:54:08.809314", "updated_at": "2025-10-03T06:54:08.809314", "run_state": false, "tasks_generated": false, "task_manager_session": null}], "meta": {"total_projects": 7, "last_updated": "2025-10-03T06:54:08.809445"}}