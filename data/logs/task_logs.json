{"logs": [{"timestamp": "2025-09-25T21:13:53.188301", "task_id": "俄罗斯方块", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (并行模式)", "extra_data": {}}, {"timestamp": "2025-09-25T22:32:22.801025", "task_id": "俄罗斯方块", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (并行模式)", "extra_data": {}}, {"timestamp": "2025-09-25T23:22:23.124414", "task_id": "俄罗斯方块", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (并行模式)", "extra_data": {}}, {"timestamp": "2025-09-25T23:30:50.299335", "task_id": "俄罗斯方块", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (并行模式)", "extra_data": {}}, {"timestamp": "2025-09-25T23:32:01.327173", "task_id": "俄罗斯方块", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}, {"timestamp": "2025-09-25T23:33:20.922082", "task_id": "俄罗斯方块", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}, {"timestamp": "2025-09-26T06:19:32.721850", "task_id": "俄罗斯方块", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}, {"timestamp": "2025-09-27T20:13:59.078043", "task_id": "俄罗斯方块", "event_type": "log_fetch_error", "level": "ERROR", "message": "获取日志失败: slice indices must be integers or None or have an __index__ method", "extra_data": {}}, {"timestamp": "2025-09-27T20:23:34.770710", "task_id": "俄罗斯方块", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}, {"timestamp": "2025-09-27T20:23:39.695902", "task_id": "俄罗斯方块", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}, {"timestamp": "2025-09-27T21:07:53.127401", "task_id": "csdk项目C语言重构", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}, {"timestamp": "2025-09-27T21:09:30.479277", "task_id": "csdk项目C语言重构", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}, {"timestamp": "2025-09-27T21:10:28.470910", "task_id": "csdk项目C语言重构", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}, {"timestamp": "2025-09-27T21:15:30.358788", "task_id": "俄罗斯方块", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}, {"timestamp": "2025-09-27T21:19:16.663961", "task_id": "俄罗斯方块", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}, {"timestamp": "2025-09-27T21:25:27.994881", "task_id": "俄罗斯方块", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}, {"timestamp": "2025-09-27T21:37:39.639460", "task_id": "俄罗斯方块", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}, {"timestamp": "2025-09-27T21:37:50.580289", "task_id": "俄罗斯方块", "event_type": "log_fetch_error", "level": "ERROR", "message": "获取日志失败: slice indices must be integers or None or have an __index__ method", "extra_data": {}}, {"timestamp": "2025-09-27T21:39:19.417582", "task_id": "俄罗斯方块", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}, {"timestamp": "2025-09-27T21:44:06.930219", "task_id": "csdk项目C语言重构", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}, {"timestamp": "2025-09-27T21:47:30.334713", "task_id": "csdk项目C语言重构", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}, {"timestamp": "2025-09-27T21:48:59.248238", "task_id": "csdk项目C语言重构", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}, {"timestamp": "2025-09-28T07:53:29.057938", "task_id": "csdk项目C语言重构", "event_type": "log_fetch_error", "level": "ERROR", "message": "获取日志失败: slice indices must be integers or None or have an __index__ method", "extra_data": {}}, {"timestamp": "2025-09-28T08:15:10.247708", "task_id": "俄罗斯方块", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}, {"timestamp": "2025-09-28T08:18:01.159508", "task_id": "俄罗斯方块", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}, {"timestamp": "2025-09-28T08:28:48.752218", "task_id": "俄罗斯方块", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}, {"timestamp": "2025-09-28T08:30:14.681070", "task_id": "俄罗斯方块", "event_type": "execution_started", "level": "INFO", "message": "开始执行任务 (顺序模式)", "extra_data": {}}], "meta": {"created_at": "2025-09-25T20:35:09.214579", "total_logs": 27, "last_updated": "2025-09-28T08:30:14.685410"}}