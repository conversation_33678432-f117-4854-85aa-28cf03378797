#!/usr/bin/env python3
"""
AI任务管理系统启动脚本
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

from web_app import app, init_app

def main():
    parser = argparse.ArgumentParser(description='AI任务管理系统')
    parser.add_argument('--host', default='0.0.0.0', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=5005, help='服务器端口')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--data-dir', default='./data', help='数据存储目录')
    
    args = parser.parse_args()
    
    # 设置数据目录环境变量
    os.environ['AI_TASK_MANAGER_DATA_DIR'] = os.path.abspath(args.data_dir)
    
    # 确保数据目录存在
    os.makedirs(args.data_dir, exist_ok=True)
    #args.debug = True
    
    print("=" * 60)
    print("🚀 AI任务管理系统启动中...")
    print("=" * 60)
    print(f"📁 数据目录: {os.path.abspath(args.data_dir)}")
    print(f"🌐 服务地址: http://{args.host}:{args.port}")
    print(f"🔧 调试模式: {'开启' if args.debug else '关闭'}")
    print("=" * 60)
        
    # 初始化应用
    init_app()
    
    print("✅ 应用初始化完成")
    print("=" * 60)
    
    # 启动Flask应用
    app.run(
        host=args.host,
        port=args.port,
        debug=args.debug,
        threaded=True
    )        
   

if __name__ == '__main__':
    main()
