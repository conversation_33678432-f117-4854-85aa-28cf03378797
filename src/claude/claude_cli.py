#!/usr/bin/env python3
import os, pty, subprocess, select, re, time
#from types import List

class PtyCli:
    def __init__(self, work_dir, cli_cmd, prompt_regex:str, timeout:int = 30):
        """
        cli_cmd: 可执行文件路径，默认从 PATH 找
        prompt_regex: 用于识别 提示行的正则
        """
        self.work_dir = work_dir
        self.cli_cmd = cli_cmd
        self.prompt_regex = prompt_regex
        self.master_fd = None
        self.proc = None
        self.command_timeout = timeout

    def start(self):
        """启动 cli_cmd，返回 master_fd（伪终端主设备）"""
        self.master_fd, slave_fd = pty.openpty()
        # 用 os.setsid 让 cli_cmd 成为新会话 leader，这样它会认为自己在一个“真实”终端里
        self.proc = subprocess.Popen(
            self.cli_cmd,
            cwd=self.work_dir,
            stdin=slave_fd,
            stdout=slave_fd,
            stderr=slave_fd,
            text=True,
            preexec_fn=os.setsid
        )
        os.close(slave_fd)
        # 等待首次提示符出现
        out, to = self._wait_for_prompt(self.command_timeout)
        return self.master_fd, out

    def _wait_for_prompt(self, timeout):
        """读到提示符为止，用来同步"""
        return self.cmd('', self.prompt_regex, timeout=timeout)

    def cmd(self, command:str, end_prompt=None, timeout=None):
        """
        发送一条命令并读取到下一个提示符为止的输出。
        返回 (output_bytes, 是否超时)
        """
        if timeout is None:
            timeout = self.command_timeout
        if command:
            if not end_prompt:
                end_prompt = command.split('\n')[-1]
            
            if not command.endswith('\n'):
                command += '\n'
            os.write(self.master_fd, command.encode('utf-8'))

        buf = ''
        start = time.time()
        while True:
            if not self.master_fd:
                break
            # 非阻塞地读，最多 1s 检查一次超时
            try:
                ready, _, _ = select.select([self.master_fd], [], [], 1)
                if ready:
                    chunk = os.read(self.master_fd, 4096)
                    if not chunk:      # EOF
                        break
                    decoded = chunk.decode('utf-8', errors='ignore')
                    #print(f"*READ*: {decoded}")
                    buf += decoded
                    # 看到提示符即可收工
                    if end_prompt in buf:
                        break
            except Exception as e:
                print(f"Error: {e}")
                break
            
            if time.time() - start > timeout:
                return buf, True   # 超时
        return buf, False
        
    def close(self):
        """发送 quit 并等待进程退出"""
        try:
            #self.cmd('/quit\n')
            os.write(self.master_fd, '/quit\n'.encode('utf-8'))
        finally:
            if self.master_fd is not None:
                os.close(self.master_fd)
                self.master_fd = None

def compact_session(work_dir:str, session_id:str, timeout=600) -> str:
    cli = PtyCli(work_dir, ["claude", "-r",session_id, "/compact"], "Compacted" , timeout)
    #cli = PtyCli(work_dir, ["bash","/mnt/d/aicode/intelliTest/src/utils/test-cli.sh"], "? for shortcuts")
    master_fd, out = cli.start()
    #print('START OUT:', out)

    status_out, to = cli.cmd('/quit','Total cost', 10)
    #print('status ->', status_out)
    # 休眠
    #time.sleep(60)
    cli.close()
    
    # 查询~/.claude/projects/目录下该项目的会话文件
    claude_dir = os.path.expanduser("~/.claude")
    claude_proj_dir = os.path.join(claude_dir, "projects", work_dir.replace('/', '-'))
    # 找出时间最新的.jsonl文件，只获取文件名做为session_id
    session_file = max(os.listdir(claude_proj_dir), key=lambda x: os.path.getmtime(os.path.join(claude_proj_dir, x)))
    session_id = os.path.splitext(session_file)[0]

    #print(f"new_session_id: {session_id}")
    return session_id

if __name__ == '__main__':
    work_dir = "/mnt/d/aicode/intelliTest/data/code/a96ca61c-c234-49b0-8603-c8c1cd2f9771"
    new_session_id = compact_session(work_dir, "32b39c4f-2546-44d7-b5f6-dbe4f4027e98")
    print(f"new_session_id: {new_session_id}")
    