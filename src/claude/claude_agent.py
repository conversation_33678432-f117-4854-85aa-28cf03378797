import re
import os
import time
from claude_code_sdk import ClaudeSDKClient, ClaudeCodeOptions,ResultMessage,create_sdk_mcp_server,tool
from claude_code_sdk.types import Message, AssistantMessage, TextBlock,UserMessage,ThinkingBlock,ToolUseBlock,ToolResultBlock,ToolPermissionContext,PermissionResultAllow,PermissionResultDeny,StreamEvent
from typing import Dict, List, Optional, Any, Callable, Union
from loguru import logger
import json

class ClaudeAgent:
    """ClaudeCode 智能体"""
    def __init__(self, system_prompt:str, provider = "local", maxt_tokens=15000):
        self.system_prompt = system_prompt
        self.maxt_tokens = maxt_tokens
        self.stop_evnet = None
        self.provider = provider
        os.environ["API_TIMEOUT_MS"]="300000"  # 10分钟超时
        if provider == "zhipu":
            self.set_zhipu_model()
        elif provider == "local":
            self.set_local_model()
        elif provider == "claude":
            self.set_claude_model()
    
    def set_zhipu_model(self):
        os.environ["ANTHROPIC_BASE_URL"] = "https://open.bigmodel.cn/api/anthropic"
        os.environ["ANTHROPIC_AUTH_TOKEN"] = "69a9bf45e4084febb7c63c34b74c6afa.NQEUZXulrv0N17Rg"
        os.environ["ANTHROPIC_MODEL"] = ""
        os.environ["ANTHROPIC_API_KEY"] = ""
        os.environ["CLAUDE_CODE_MAX_OUTPUT_TOKENS"] = "25000"
    
    def set_claude_model(self):
        os.environ["ANTHROPIC_BASE_URL"] = "https://api.aicodemirror.com/api/claudecode"
        os.environ["ANTHROPIC_AUTH_TOKEN"] = ""
        os.environ["ANTHROPIC_MODEL"] = ""
        os.environ["ANTHROPIC_API_KEY"] = "sk-ant-api03-ltc4tIgTW6CjHSJ-MSl5lSjYVfHYOyy-r219vjK-n4UqMkq_iJRclyXdNGBN0ySciQpzGQXB5Ul6wTGnTBu15Q"
        os.environ["CLAUDE_CODE_MAX_OUTPUT_TOKENS"] = "25000"
        
    def set_local_model(self):
        os.environ["ANTHROPIC_BASE_URL"] = "https://ai.secsign.online:3006/"
        os.environ["ANTHROPIC_BASE_URL"] = "http://***********:8108/"
        os.environ["ANTHROPIC_BASE_URL"] = "http://127.0.0.1:8100/"
        os.environ["ANTHROPIC_AUTH_TOKEN"] = "sk-NO_KEY"
        os.environ["ANTHROPIC_MODEL"] = "glm-4.5-air"
        os.environ["ANTHROPIC_API_KEY"] = ""
        os.environ["CLAUDE_CODE_MAX_OUTPUT_TOKENS"] = "15000"
        
    async def run_agent(self, work_dir , user_req, session_id = None, nothink="", progress_callback=None
    ) -> Dict[str, Any]:
        while True:
            result = await self._run_agent(work_dir, user_req, session_id, nothink, progress_callback)
            if result.get("success"):
                return result
            if self.stop_evnet:
                return {"success": False, "message": "任务已取消"}
            
            logger.error(f"{self.provider}-任务执行出错: {result.get('message')}")
            session_id = result.get("session_id")
            if self.provider == "claude":
                # claude模型没有额度后转智谱
                self.provider = "zhipu"
                self.set_zhipu_model()
            elif self.provider == "zhipu":
                # 达到5小时120次的阈值，可睡眠30分钟后继续
                time.sleep(10 * 60)
                # self.provider = "claude"
                # self.set_claude_model()
            else:
                # 内网模型可能是请求上下文超限
                break
    def stop(self):
        self.stop_evnet = True
    async def _run_agent(self, work_dir , user_req, session_id = None, nothink="", progress_callback=None
    ) -> Dict[str, Any]:
        if session_id and session_id.strip().lower() == "none":
            session_id = None
        
        def claude_progress(progress: int, title:str, message: str=''):
            #print(f"进度: {progress}% {title}:{message}")
            if progress_callback:
                progress_callback(progress, title, message)
        
        def print_message_content(message_count, title, content: list[Any], claude_progress):
            try:
                for block in content:
                    if isinstance(block, TextBlock):
                        claude_progress(message_count,title, f"💬 {block.text}")
                    elif isinstance(block, ThinkingBlock):
                        claude_progress(message_count,title, f"🤔 {block.thinking}")
                    elif isinstance(block, ToolUseBlock):
                        input_str = f"{block.input}" #⏎ ¶
                        claude_progress(message_count,title, f"🔧 {block.name}, {input_str}")
                    elif isinstance(block, ToolResultBlock):
                        tool_rst = f"{block.content}"
                        claude_progress(message_count,title, f"📊 {tool_rst}")
            except Exception as e:
                print(f"ERROR: {e}")
            
        new_session_id = session_id
        
        result = {
            "is_error":False
        }
        message_count = 0
        options = self._create_options(work_dir, session_id)
        # 在对话中跟踪使用情况
        async def track_usage(message):
            if message['type'] == 'assistant' and 'usage' in message:
                print(f"消息 ID: {message['id']}")
                print(f"使用情况: {message['usage']}")
        
        async with ClaudeSDKClient(options) as client:
            claude_progress(0,"Request", f"📝 Sending query:{user_req}")
            await client.query(f"{user_req} {nothink}")
            async for message in client.receive_response():
                if self.stop_evnet:
                    break
                message_count += 1
                if isinstance(message, AssistantMessage):
                    # Print Claude's text responses
                    print_message_content(message_count,"Assistant", message.content, claude_progress)
                elif isinstance(message, UserMessage):
                    print_message_content(message_count,"User", message.content, claude_progress)
                elif isinstance(message, StreamEvent):
                    # Print Claude's text responses
                    for event in message.event:
                        claude_progress(message_count, "Stream",  f"🌊 {event}")
                elif isinstance(message, ResultMessage):
                    claude_progress(100, "Result", f"✅ Task completed! Duration: {message.duration_ms/1000} seconds.")
                    result["is_error"] = message.is_error
                    result["message"] = f"{message.result}"
                    new_session_id = message.session_id
        
        result["success"] = not result["is_error"]
        result["session_id"] = new_session_id
        return result
    
    def _create_options(self, work_dir, session_id):
        async def file_permission_handler(
            tool_name: str,
            input_data: dict,
            context: ToolPermissionContext
        ) -> PermissionResultAllow | PermissionResultDeny:
                """工具权限的自定义逻辑。"""
                #print(f"{tool_name} 权限请求")

                # 阻止读写工作目录之外的文件
                if tool_name in ["Write","Read","mcp__read__read_file"]:
                    file_path = input_data.get("file_path", "")
                    real_path = os.path.abspath(file_path)
                    #real_dir = os.path.dirname(real_path)
                    if not real_path.startswith(work_dir):
                        print(f"⚠️ {tool_name} 不能读写文件: {real_path}")
                        return PermissionResultDeny(
                            behavior="deny",
                            message=f"只能读写工作目录: {work_dir}.",
                            interrupt=True
                        )
                # 允许其他所有操作
                # 返回的Allo，依然错误：Tool permission request failed: [↵   {↵     "code": "invalid_union",
                return PermissionResultAllow()
        
        mcp_read_file = create_sdk_mcp_server(
            name="read_file",
            version="2.0.0",
            tools=[
                read_file
            ],
        )
        
        options=ClaudeCodeOptions(
                system_prompt = self.system_prompt,
                resume = session_id,
                #max_turns=200,
                cwd=work_dir,
                permission_mode="acceptEdits" if self.provider == "local" else "bypassPermissions",
                #mcp_servers=self.mcp_servers,
                mcp_servers={"read": mcp_read_file},
                allowed_tools=["Task","Write","LS","Bash","BashOutput","KillShell","Edit","TodoWrite","NotebookEdit","MultiEdit","Grep","Glob","mcp__read__read_file"]  if self.provider == "local" else [],
                disallowed_tools=["ExitPlanMode","Read","WebFetch","WebSearch"] if self.provider == "local" else [],
                #settings=os.path.join(work_dir, ".claude/settings.local.json"),
                extra_args={
                    #"verbose": None,
                    #"output-format": "stream-json",
                    #"mcp-debug": None,
                    #"dangerously-skip-permissions": None
                },
                can_use_tool = file_permission_handler,
                # hooks={
                #     'PreToolUse': [
                #         #HookMatcher(matcher='Bash', hooks=[validate_file_path_command]),
                #         HookMatcher(hooks=[validate_file_path_command]),  # 适用于所有工具
                #     ]
                # }
        )
        return options

@tool("read_file", f"""读取单个文件内容的工具.
当您需要查看文件内容时，请使用此工具。它会返回文件的文本内容以及相关的元信息。
返回值说明:
      Dict[str, Any]: 包含以下键值的字典
        - total_lines (int): 文件的总行数
        - start_line (int): 本次读取的起始行号（与传入的offset相同）
        - end_line (int): 本次实际读取的结束行号
        - content (List[str]): 实际读取到的行内容列表
""",
     {
        "type": "object",
        "properties": {
            "file_path": {
              "type": "string",
              "description": "单个文件的完整路径"
            },
            "offset": {
              "type": "integer",
              "description": "从第几行开始读取，默认为1"
            },
            "maxline": {
              "type": "integer",
              "description": "最多读取多少行，默认1000,最大不能超过1000行"
            },
            "encode": {
              "type": "string",
              "description": "文件编码格式，默认为utf-8"
            }
        },
        "required": ["file_path"]
     }
    )
async def read_file(args:dict[str,Any]) -> dict[str, Any]:
    """
    读取文件内容的工具
    """
    try:
        file_path=args.get("file_path")
        # 检查文件路径是否有空格 逗号
        if " " in file_path or "," in file_path:
            raise ValueError("只能读取一个文件")
        
        offset: int = args.get("offset", 1)
        maxline:int = args.get("maxline", 1000)
        if maxline > 1000:
            raise ValueError("maxline 不能超过 1000")
        encode:str=args.get("encode", "utf-8")
        if os.path.isdir(file_path):
            raise ValueError(f"{file_path} 是目录名")
        if not os.path.isfile(file_path):
            raise ValueError(f"文件不存在: {file_path}")
        
        with open(file_path, "r", encoding=encode) as f:
            lines = f.readlines()

        total_lines = len(lines)
        
        # 限制读取内容在10KB以内
        MAX_BYTES = 10 * 1024  # 20KB
        accumulated_bytes = 0
        actual_end_line = offset - 1
        
        start = offset - 1
        for i in range(start, min(start + maxline, total_lines)):
            line_bytes = len(lines[i].encode('utf-8'))
            if accumulated_bytes + line_bytes > MAX_BYTES:
                break
            accumulated_bytes += line_bytes
            actual_end_line = i + 1  # 转换为1基索引
        
        out_lines = lines[start:actual_end_line]

        result = {
            "total_lines": total_lines,
            "start_line": offset,
            "end_line": actual_end_line,
            "content": [line for line in out_lines]
        }
        return {
            "content": [{"type": "text", "text": f"{result}"}]
        }
    except Exception as e:
        return {
            "content": [{"type": "text", "text": f"文件读取失败：{e}"}]
        }
# async def validate_file_path_hook(
#     input_data: dict[str, Any],
#     tool_use_id: str | None,
#     context: HookContext
# ) -> HookJSONOutput:
#     """验证并阻止危险的 bash 命令。"""
#     if input_data['tool_name'] == 'Read' or input_data['tool_name'] == 'Write' or input_data['tool_name'] == 'mcp__read__read_file':
#
#         file_path = input_data.get("file_path", "")
#         if file_path.startswith("/system/"):
#             return {
#                 'hookSpecificOutput': {
#                     'hookEventName': 'PreToolUse',
#                     'permissionDecision': 'deny',
#                     'permissionDecisionReason': '危险命令已阻止'
#                 }
#             }
#     return {}