#!/usr/bin/env python3
"""
AI任务管理系统Web界面
"""

import os
import json
import threading
import logging
from datetime import datetime
from flask import Flask, request, jsonify, redirect, url_for, flash, send_file, send_from_directory
from werkzeug.utils import secure_filename

try:
    from .project_manager import ProjectManager
    from .task_manager import TaskManager
    from .log_manager import TaskLogManager
    from .file_manager import FileManager
    from .knowledge_manager import KnowledgeManager
except ImportError:
    from project_manager import ProjectManager
    from task_manager import TaskManager
    from log_manager import TaskLogManager
    from file_manager import FileManager
    from knowledge_manager import KnowledgeManager

# 获取当前文件所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 构建模板和静态文件目录路径
template_dir = os.path.join(current_dir, '..', 'templates')
static_dir = os.path.join(current_dir, '..', 'static')

# 实例化Flask应用并指定模板和静态文件目录
app = Flask(__name__, 
            template_folder=template_dir,
            static_folder=static_dir)
app.secret_key = 'ai-task-manager-secret-key-2025'

# 全局变量
project_manager = None
knowledge_manager = None
running_tasks = {}  # 存储正在运行的任务
task_threads = {}   # 存储任务线程

# 静态文件路由 - 提供HTML页面
@app.route('/')
def index():
    """首页"""
    return app.send_static_file('index.html')

@app.route('/index.html')
def index_html():
    """首页HTML"""
    return app.send_static_file('index.html')

@app.route('/projects.html')
def projects_html():
    """项目列表HTML"""
    return app.send_static_file('projects.html')

@app.route('/project_tasks.html')
def project_tasks_html():
    """项目任务HTML"""
    return app.send_static_file('project_tasks.html')

@app.route('/project_files.html')
def project_files_html():
    """项目文件HTML"""
    return app.send_static_file('project_files.html')

@app.route('/task_llm_logs.html')
def task_llm_logs_html():
    """任务LLM日志HTML"""
    return app.send_static_file('task_llm_logs.html')

@app.route('/requirement_manager.html')
def requirement_manager_html():
    """需求管理HTML"""
    return app.send_static_file('requirement_manager.html')

@app.route('/design_manager.html')
def design_manager_html():
    """设计管理HTML"""
    return app.send_static_file('design_manager.html')

@app.route('/rules_manager.html')
def rules_manager_html():
    """规则管理HTML"""
    return app.send_static_file('rules_manager.html')

@app.route('/knowledge_manager.html')
def knowledge_manager_html():
    """知识库管理HTML"""
    return app.send_static_file('knowledge_manager.html')

@app.route('/knowledge_detail.html')
def knowledge_detail_html():
    """知识库详情HTML"""
    return app.send_static_file('knowledge_detail.html')

@app.route('/debug/simple_test.html')
def simple_test_html():
    """简单测试HTML"""
    return send_from_directory('../debug', 'simple_test.html')

@app.route('/debug/test_container_display.html')
def test_container_display_html():
    """容器显示测试HTML"""
    return send_from_directory('../debug', 'test_container_display.html')

@app.route('/projects.html')
def projects_simple_html():
    """简化项目管理HTML"""
    return app.send_static_file('projects.html')

@app.route('/project_tasks.html')
def project_tasks_simple_html():
    """简化任务管理HTML"""
    return app.send_static_file('project_tasks.html')

def init_app(data_dir="data"):
    """初始化应用"""
    global project_manager, knowledge_manager
    project_manager = ProjectManager(data_dir)
    knowledge_manager = KnowledgeManager(data_dir)

    # 创建模板目录
    template_dir = os.path.join(os.path.dirname(__file__), '..', 'templates')
    static_dir = os.path.join(os.path.dirname(__file__), '..', 'static')
    os.makedirs(template_dir, exist_ok=True)
    os.makedirs(static_dir, exist_ok=True)



# API路由
@app.route('/api/projects', methods=['GET', 'POST'])
def api_projects():
    """项目API"""
    if request.method == 'GET':
        projects = project_manager.list_projects()
        return jsonify([project.to_dict() for project in projects])
    
    elif request.method == 'POST':
        data = request.get_json()
        try:
            # 验证必填字段
            name = data.get('name', '').strip()
            work_dir = data.get('work_dir', '').strip()
            
            if not name or not work_dir:
                return jsonify({'success': False, 'message': '项目名称和工作目录不能为空'}), 400
            
            project_id = project_manager.create_project(
                name=name,
                work_dir=work_dir,
                description=data.get('description', ''),
                requirement=data.get('requirement', ''),
                provider=data.get('provider', 'local'),
                task_type=data.get('task_type', '新功能'),
                rules_constraint=data.get('rules_constraint', ''),
                design=data.get('design', '')
            )
            return jsonify({'success': True, 'project_id': project_id})
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)}), 400

@app.route('/api/projects/<project_id>', methods=['GET', 'PUT', 'DELETE'])
def api_project(project_id):
    """单个项目API"""
    if request.method == 'GET':
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({'error': '项目不存在'}), 404
        task_manager = project_manager.get_task_manager(project_id)
        if task_manager:
            project.run_state = task_manager.get_run_state()
        
        return jsonify(project.to_dict())

    elif request.method == 'PUT':
        data = request.get_json()
        success = project_manager.update_project(
            project_id,
            name=data.get('name'),
            description=data.get('description'),
            work_dir=data.get('work_dir'),
            requirement=data.get('requirement'),
            provider=data.get('provider'),
            rules_constraint=data.get('rules_constraint'),
            task_type=data.get('task_type'),
            design=data.get('design')
        )
        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'message': '项目不存在'}), 404
    
    elif request.method == 'DELETE':
        success = project_manager.delete_project(project_id, delete_files=False)
        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'message': '项目不存在'}), 404

@app.route('/api/projects/<project_id>/summary', methods=['GET'])
def api_project_summary(project_id):
    """获取项目摘要信息API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    try:
        summary = project_manager.get_project_summary(project_id)
        return jsonify(summary)
    except Exception as e:
        return jsonify({'error': f'获取项目摘要失败: {e}'}), 500

@app.route('/api/projects/<project_id>/generate_tasks', methods=['POST'])
def api_generate_project_tasks(project_id):
    """为项目生成任务"""
    try:
        data = request.get_json() or {}
        num_tasks = data.get('num_tasks')

        result = project_manager.generate_tasks_for_project(project_id, num_tasks)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/projects/<project_id>/run_tasks', methods=['POST'])
def api_run_project_tasks(project_id):
    """运行项目任务"""
    try:
        data = request.get_json() or {}
        parallel_mode = data.get('parallel_mode', False)

        result = project_manager.run_project_tasks(project_id, parallel_mode)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# 新增需求管理API
@app.route('/api/projects/<project_id>/requirement', methods=['GET', 'PUT'])
def api_project_requirement(project_id):
    """项目需求管理API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    if request.method == 'GET':
        return jsonify({'requirement': project.requirement})

    elif request.method == 'PUT':
        data = request.get_json()
        requirement = data.get('requirement', '')
        success = project_manager.update_project(project_id, requirement=requirement)
        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'message': '更新失败'}), 500

# 新增设计管理API
@app.route('/api/projects/<project_id>/design', methods=['GET', 'PUT'])
def api_project_design(project_id):
    """项目设计管理API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    if request.method == 'GET':
        return jsonify({'design': project.design})

    elif request.method == 'PUT':
        data = request.get_json()
        design = data.get('design', '')
        success = project_manager.update_project(project_id, design=design)
        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'message': '更新失败'}), 500

# 新增规则管理API
@app.route('/api/projects/<project_id>/rules', methods=['GET', 'PUT'])
def api_project_rules(project_id):
    """项目规则管理API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    if request.method == 'GET':
        return jsonify({'rules_constraint': project.rules_constraint})

    elif request.method == 'PUT':
        data = request.get_json()
        rules_constraint = data.get('rules_constraint', '')
        success = project_manager.update_project(project_id, rules_constraint=rules_constraint)
        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'message': '更新失败'}), 500

# 删除了project_tasks和project_files路由

@app.route('/api/projects/<project_id>/tasks', methods=['GET'])
def api_project_tasks(project_id):
    """获取项目任务列表API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    # 获取任务信息
    tasks = []
    task_manager = project_manager.get_task_manager(project_id)
    if task_manager:
        try:
            tasks = task_manager.list_tasks()
        except Exception as e:
            return jsonify({'error': f'加载任务失败: {e}'}), 500

    return jsonify({'tasks': tasks})

@app.route('/api/projects/<project_id>/tasks/<task_id>', methods=['PUT', 'DELETE'])
def api_project_task(project_id, task_id):
    """项目任务编辑和删除API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    task_manager = project_manager.get_task_manager(project_id)
    if not task_manager:
        return jsonify({'error': '无法获取TaskManager'}), 404

    if request.method == 'PUT':
        # 编辑任务 - 使用TaskManager的update_task方法
        data = request.get_json()
        try:
            # 调用ProjectManager的update_project_task方法，它会调用TaskManager的update_task方法
            result = project_manager.update_project_task(
                project_id,
                task_id,
                title=data.get('title'),
                description=data.get('description', ''),
                priority=data.get('priority', 'medium'),
                dependencies=data.get('dependencies', []),
                details=data.get('details', ''),
                testStrategy=data.get('testStrategy', '')
            )
            
            if result.get('success'):
                return jsonify({'success': True, 'message': '任务更新成功'})
            else:
                return jsonify({'success': False, 'message': result.get('message', '任务更新失败')}), 400

        except Exception as e:
            return jsonify({'error': f'更新任务失败: {e}'}), 500

    elif request.method == 'DELETE':
        # 删除任务
        try:
            # 加载现有任务
            if not os.path.exists(task_manager.task_file_name):
                return jsonify({'error': '任务文件不存在'}), 404

            with open(task_manager.task_file_name, 'r', encoding='utf-8') as f:
                tasks_data = json.load(f)

            tasks = tasks_data.get("tasks", [])

            # 查找要删除的任务
            task_to_delete = None
            for task in tasks:
                if task.get('task_id') == task_id:
                    task_to_delete = task
                    break

            if not task_to_delete:
                return jsonify({'error': '任务不存在'}), 404

            # 检查是否有其他任务依赖此任务
            dependent_tasks = []
            for task in tasks:
                if task.get('dependencies') and task_id in task['dependencies']:
                    dependent_tasks.append(task['title'])

            if dependent_tasks:
                return jsonify({
                    'error': f'无法删除任务，以下任务依赖此任务: {", ".join(dependent_tasks)}'
                }), 400

            # 删除任务
            tasks.remove(task_to_delete)

            # 保存更新后的任务
            with open(task_manager.task_file_name, 'w', encoding='utf-8') as f:
                json.dump(tasks_data, f, indent=2, ensure_ascii=False)

            return jsonify({'success': True})

        except Exception as e:
            return jsonify({'error': f'删除任务失败: {e}'}), 500

@app.route('/api/projects/<project_id>/tasks/<task_id>/run', methods=['POST'])
def api_run_single_task(project_id, task_id):
    """运行单个任务API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    try:
        result = project_manager.run_single_task(project_id, task_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': f'运行任务失败: {e}'}), 500

@app.route('/api/projects/<project_id>/tasks/<task_id>/logs', methods=['GET'])
def api_get_task_logs(project_id, task_id):
    """获取任务日志API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    try:
        logs = project_manager.get_task_logs(project_id, task_id)
        return jsonify({'logs': logs})
    except Exception as e:
        return jsonify({'error': f'获取日志失败: {e}'}), 500

@app.route('/api/projects/<project_id>/tasks/<task_id>/llm-logs', methods=['GET'])
def api_get_task_llm_logs(project_id, task_id):
    """获取任务LLM交互日志API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    try:
        from src.log_manager import get_task_llm_logs
        limit = None # request.args.get('limit', 100, type=int)
        since_timestamp = request.args.get('since')  # 获取指定时间戳之后的日志

        logs = get_task_llm_logs(project.name, task_id, limit)

        # 如果指定了since_timestamp，只返回该时间戳之后的日志
        if since_timestamp:
            filtered_logs = []
            for log in logs:
                if log['timestamp'] > since_timestamp:
                    filtered_logs.append(log)
            logs = filtered_logs

        return jsonify({
            'success': True,
            'logs': logs,
            'total_count': len(logs)
        })
    except Exception as e:
        logging.error(f"获取LLM日志失败: {e}")
        return jsonify({'error': f'获取LLM日志失败: {e}'}), 500
        
@app.route('/api/projects/<project_id>/tasks/<task_id>/continue', methods=['POST'])
def api_continue_task(project_id, task_id:int):
    """继续任务API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    # 获取请求内容
    data = request.get_json()
    request_content = data.get('request', '')
    
    if not request_content:
        return jsonify({'success': False, 'message': '请求内容不能为空'}), 400

    try:
        # 调用project_manager的continue_task方法
        result = project_manager.continue_task(project_id, int(task_id), request_content)
        return jsonify(result)
    except Exception as e:
        logging.error(f"继续任务失败: {e}")
        return jsonify({'success': False, 'message': f'继续任务失败: {e}'}), 500

@app.route('/api/projects/<project_id>/tasks', methods=['POST'])
def api_add_task(project_id):
    """添加新任务API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    data = request.get_json()
    try:
        result = project_manager.add_task_to_project(
            project_id,
            title=data.get('title'),
            description=data.get('description', ''),
            priority=data.get('priority', 'medium'),
            dependencies=data.get('dependencies', [])
        )

        # 如果需要立即执行
        if result.get('success') and data.get('execute_immediately'):
            task_id = result['task_id']
            run_result = project_manager.run_single_task(project_id, task_id)
            result['execution_result'] = run_result

        return jsonify(result)
    except Exception as e:
        return jsonify({'error': f'添加任务失败: {e}'}), 500

@app.route('/api/projects/<project_id>/tasks/<task_id>', methods=['PUT'])
def api_update_task(project_id, task_id):
    """更新任务API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    data = request.get_json()
    try:
        result = project_manager.update_project_task(
            project_id,
            task_id,
            title=data.get('title'),
            description=data.get('description', ''),
            priority=data.get('priority', 'medium'),
            dependencies=data.get('dependencies', []),
            details=data.get('details', ''),
            testStrategy=data.get('testStrategy', '')
        )

        return jsonify(result)
    except Exception as e:
        return jsonify({'error': f'更新任务失败: {e}'}), 500

@app.route('/api/projects/<project_id>/tasks/reset', methods=['POST'])
def api_reset_project_tasks(project_id):
    """重置项目所有任务API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    try:
        result = project_manager.reset_project_tasks(project_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': f'重置任务失败: {e}'}), 500

@app.route('/api/projects/<project_id>/stop_execution', methods=['POST'])
def api_stop_project_execution(project_id):
    """停止项目执行API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    try:
        result = project_manager.stop_project_execution(project_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': f'停止执行失败: {e}'}), 500

@app.route('/api/projects/<project_id>/tasks/<task_id>', methods=['DELETE'])
def api_delete_task(project_id, task_id):
    """删除任务API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    try:
        result = project_manager.delete_task(project_id, task_id)
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': f'删除任务失败: {e}'}), 500

@app.route('/api/projects/<project_id>/files', methods=['GET'])
def api_get_project_files(project_id):
    """获取项目文件树API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    try:
        work_dir = project.work_dir
        if not os.path.exists(work_dir):
            return jsonify({'files': []})

        def build_file_tree(path, base_path):
            """构建文件树"""
            items = []
            try:
                for item in sorted(os.listdir(path)):
                    if item.startswith('.'):
                        continue

                    item_path = os.path.join(path, item)
                    relative_path = os.path.relpath(item_path, base_path)

                    if os.path.isdir(item_path):
                        items.append({
                            'name': item,
                            'type': 'directory',
                            'path': relative_path,
                            'children': build_file_tree(item_path, base_path)
                        })
                    else:
                        items.append({
                            'name': item,
                            'type': 'file',
                            'path': relative_path,
                            'size': os.path.getsize(item_path)
                        })
            except PermissionError:
                pass

            return items

        files = build_file_tree(work_dir, work_dir)
        return jsonify({'files': files})

    except Exception as e:
        return jsonify({'error': f'获取文件列表失败: {e}'}), 500

@app.route('/api/projects/<project_id>/files/content', methods=['GET'])
def api_get_file_content(project_id):
    """获取文件内容API"""
    project = project_manager.get_project(project_id)
    if not project:
        return jsonify({'error': '项目不存在'}), 404

    file_path = request.args.get('path')
    if not file_path:
        return jsonify({'error': '文件路径不能为空'}), 400

    try:
        full_path = os.path.join(project.work_dir, file_path)

        # 安全检查：确保文件在项目目录内
        if not os.path.abspath(full_path).startswith(os.path.abspath(project.work_dir)):
            return jsonify({'error': '无效的文件路径'}), 400

        if not os.path.exists(full_path) or not os.path.isfile(full_path):
            return jsonify({'error': '文件不存在'}), 404

        # 检查文件大小（限制为1MB）
        if os.path.getsize(full_path) > 1024 * 1024:
            return jsonify({'error': '文件过大，无法预览'}), 400

        # 尝试读取文件内容
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            encoding = 'utf-8'
        except UnicodeDecodeError:
            try:
                with open(full_path, 'r', encoding='gbk') as f:
                    content = f.read()
                encoding = 'gbk'
            except UnicodeDecodeError:
                return jsonify({'error': '无法解码文件内容'}), 400

        # 获取文件扩展名
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()

        return jsonify({
            'content': content,
            'encoding': encoding,
            'extension': ext,
            'size': os.path.getsize(full_path),
            'path': file_path
        })

    except Exception as e:
        return jsonify({'error': f'读取文件失败: {e}'}), 500

# 任务管理API
@app.route('/api/tasks', methods=['POST'])
def api_create_task():
    """创建任务API"""
    data = request.get_json()
    try:
        req_id = data['req_id']
        task_manager = project_manager.get_task_manager(req_id)
        if not task_manager:
            return jsonify({'success': False, 'message': '无法获取TaskManager'}), 404

        # 创建任务
        task_id = task_manager.add_task(
            title=data['title'],
            description=data['description'],
            details=data.get('details', ''),
            priority=data.get('priority', 'medium'),
            dependencies=data.get('dependencies', []),
            test_strategy=data.get('testStrategy', '')
        )

        return jsonify({'success': True, 'task_id': task_id})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 400

@app.route('/api/tasks/<int:task_id>', methods=['GET', 'PUT', 'DELETE'])
def api_task_detail(task_id):
    """任务详情API"""
    # 这里需要从请求中获取req_id，或者从任务ID反向查找
    # 暂时返回错误，需要完善TaskManager的接口
    return jsonify({'success': False, 'message': '功能正在开发中'}), 501

# 文件管理API
@app.route('/api/projects/<project_id>/files')
def api_list_files(project_id):
    """获取项目文件列表"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({'success': False, 'message': '项目不存在'}), 404

        path = request.args.get('path', '')

        # 检查项目目录是否存在
        if not os.path.exists(project.directory):
            return jsonify({'success': False, 'message': f'项目目录不存在: {project.directory}'}), 404

        file_manager = FileManager(project.directory)
        files = file_manager.list_files(path)

        return jsonify({
            'success': True,
            'files': [file.to_dict() for file in files]
        })

    except Exception as e:
        logging.error(f"获取文件列表失败: {e}")
        return jsonify({'success': False, 'message': f'获取文件列表失败: {str(e)}'}), 500

@app.route('/api/projects/<project_id>/files/preview')
def api_preview_file(project_id):
    """预览文件内容"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({'success': False, 'message': '项目不存在'})

        path = request.args.get('path', '')
        if not path:
            return jsonify({'success': False, 'message': '文件路径不能为空'})

        file_manager = FileManager(project.directory)
        result = file_manager.get_file_content(path)

        if result is None:
            return jsonify({'success': False, 'message': '文件不存在或无法读取'})

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/projects/<project_id>/files/download')
def api_download_file(project_id):
    """下载文件"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({'success': False, 'message': '项目不存在'})

        path = request.args.get('path', '')
        if not path:
            return jsonify({'success': False, 'message': '文件路径不能为空'})

        file_manager = FileManager(project.directory)
        file_path = file_manager.get_file_path(path)

        if not file_path:
            return jsonify({'success': False, 'message': '文件不存在'})

        filename = os.path.basename(path)
        return send_file(file_path, as_attachment=True, download_name=filename)

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/projects/<project_id>/files/stats')
def api_file_stats(project_id):
    """获取项目文件统计"""
    try:
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({'success': False, 'message': '项目不存在'})

        file_manager = FileManager(project.directory)
        stats = file_manager.get_project_stats()

        return jsonify({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})


# 知识库管理API
@app.route('/api/projects/<project_id>/knowledge_bases', methods=['GET'])
def api_knowledge_bases(project_id):
    """知识库API"""
    # 获取项目的知识库（自动创建默认知识库）
    try:
        # 验证项目是否存在
        project = project_manager.get_project(project_id)
        if not project:
            return jsonify({'success': False, 'message': '项目不存在'})

        # 获取或创建项目的默认知识库
        kb = knowledge_manager.get_or_create_project_knowledge_base(project_id, project.name)

        return jsonify({
            'success': True,
            'knowledge_base': kb.to_dict()
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})




@app.route('/api/knowledge_bases/<kb_id>', methods=['GET', 'DELETE'])
def api_knowledge_base_detail(kb_id):
    """知识库详情API"""
    if request.method == 'GET':
        # 获取知识库详情
        try:
            kb = knowledge_manager.get_knowledge_base(kb_id)
            if not kb:
                return jsonify({'success': False, 'message': '知识库不存在'})

            return jsonify({
                'success': True,
                'knowledge_base': kb.to_dict()
            })
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)})

    elif request.method == 'DELETE':
        # 删除知识库
        try:
            success = knowledge_manager.delete_knowledge_base(kb_id)
            if success:
                return jsonify({
                    'success': True,
                    'message': '知识库删除成功'
                })
            else:
                return jsonify({'success': False, 'message': '知识库删除失败'})
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)})


@app.route('/api/knowledge_bases/<kb_id>/documents', methods=['POST'])
def api_add_document(kb_id):
    """添加文档到知识库"""
    try:
        data = request.get_json()
        title = data.get('title', '').strip()
        content = data.get('content', '').strip()
        metadata = data.get('metadata', {})

        if not title or not content:
            return jsonify({'success': False, 'message': '标题和内容不能为空'})

        doc_id = knowledge_manager.add_document(kb_id, title, content, metadata)
        return jsonify({
            'success': True,
            'doc_id': doc_id,
            'message': '文档添加成功'
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})


@app.route('/api/knowledge_bases/<kb_id>/search', methods=['POST'])
def api_search_documents(kb_id):
    """在知识库中搜索文档"""
    try:
        data = request.get_json()
        query = data.get('query', '').strip()
        limit = data.get('limit', 10)

        if not query:
            return jsonify({'success': False, 'message': '搜索查询不能为空'})

        documents = knowledge_manager.search_documents(kb_id, query, limit)
        return jsonify({
            'success': True,
            'documents': documents,
            'total': len(documents)
        })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})


if __name__ == '__main__':
    init_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
