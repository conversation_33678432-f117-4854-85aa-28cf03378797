"""
任务管理器模块
实现任务的创建、管理、执行和监控功能
"""

import os
import json
import uuid
import shutil
import threading
import time
import asyncio
import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from log_manager import LLMLogManager

try:
    from .claude.claude_agent import ClaudeAgent
except ImportError:
    from claude.claude_agent import ClaudeAgent

class Task:
    """任务类"""
    def __init__(self, id: int, title: str, description: str,
                 priority: str = "medium", dependencies: List[int] = None,
                 status: str = "pending", details: str = "", testStrategy: str = ""):
        self.id = id  # 使用id而不是task_id，与demo/.taskai/task.json保持一致
        self.title = title
        self.description = description
        self.priority = priority
        self.dependencies = dependencies or []
        self.status = status  # pending, running, completed, failed
        self.details = details  # 任务详细信息
        self.testStrategy = testStrategy  # 测试策略
        self.created_at = datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()
        self.result = ""
        self.execution_time = None  # 添加执行时间字段
        self.session_id = None
        # 该任务的后续请求
        self.next_requests = []
        
    def to_dict(self) -> Dict[str, Any]:
        data = {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'priority': self.priority,
            'dependencies': self.dependencies,
            'status': self.status,
            'details': self.details,
            'testStrategy': self.testStrategy,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'result': self.result,
            'session_id': self.session_id,
            "next_requests": self.next_requests
        }
        # 只有当执行时间存在时才添加到字典中
        if self.execution_time is not None:
            data['execution_time'] = self.execution_time
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Task':
        # 兼容两种格式：新格式使用id，旧格式使用task_id
        id = data.get('id')
        task = cls(
            id=id,
            title=data['title'],
            description=data['description'],
            priority=data.get('priority', 'medium'),
            dependencies=data.get('dependencies', []),
            status=data.get('status', 'pending'),
            details=data.get('details', ''),
            testStrategy=data.get('testStrategy', '')
        )
        task.created_at = data.get('created_at', task.created_at)
        task.updated_at = data.get('updated_at', task.updated_at)
        task.result = data.get('result', '')
        task.session_id = data.get('session_id')
        task.execution_time = data.get('execution_time')  # 从数据中获取执行时间
        task.next_requests = data.get('next_requests', [])
        return task


class TaskManager:
    """任务管理器"""
    
    def __init__(self, project_name: str, work_dir:str, rules_constraint:str = None, log_manager=None, provider: str = "local", task_type: str = "其他"):
        self.project_name = project_name
        self.log_manager = log_manager
        self.provider = provider
        self.task_type = task_type  # 任务类型：代码重构、PMO需求、新功能、代码分析、其他
        self.tasks: Dict[int, Task] = {}
        self.meta = {}
        self.stop_event = threading.Event()
        self.max_workers = 3  # 并发任务数
        
        # 设置任务文件路径
        self.work_dir = work_dir
        self.task_file_name = os.path.join(self.work_dir, ".taskai/task.json")
        self.load_tasks()
        
        # 初始化Claude配置
        self._setup_claude_config(rules_constraint)
        self.is_running = False
        self.curr_agent = None
        
    def get_system_prompt(self) -> str:
        """
        根据不同的任务类型返回不同的系统提示词
        
        Returns:
            str: 系统提示词
        """
        prompt_templates = {
            "代码重构": f"""你是一位专业的软件架构师和代码重构专家，正在为项目"{self.work_dir}"进行代码重构工作。""",

            "PMO需求": f"""你是一位经验丰富的项目管理专家，正在为项目"{self.work_dir}"处理PMO（用户提出的项目需求）相关需求。""",

            "新功能": f"""你是一位资深的产品开发专家，正在为项目"{self.work_dir}"设计和开发新功能。""",

            "代码分析": f"""你是一位资深的代码审查专家和技术分析师，正在对项目"{self.work_dir}"进行代码分析。""",

            "其他": f"""你是一位全栈开发专家，正在处理项目"{self.work_dir}"的各类任务。"""
        }
        
        # 返回对应任务类型的提示词，如果没有匹配则返回默认的"其他"提示词
        return prompt_templates.get(self.task_type, prompt_templates["其他"])
    
    def _setup_claude_config(self, rules_constraint:str = None):
        """设置Claude配置文件 - 完成TODO任务"""
        try:
            # 创建.claude目录
            claude_dir = os.path.join(self.work_dir, ".claude")
            os.makedirs(claude_dir, exist_ok=True)
            
            # 复制CLAUDE.md
            claude_md_src = os.path.join(os.getcwd(), "prompts", "CLAUDE.md")
            claude_md_dst = os.path.join(self.work_dir, "CLAUDE.md")
            if os.path.exists(claude_md_src):
                shutil.copy2(claude_md_src, claude_md_dst)
            
            # 复制后替换CLAUDE.md中的work_dir
            with open(claude_md_dst, "r", encoding="utf-8") as f:
                claude_md = f.read()
                claude_md = claude_md.replace("{work_dir}", self.work_dir)                
                # 如果项目有规则约束，则替换规则约束占位符
                if rules_constraint:
                    claude_md = claude_md.replace("{rules_constraint}", f"# 项目规则约束\n{rules_constraint}")
                else:
                    claude_md = claude_md.replace("{rules_constraint}", "")
                
            
            # 写入CLAUDE.md
            with open(claude_md_dst, 'w', encoding='utf-8') as f:
                f.write(claude_md)
            
            # 复制settings.local.json
            settings_src = os.path.join(os.getcwd(), "prompts", "settings.local.json")
            settings_dst = os.path.join(claude_dir, "settings.local.json")
            if os.path.exists(settings_src) and not os.path.exists(settings_dst):
                shutil.copy2(settings_src, settings_dst)
                
        except Exception as e:
            if self.log_manager:
                self.log_manager.log_event(self.project_name, "setup_error", f"设置Claude配置失败: {e}", level="warning")
    def load_tasks(self):
        """从文件加载任务"""
        try:
            if not os.path.exists(self.task_file_name):
                return

            with open(self.task_file_name, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 清空现有任务
            self.tasks.clear()

            # 加载任务
            for task_data in data.get('tasks', []):
                task = Task.from_dict(task_data)
                self.tasks[task.id] = task
            
            self.meta = data.get('meta', {})

        except Exception as e:
            logging.error(f"加载任务文件失败: {e}")
    def get_session_id(self):
        """获取最后一个完成状态的任务的session_id"""
        # 查找最后一个状态为completed或failed的任务
        completed_or_failed_tasks = [
            task for task in self.tasks.values() 
            if task.status in ['completed', 'failed']
        ]
        
        if not completed_or_failed_tasks:
            return None
            
        # 按任务ID逆序排序，获取第一个任务（即ID最大的任务）
        last_task = sorted(completed_or_failed_tasks, key=lambda t: t.id, reverse=True)[0]
        return last_task.session_id
    def save_tasks(self):
        """保存任务到文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.task_file_name), exist_ok=True)            
            # 准备任务数据
            tasks_data = {
                "meta": self.meta,
                "tasks": [task.to_dict() for task in self.tasks.values()]
            }

            # 保存到文件
            with open(self.task_file_name, 'w', encoding='utf-8') as f:
                json.dump(tasks_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logging.error(f"保存任务文件失败: {e}")
            raise e
    def get_task(self, task_id: int) -> Optional[Task]:
        """获取任务"""
        return self.tasks.get(task_id)

    def list_tasks(self) -> List[Dict[str, Any]]:
        """列出所有任务(从文件加载)"""
        try:
            if not self.tasks:
                self.load_tasks()
                        
            return [task.to_dict() for task in self.tasks.values()]
        except Exception as e:
            logging.error(f"加载任务文件失败: {e}")
            return []
    def add_task(self, title: str, description: str, priority: str = "medium", dependencies: List[int] = None) -> str:
        """添加新任务"""
        try:
            # 使用递增的数字ID而不是UUID
            existing_ids = [int(task.id) for task in self.tasks.values() if str(task.id).isdigit()]
            task_id = max(existing_ids) + 1 if existing_ids else 1

            # 创建任务对象
            task = Task(
                id=task_id,
                title=title,
                description=description,
                priority=priority,
                dependencies=dependencies or [],
                status="pending"
            )

            # 添加到任务列表
            self.tasks[task_id] = task

            # 保存任务
            self.save_tasks()

            if self.log_manager:
                self.log_manager.log_event(
                    self.project_name, "task_added", f"添加新任务: {title}",
                    task_id=task_id, level="info"
                )

            return task_id

        except Exception as e:
            logging.error(f"添加任务失败: {e}")
            raise e
    def update_task(self, task_id: int, **kwargs) -> bool:
        """更新任务"""        
        task = self.tasks.get(int(task_id))
        if not task:
            return False
        
        # 只有pending状态的任务可以修改基本信息
        # if task.status != "pending" and any(k in kwargs for k in ['title', 'description', 'priority', 'dependencies']):
        #     return False
        
        for key, value in kwargs.items():
            if hasattr(task, key):
                setattr(task, key, value)
        
        task.updated_at = datetime.now().isoformat()
        self.save_tasks()
        
        if self.log_manager:
            self.log_manager.log_event(
                self.project_name, "task_updated", f"更新任务: {task.title}",
                task_id=task_id, level="info"
            )
        
        return True
    
    def update_task_status(self, task_id: int, status: str, result: str = "", error_message: str = "") -> bool:
        """更新任务状态"""
        task = self.tasks.get(task_id)
        if not task:
            return False
        old_status = task.status
        task.status = status
        task.updated_at = datetime.now().isoformat()
        
        if result:
            task.result = result
        self.save_tasks()
        
        if self.log_manager:
            self.log_manager.log_event(
                self.project_name, "task_status_changed", 
                f"任务状态变更: {old_status} -> {status}",
                task_id=task_id, level="info"
            )
        
        return True
    
    def delete_task(self, task_id: int) -> bool:
        """删除任务（检查依赖关系）"""
        task = self.tasks.get(task_id)
        if not task:
            return False
        
        # 检查是否有其他任务依赖此任务
        dependent_tasks = [t for t in self.tasks.values() if task_id in t.dependencies]
        if dependent_tasks:
            return False  # 有依赖任务，不能删除
        
        del self.tasks[task_id]
        self.save_tasks()
        
        if self.log_manager:
            self.log_manager.log_event(
                self.project_name, "task_deleted", f"删除任务: {task.title}",
                task_id=task_id, level="info"
            )
        
        return True
    
    def reset_all_tasks(self) -> dict[str, Any]:
        """重置所有任务状态"""
        try:
            for task_id, task in self.tasks.items():
                self.reset_task_status(task_id, save = False)

            # 重置meta数据
            self.meta["summary"] = ""

            # 保存更改
            self.save_tasks()

            logging.info(f"项目 {self.project_name} 的所有任务已重置")
            return {
                "success": True,
                "message": "项目所有任务已重置"
                }

        except Exception as e:
            logging.error(f"重置任务失败: {e}")
            return {
                "success": False,
                "message": f"重置任务失败: {e}"
                }
        
    def reset_task_status(self, task_id: int, save: bool = True) -> bool:
        """重置任务状态"""
        task = self.tasks.get(int(task_id))
        if not task:
            return False
        task.status = "pending"
        task.result = ""
        task.updated_at = datetime.now().isoformat()
        task.execution_time = 0
        task.session_id = None
        llm_log_manager = LLMLogManager(self.project_name, task.id)
        # 清空之前的日志
        llm_log_manager.clear_logs()
        if save:
            self.save_tasks()
        
        if self.log_manager:
            self.log_manager.log_event(
                self.project_name, "task_reset", f"重置任务状态: {task.title}",
                task_id=task_id, level="info"
            )
        
        return True
    
    def get_run_state(self):
        return self.curr_agent is not None
    
    def delete_all(self):
        self.reset_all_tasks()
    
    def can_task_run(self, task_id: int) -> bool:
        """检查任务是否可以运行（依赖关系检查）"""        
        task = self.tasks.get(int(task_id))
        if not task:
            return False
        
        # 检查所有依赖任务是否已完成
        for dep_id in task.dependencies:
            dep_task = self.tasks.get(int(dep_id))
            if dep_task and dep_task.status != "completed":
                return False
        
        return True
    
    def load_prompt(self, prompt_file) -> str:
        with open(prompt_file, "r") as f:
            prompt = f.read()
        return prompt
    def gen_tasks(self, user_req, num_tasks=None, knowledge_base = None, progress_callback = None) -> Dict[str, Any]:        
        self.reset_all_tasks()
        self.tasks = {}
        # 加载prompts/task_split.md文件的提示词
        prompt_file = os.path.join("prompts", "gen_task.md")
        prompt = self.load_prompt(prompt_file)
        # 替换提示词中的占位符
        # 如果任务文件已经存在，则删除
        if os.path.exists(self.task_file_name):
            os.remove(self.task_file_name)
        prompt = prompt.replace("{task_file_name}", self.task_file_name)
        if num_tasks:
            prompt = prompt.replace("{num_tasks}", f"准确创建{num_tasks}个任务，编号从1到{num_tasks}")
        else:
            prompt = prompt.replace("{num_tasks}", "根据用户需求复杂性创建最佳数量的任务 - 使用您的判断力确定多少任务是合适的")
        
        result = {}
        try:
            logging.info('正在调用Claude Code 进行任务拆解...')
            
        
            claude_agent = ClaudeAgent("你是一个需求任务拆解助手", self.provider)
            user_req = prompt + user_req
            
            result = asyncio.run(claude_agent.run_agent(self.work_dir, user_req, None, "", progress_callback))
            
            if result.get("success"):
                logging.info(f'任务拆解完成，共{len(self.list_tasks())}个任务.')    
                # 提取任务，并持久化的work_dir的./tasks/tasks.json文件
                return result   
        except Exception as e:
            logging.error(f"任务拆解出错: {e}")
            result["success"] = False
            result["message"] = str(e)
        
        return result

    def _new_log_callback(self, task_id:int, progress_callback=None):
        # 创建LLM日志管理器
        from log_manager import LLMLogManager
        llm_log_manager = LLMLogManager(self.project_name, task_id)

        # 清空之前的日志
        # llm_log_manager.clear_logs()

        def log_and_callback(progress: int, title:str, content: str=''):
            # 使用LLM日志管理器记录日志
            content = content.replace("\n", "↵ ")
            llm_log_manager.add_log_entry(f"{progress}%", title, content)

            # 调用回调函数
            if progress_callback:
                progress_callback(progress, title, content)
            else:
                print(f"{progress}% - {title}: {content[:100]}")
        return log_and_callback
    def run_single_task(self, task_id: int, task_summary:str, progress_callback=None) -> Dict[str, Any]:
        """重新运行或新运行单个任务"""
        if self.curr_agent:
            return {'success': False, 'message': '当前有任务在运行，请等待完成'}
        task = self.tasks.get(task_id)
        if not task:
            return {'success': False, 'message': '任务不存在'}
        if not self.can_task_run(task_id):
            return {'success': False, 'message': '任务依赖未满足，无法运行'}
        try:
            # 重置任务状态，总结已完成任务
            self.reset_task_status(task_id)
            if not task_summary:
                task_summary = self._summarize_completed_tasks()
            
            # 构建任务执行请求
            # task_titles = [f"任务ID:{task.get('id')}\n任务标题:{task.get('title')}\n" for task in self.list_tasks()]
            # task_titles = "\n".join(task_titles)
            user_req = f"""用户需求被拆解为多个任务执行。"""
            
            if task_summary:
                user_req += f"""\n已经完成的任务总结如下：
                {task_summary}
                """
            user_req += f"""\n
            本次请完成下面这个任务。
            # 任务详情
            任务ID: {task.id}
            任务标题: {task.title}
            任务描述: {task.description}
            任务详情: {task.details}
            验证策略: {task.testStrategy}
            """
            
            system_prompt = self.get_system_prompt()
            result = self._run_agent(task, system_prompt, user_req, progress_callback)
            
            return result
        except Exception as e:
            return {"success": False,
                    "message": f"任务执行失败: {e}"
                    }

    def continue_run(self, task_id:int, request:str, progress_callback=None) -> Dict[str, Any]:
        """继续运行任务，完成用户的新请求，"""
        task = self.tasks.get(task_id)
        if not task:
            return {'success': False, 'message': '任务不存在'}
        # TODO 是否需要检查任务是否已经执行过？
        # if task.status != "completed" or task.status != "failed":
        #     return self.run_single_task(task_id, content, progress_callback)
        
        # 使用task原会话ID
        task.next_requests.append(request)
        return self._run_agent(task, "", request, progress_callback)
    def _run_agent(self, task: Task, system_prompt:str, user_request:str, progress_callback=None) -> Dict[str, Any]:
        """运行agent任务"""
        if self.curr_agent:
            # TODO 只支持一个智能体运行，并发控制？
            return {'success': False, 'message': '智能体运行中，请稍后重试....'}
        result = {}
        start_time = time.time()  # 记录开始时间
        try:
                            
            # 更新任务状态为进行中
            task.status = "in_progress"
            task.updated_at = datetime.now().isoformat()
            self.save_tasks()
            
            log_and_callback = self._new_log_callback(task.id, progress_callback)
            
            self.curr_agent = ClaudeAgent(system_prompt, self.provider)
            result = asyncio.run(self.curr_agent.run_agent(self.work_dir, user_request, task.session_id, "", log_and_callback))
            
            task.result = result["message"]
            if result.get("session_id"):
                task.session_id = result.get("session_id")
            else:
                task.session_id = None
            
            if result.get("success"):
                task.status = "completed"
            else:
                task.status = "failed"
            
            return result
        except Exception as e:
            logging.error(f"任务-[{task.title}]运行出错: {e}")
            
            # 更新任务状态为失败
            task.status = "failed"
            task.result = str(e)
            result["success"] = False
            result["message"] = str(e)
            return result
        finally:
            # 计算执行时间（即使失败也要记录）
            end_time = time.time()
            execution_time = end_time - start_time
            task.execution_time += execution_time # 累计执行时间
            
            self.save_tasks()
            self.curr_agent = None
    def get_task_logs(self, task_id: int) -> List[str]:
        """获取任务运行日志"""
        try:
            from log_manager import LLMLogManager
            llm_log_manager = LLMLogManager(self.project_name, task_id)

            # 获取LLM日志
            log_entries = llm_log_manager.get_logs(100)

            if not log_entries:
                return ["暂无日志记录"]

            # 转换为字符串格式
            logs = []
            for entry in log_entries:
                log_line = f"[{entry['timestamp']}] {entry['progress']} - {entry['log_type']}: {entry['content']}"
                logs.append(log_line)

            return logs

        except Exception as e:
            logging.error(f"获取任务日志失败: {e}")
            return [f"获取日志失败: {e}"]
    def get_runnable_tasks(self) -> List[Task]:
        """获取可运行的任务"""
        runnable = []
        for task in self.tasks.values():
            if task.status != "completed" and self.can_task_run(task.id):
                runnable.append(task)
        return runnable
    
    def stop_execution(self):
        """停止任务执行"""
        self.stop_event.set()
        self.is_running = False
        if self.curr_agent:
            self.curr_agent.stop()
            self.curr_agent = None

        if self.log_manager:
            self.log_manager.log_event(
                self.project_name, "execution_stopped", "任务执行已停止",
                level="warning"
            )

    def _summarize_completed_tasks(self) -> str:
        """总结已完成的任务 """
        completed_tasks = [task for task in self.tasks.values() if task.status in ["completed"]]

        if not completed_tasks:
            return ""
        final_summary = self.meta.get("summary","")
        if final_summary:
            final_summary = ""
            #return final_summary
        
        batch_summary = []
        task_content = ""
        if self.provider == "local":
            max_batch_size = 50 * 1024
        else:
            max_batch_size = 100 * 1024
        for task in completed_tasks:
            task_content += f"任务ID:{task.id}\n任务描述:{task.description}\n\n任务执行结果:{task.result}\n"
            if len(task_content.encode('utf-8')) > max_batch_size:
                batch_summary.append(self._summarize_task_batch(task_content))
                task_content = ""
        if task_content:
            batch_summary.append(self._summarize_task_batch(task_content))
        
        if len(batch_summary) > 1:
            #合并多个总结
            final_summary = self._summarize_task_batch(batch_summary)
        else:
            final_summary = batch_summary[0]
        
        if self.meta:
            self.meta["summary"] = final_summary
        self.save_tasks()

        return final_summary

    def _summarize_task_batch(self, task_content: str, progress_callback = None) -> str:
        """总结一批任务"""
        try:
            claude_agent = ClaudeAgent("你是一个任务总结助手，可以把已经完成的任务进行总结，用于指导下一个任务的执行", self.provider)

            user_req = f"""请对以下已完成的任务进行总结：
            
            {task_content}
            """
            # 请提供简洁的总结，包括：
            # 1. 项目的简介、主要架构
            # 2. 已经完成的任务介绍
            # 3. 对后续任务的建议

             # 使用单独会话
            result = asyncio.run(claude_agent.run_agent(self.work_dir, user_req, None, "", progress_callback))
            
            if result.get("success"):
                summary = result["message"]
                return summary

        except Exception as e:
            logging.error(f"任务总结出错: {e}")
            result["message"] = f"任务总结出错: {e}"
        
        return result.get("message")

    def auto_run_tasks(self, parallel_mode: bool = False, progress_callback: Callable = None) -> Dict[str, Any]:
        """自动运行任务 - 完成所有TODO任务"""
        if self.is_running:
            return {"success": False, "message": "任务正在运行中"}

        self.is_running = True
        self.stop_event.clear()

        if self.log_manager:
            self.log_manager.log_event(
                self.project_name, "execution_started",
                f"开始执行任务 ({'并行' if parallel_mode else '顺序'}模式)",
                level="info"
            )

        try:
            if parallel_mode:
                return self._run_tasks_parallel(progress_callback)
            else:
                return self._run_tasks_sequential(progress_callback)
        finally:
            self.is_running = False

    def _run_tasks_sequential(self, progress_callback: Callable = None) -> Dict[str, Any]:
        """顺序执行任务"""
        executed_count = 0

        while True and executed_count < len(self.tasks):
            if self.stop_event.is_set():
                break

            # 获取可运行的任务
            runnable_tasks = self.get_runnable_tasks()
            if not runnable_tasks:
                break

            # 按优先级排序
            runnable_tasks.sort(key=lambda t: {"high": 3, "medium": 2, "low": 1}.get(t.priority, 2), reverse=True)

            # 执行第一个任务
            task = runnable_tasks[0]
            success = self.run_single_task(task.id, None, progress_callback)
            executed_count += 1

            if not success and not self.stop_event.is_set():
                # 任务失败，记录日志但继续执行其他任务
                if self.log_manager:
                    self.log_manager.log_event(
                        self.project_name, "task_failed", f"任务执行失败: {task.title}",
                        task_id=task.id, level="error"
                    )

        return {
            "success": True,
            "message": f"顺序执行完成，共执行{executed_count}个任务",
            "executed_count": executed_count
        }

    def _run_tasks_parallel(self, progress_callback: Callable = None) -> Dict[str, Any]:
        """并行执行任务"""
        executed_count = 0

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            while True:
                if self.stop_event.is_set():
                    break

                # 获取可运行的任务
                runnable_tasks = self.get_runnable_tasks()
                if not runnable_tasks:
                    break

                session_id = None
                # 并行任务使用新会话，总结已完成的任务
                task_summary = self._summarize_completed_tasks()

                # 提交并行任务
                future_to_task = {}
                for task in runnable_tasks[:self.max_workers]:
                    if self.stop_event.is_set():
                        break

                    # 为并发任务使用新的会话ID
                    session_id = None
                    future = executor.submit(self.run_single_task, task, task_summary, progress_callback)
                    future_to_task[future] = task

                # 等待任务完成
                for future in as_completed(future_to_task):
                    if self.stop_event.is_set():
                        break

                    task = future_to_task[future]
                    try:
                        success = future.result()
                        executed_count += 1

                        if not success:
                            if self.log_manager:
                                self.log_manager.log_event(
                                    self.project_name, "task_failed", f"并行任务执行失败: {task.title}",
                                    task_id=task.id, level="error"
                                )
                    except Exception as e:
                        if self.log_manager:
                            self.log_manager.log_event(
                                self.project_name, "task_error", f"并行任务异常: {e}",
                                task_id=task.id, level="error"
                            )

        return {
            "success": True,
            "message": f"并行执行完成，共执行{executed_count}个任务",
            "executed_count": executed_count
        }
    def restart_task(self, task_id: int) -> bool:
        """重新开始任务（使用新的会话ID）"""
        if task_id not in self.tasks:
            return False

        task = self.tasks[task_id]

        # 总结之前的结果
        previous_summary = ""
        if task.result:
            previous_summary = f"之前的执行结果: {task.result}"

        # 重置任务状态
        task.status = "pending"
        task.result = previous_summary
        # 不生成新的session_id，保持原有的session_id
        task.updated_at = datetime.now().isoformat()

        if self.log_manager:
            self.log_manager.log_event(
                self.project_name, "task_restarted", f"重新开始任务: {task.title}",
                task_id=task_id, level="info"
            )

        return True

    def quick_task(self, description: str, progress_callback: Callable = None) -> Dict[str, Any]:
        """快速任务：一句话描述需求，系统自动生成任务并启动运行"""
        try:
            # 生成任务
            task_id = self.add_task(
                title=f"快速任务: {description[:50]}...",
                description=description,
                priority="high"
            )

            if self.log_manager:
                self.log_manager.log_event(
                    self.project_name, "quick_task_created", f"创建快速任务: {description}",
                    task_id=task_id, level="info"
                )

            # 立即执行
            task = self.get_task(task_id)
            success = self.run_single_task(task, None, progress_callback)

            return {
                "success": success,
                "task_id": task_id,
                "message": "快速任务执行完成" if success else "快速任务执行失败"
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"快速任务创建失败: {str(e)}"
            }

    def get_task_statistics(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        total = len(self.tasks)
        pending = len([t for t in self.tasks.values() if t.status == "pending"])
        running = len([t for t in self.tasks.values() if t.status == "running"])
        completed = len([t for t in self.tasks.values() if t.status == "completed"])
        failed = len([t for t in self.tasks.values() if t.status == "failed"])

        return {
            "total": total,
            "pending": pending,
            "running": running,
            "completed": completed,
            "failed": failed,
            "completion_rate": (completed / total * 100) if total > 0 else 0
        }

    def export_tasks(self) -> Dict[str, Any]:
        """导出任务数据"""
        return {
            "req_id": self.project_name,
            "tasks": [task.to_dict() for task in self.tasks.values()],
            "statistics": self.get_task_statistics(),
            "exported_at": datetime.now().isoformat()
        }

    def get_task_logs(self, limit: int = 50):
        """获取任务日志"""
        if not self.log_manager:
            return []
        
        try:
            # 从日志管理器获取日志，使用req_id作为过滤条件
            logs = self.log_manager.get_task_logs(limit=limit)
            # 过滤与当前需求相关的日志
            filtered_logs = [log for log in logs if log.get("req_id") == self.project_name or log.get("task_id") == self.project_name]
            return filtered_logs[:limit]
        except Exception as e:
            if self.log_manager:
                self.log_manager.log_event(self.project_name, "log_fetch_error", f"获取日志失败: {e}", level="error")
            return []
