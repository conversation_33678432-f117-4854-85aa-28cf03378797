#!/usr/bin/env python3
"""
知识库管理器
用于管理项目相关的知识库，支持向量存储和检索
"""

import os
import json
import uuid
import hashlib
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict

try:
    from pymilvus import connections, Collection, FieldSchema, CollectionSchema, DataType, utility
    MILVUS_AVAILABLE = True
except ImportError:
    MILVUS_AVAILABLE = False
    print("Warning: Milvus packages not available. Knowledge base functionality will be limited.")


@dataclass
class KnowledgeBase:
    """知识库数据类"""
    kb_id: str
    name: str
    description: str
    project_id: str
    collection_name: str
    created_at: str
    updated_at: str
    document_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class Document:
    """文档数据类"""
    doc_id: str
    kb_id: str
    title: str
    content: str
    metadata: Dict[str, Any]
    created_at: str
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class KnowledgeManager:
    """知识库管理器"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = data_dir
        self.kb_file = os.path.join(data_dir, "knowledge_bases.json")
        self.milvus_dir = os.path.join(data_dir, "milvus")
        
        # 确保目录存在
        os.makedirs(data_dir, exist_ok=True)
        os.makedirs(self.milvus_dir, exist_ok=True)
        
        # 加载知识库数据
        self.knowledge_bases = self._load_knowledge_bases()
        
        # 初始化Milvus连接
        self.milvus_client = None
        if MILVUS_AVAILABLE:
            self._init_milvus()
    
    def _init_milvus(self):
        """初始化Milvus连接"""
        try:
            # 使用Milvus Lite，直接连接到本地文件
            milvus_uri = f"./data/milvus/milvus.db"
            connections.connect("default", uri=milvus_uri)
            self.milvus_client = True
            print(f"Milvus Lite initialized at {milvus_uri}")
        except Exception as e:
            print(f"Failed to initialize Milvus: {e}")
            self.milvus_client = None
    
    def _load_knowledge_bases(self) -> Dict[str, KnowledgeBase]:
        """加载知识库数据"""
        if os.path.exists(self.kb_file):
            try:
                with open(self.kb_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return {kb_id: KnowledgeBase(**kb_data) for kb_id, kb_data in data.items()}
            except Exception as e:
                print(f"Error loading knowledge bases: {e}")
        return {}
    
    def _save_knowledge_bases(self):
        """保存知识库数据"""
        try:
            data = {kb_id: kb.to_dict() for kb_id, kb in self.knowledge_bases.items()}
            with open(self.kb_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"Error saving knowledge bases: {e}")
    
    def create_knowledge_base(self, name: str, description: str, project_id: str) -> str:
        """创建知识库"""
        kb_id = str(uuid.uuid4())
        collection_name = f"kb_{kb_id.replace('-', '_')}"

        # 创建知识库对象
        kb = KnowledgeBase(
            kb_id=kb_id,
            name=name,
            description=description,
            project_id=project_id,
            collection_name=collection_name,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )

        # 创建Milvus集合
        if self.milvus_client and self._create_collection(collection_name):
            self.knowledge_bases[kb_id] = kb
            self._save_knowledge_bases()
            return kb_id
        else:
            raise Exception("Failed to create Milvus collection")

    def get_or_create_project_knowledge_base(self, project_id: str, project_name: str = None) -> KnowledgeBase:
        """获取或创建项目的默认知识库"""
        # 查找项目是否已有知识库
        for kb in self.knowledge_bases.values():
            if kb.project_id == project_id:
                return kb

        # 如果没有，则自动创建一个默认知识库
        kb_name = f"{project_name or '项目'}知识库" if project_name else f"项目{project_id}知识库"
        kb_description = f"项目 {project_name or project_id} 的默认知识库"

        kb_id = self.create_knowledge_base(kb_name, kb_description, project_id)
        return self.knowledge_bases[kb_id]
    
    def _create_collection(self, collection_name: str) -> bool:
        """创建Milvus集合"""
        try:
            if not self.milvus_client:
                return False
            
            # 定义字段
            fields = [
                FieldSchema(name="id", dtype=DataType.VARCHAR, max_length=100, is_primary=True),
                FieldSchema(name="title", dtype=DataType.VARCHAR, max_length=500),
                FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=10000),
                FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=384),  # 使用384维向量
                FieldSchema(name="metadata", dtype=DataType.VARCHAR, max_length=2000)
            ]
            
            # 创建集合schema
            schema = CollectionSchema(fields, f"Knowledge base collection: {collection_name}")
            
            # 创建集合
            collection = Collection(collection_name, schema)
            
            # 创建索引
            index_params = {
                "metric_type": "L2",
                "index_type": "IVF_FLAT",
                "params": {"nlist": 128}
            }
            collection.create_index("embedding", index_params)
            
            return True
        except Exception as e:
            print(f"Error creating collection {collection_name}: {e}")
            return False
    
    def list_knowledge_bases(self, project_id: str = None) -> List[KnowledgeBase]:
        """列出知识库"""
        kbs = list(self.knowledge_bases.values())
        if project_id:
            kbs = [kb for kb in kbs if kb.project_id == project_id]
        return kbs
    
    def get_knowledge_base(self, kb_id: str) -> Optional[KnowledgeBase]:
        """获取知识库"""
        return self.knowledge_bases.get(kb_id)
    
    def delete_knowledge_base(self, kb_id: str) -> bool:
        """删除知识库"""
        kb = self.knowledge_bases.get(kb_id)
        if not kb:
            return False
        
        try:
            # 删除Milvus集合
            if self.milvus_client:
                utility.drop_collection(kb.collection_name)
            
            # 删除知识库记录
            del self.knowledge_bases[kb_id]
            self._save_knowledge_bases()
            return True
        except Exception as e:
            print(f"Error deleting knowledge base {kb_id}: {e}")
            return False
    
    def add_document(self, kb_id: str, title: str, content: str, metadata: Dict[str, Any] = None) -> str:
        """添加文档到知识库"""
        kb = self.knowledge_bases.get(kb_id)
        if not kb:
            raise Exception("Knowledge base not found")
        
        if not self.milvus_client:
            raise Exception("Milvus not available")
        
        doc_id = str(uuid.uuid4())
        
        # 生成文档向量（这里使用简单的hash作为示例，实际应该使用embedding模型）
        embedding = self._generate_embedding(content)
        
        # 准备数据
        data = [
            [doc_id],
            [title],
            [content[:10000]],  # 限制内容长度
            [embedding],
            [json.dumps(metadata or {}, ensure_ascii=False)]
        ]
        
        try:
            # 插入到Milvus
            collection = Collection(kb.collection_name)
            collection.insert(data)
            collection.flush()
            
            # 更新知识库统计
            kb.document_count += 1
            kb.updated_at = datetime.now().isoformat()
            self._save_knowledge_bases()
            
            return doc_id
        except Exception as e:
            print(f"Error adding document to knowledge base {kb_id}: {e}")
            raise
    
    def _generate_embedding(self, text: str) -> List[float]:
        """生成文本向量（简单实现）"""
        # 这里使用简单的hash方法生成固定维度的向量
        # 实际应用中应该使用专业的embedding模型
        hash_obj = hashlib.md5(text.encode('utf-8'))
        hash_bytes = hash_obj.digest()
        
        # 将hash转换为384维向量
        embedding = []
        for i in range(384):
            byte_index = i % len(hash_bytes)
            embedding.append(float(hash_bytes[byte_index]) / 255.0)
        
        return embedding
    
    def search_documents(self, kb_id: str, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """在知识库中搜索文档"""
        kb = self.knowledge_bases.get(kb_id)
        if not kb:
            return []
        
        if not self.milvus_client:
            return []
        
        try:
            # 生成查询向量
            query_embedding = self._generate_embedding(query)
            
            # 搜索
            collection = Collection(kb.collection_name)
            collection.load()
            
            search_params = {"metric_type": "L2", "params": {"nprobe": 10}}
            results = collection.search(
                data=[query_embedding],
                anns_field="embedding",
                param=search_params,
                limit=limit,
                output_fields=["title", "content", "metadata"]
            )
            
            # 格式化结果
            documents = []
            for hits in results:
                for hit in hits:
                    documents.append({
                        "id": hit.id,
                        "title": hit.entity.get("title"),
                        "content": hit.entity.get("content"),
                        "metadata": json.loads(hit.entity.get("metadata", "{}")),
                        "score": hit.score
                    })
            
            return documents
        except Exception as e:
            print(f"Error searching in knowledge base {kb_id}: {e}")
            return []
