<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务列表 - AI任务管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .task-card {
            transition: transform 0.2s;
        }
        .task-card:hover {
            transform: translateY(-2px);
        }
        .requirement-container {
            max-height: 150px;
            overflow: hidden;
            position: relative;
        }
        .requirement-container.expanded {
            max-height: none;
        }
        .requirement-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: linear-gradient(transparent, #f8f9fa);
            display: flex;
            justify-content: center;
            align-items: end;
            padding-bottom: 8px;
        }
        body {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- 主内容区域 - 只保留任务列表 -->
    <div class="container-fluid p-3">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
            
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-success" id="run-tasks-btn">
                        <i class="fas fa-play"></i> 运行任务
                    </button>
                    <button type="button" class="btn btn-warning" id="reset-tasks-btn">
                        <i class="fas fa-undo"></i> 重置任务
                    </button>
                    <button type="button" class="btn btn-info" id="stop-execution-btn">
                        <i class="fas fa-stop"></i> 停止运行
                    </button>
                    <button type="button" class="btn btn-danger" id="generate-tasks-btn">
                        <i class="fas fa-redo"></i> 生成任务
                    </button>
                    <button type="button" class="btn btn-outline-info" id="view-files-btn">
                        <i class="fas fa-folder-open"></i> 文件预览
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="refresh-tasks-btn">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <button type="button" class="btn btn-outline-primary" id="add-task-btn">
                        <i class="fas fa-plus"></i> 添加任务
                    </button>
                </div>
            </div>
        </div>

        <!-- 项目信息卡片 -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle"></i> 项目信息</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>项目名称:</strong> <span id="project-name-detail">-</span></p>
                                <p><strong>LLM Provider:</strong> <span id="project-provider">-</span></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>创建时间:</strong> <span id="project-created-at">-</span></p>
                                <p><strong>Agent状态:</strong> <span id="project-run-state">-</span></p>
                            </div>
                        </div>
                        <div class="mt-3" id="requirement-section" style="display: none;">
                            <h6><i class="fas fa-file-alt"></i> 项目需求</h6>
                            <div class="border rounded p-3 bg-light requirement-container" id="requirement-container">
                                <div id="project-requirement"></div>
                                <div class="requirement-overlay" id="requirement-overlay" style="display: none;">
                                    <button id="toggle-requirement" class="btn btn-sm btn-outline-primary" type="button">查看更多</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-bar"></i> 任务统计</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="h4 mb-0 text-info" id="total-tasks">0</div>
                                <small class="text-muted">总任务</small>
                            </div>
                            <div class="col-6">
                                <div class="h4 mb-0 text-success" id="completed-tasks">0</div>
                                <small class="text-muted">已完成</small>
                            </div>
                        </div>
                        <div class="row text-center mt-3">
                            <div class="col-6">
                                <div class="h4 mb-0 text-warning" id="in-progress-tasks">0</div>
                                <small class="text-muted">进行中</small>
                            </div>
                            <div class="col-6">
                                <div class="h4 mb-0 text-danger" id="failed-tasks">0</div>
                                <small class="text-muted">失败</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务列表容器 -->
        <div id="tasks-container">
            <div class="text-center py-5">
                <i class="fas fa-spinner fa-spin fa-3x text-muted mb-3"></i>
                <div>加载中...</div>
            </div>
        </div>
    </div>

    <!-- 添加任务模态框 -->
    <div class="modal fade" id="addTaskModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加新任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addTaskForm">
                        <div class="mb-3">
                            <label for="taskTitle" class="form-label">任务标题 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="taskTitle" required
                                   placeholder="请输入任务标题">
                        </div>
                        <div class="mb-3">
                            <label for="taskDescription" class="form-label">任务描述</label>
                            <textarea class="form-control" id="taskDescription" rows="4"
                                      placeholder="请输入任务描述"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="taskPriority" class="form-label">优先级</label>
                            <select class="form-control" id="taskPriority">
                                <option value="low">低</option>
                                <option value="medium" selected>中</option>
                                <option value="high">高</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="taskDependencies" class="form-label">依赖任务 (可选)</label>
                            <input type="text" class="form-control" id="taskDependencies"
                                   placeholder="输入依赖的任务ID，多个用逗号分隔">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmAddTask">添加任务</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑任务模态框 -->
    <div class="modal fade" id="editTaskModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editTaskForm">
                        <input type="hidden" id="editTaskId">
                        <div class="mb-3">
                            <label for="editTaskTitle" class="form-label">任务标题 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editTaskTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="editTaskDescription" class="form-label">任务描述</label>
                            <textarea class="form-control" id="editTaskDescription" rows="4"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="editTaskPriority" class="form-label">优先级</label>
                            <select class="form-control" id="editTaskPriority">
                                <option value="low">低</option>
                                <option value="medium">中</option>
                                <option value="high">高</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editTaskDependencies" class="form-label">依赖任务 (可选)</label>
                            <input type="text" class="form-control" id="editTaskDependencies">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmEditTask">保存更改</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 继续任务模态框 -->
    <div class="modal fade" id="continueTaskModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">继续任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="continueTaskForm">
                        <input type="hidden" id="continueTaskId">
                        <div class="mb-3">
                            <label for="continueTaskRequest" class="form-label">请求内容 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="continueTaskRequest" rows="4" required
                                      placeholder="请输入您希望继续执行的任务请求"></textarea>
                            <div class="form-text">请描述您希望对已完成的任务进行的进一步操作</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmContinueTask">继续任务</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 生成任务确认模态框 -->
    <div class="modal fade" id="generateTasksModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">生成任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="generateTasksContent">
                        <!-- 内容将通过JavaScript加载 -->
                    </div>
                    <div class="mt-3" id="taskCountSection" style="display: none;">
                        <label for="taskCount" class="form-label">任务数量 (可选)</label>
                        <input type="number" class="form-control" id="taskCount" min="1" max="20"
                               placeholder="留空则自动确定任务数量">
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            建议生成5-10个任务，留空将根据需求自动确定
                        </div>
                    </div>
                    <input type="hidden" id="generateTasksProjectId">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="ProjectManager.confirmGenerateTasks()">确认生成</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

    <!-- 自定义JS -->
    <script src="static/js/common.js"></script>
    <script src="static/js/projects.js"></script>
    <script src="static/js/tasks.js"></script>

    <script>
        // 页面初始化
        $(document).ready(function() {
            const projectId = Router.getParam('project_id');
            if (projectId) {
                TaskManager.initTasksPage(projectId);
            } else {
                Utils.showAlert('未指定项目ID', 'error');
            }
        });
    </script>
</body>
</html>
