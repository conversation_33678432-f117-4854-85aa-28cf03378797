<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则管理 - AI任务管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    
    <style>
        .editor-container {
            height: calc(100vh - 120px);
            display: flex;
            flex-direction: column;
        }
        
        .editor-toolbar {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 8px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }
        
        .editor-content {
            flex: 1;
            display: flex;
            min-height: 0;
        }
        
        .editor-pane {
            flex: 1;
            border-right: 1px solid #dee2e6;
        }
        
        .editor-pane:last-child {
            border-right: none;
        }
        
        .editor-textarea {
            width: 100%;
            height: 100%;
            border: none;
            resize: none;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.6;
            padding: 15px;
        }
        
        .editor-textarea:focus {
            outline: none;
            box-shadow: none;
        }
        
        .preview-pane {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            background-color: #ffffff;
        }
        
        .preview-content {
            line-height: 1.8;
        }
        
        .preview-content h1,
        .preview-content h2,
        .preview-content h3,
        .preview-content h4,
        .preview-content h5,
        .preview-content h6 {
            margin-top: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .preview-content h1 {
            border-bottom: 2px solid #eee;
            padding-bottom: 0.5rem;
        }
        
        .preview-content pre {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 1rem;
            overflow-x: auto;
        }
        
        .preview-content blockquote {
            border-left: 4px solid #007bff;
            padding-left: 1rem;
            margin-left: 0;
            color: #6c757d;
        }
        
        .view-mode-buttons .btn {
            margin-left: 5px;
        }
        
        .btn-sm {
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-tasks"></i> AI任务管理系统
            </a>
            <span class="navbar-text text-white">
                规则管理 - <span id="projectName">加载中...</span>
            </span>
        </div>
    </nav>

    <div class="container-fluid p-0">
        <div class="editor-container">
            <!-- 工具栏 -->
            <div class="editor-toolbar">
                <div class="toolbar-left">
                    <h6 class="mb-0">规则管理</h6>
                </div>
                <div class="toolbar-right d-flex align-items-center">
                    <!-- 动态按钮区域 -->
                    <div id="actionButtons" class="me-3">
                        <button class="btn btn-outline-primary btn-sm" onclick="saveContent()">
                            <i class="fas fa-save"></i> 保存
                        </button>
                    </div>
                    
                    <!-- 视图模式切换 -->
                    <div class="view-mode-buttons">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="editModeBtn" onclick="setViewMode('edit')">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="previewModeBtn" onclick="setViewMode('preview')">
                                <i class="fas fa-eye"></i> 预览
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="splitModeBtn" onclick="setViewMode('split')">
                                <i class="fas fa-columns"></i> 混合
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 编辑区域 -->
            <div class="editor-content">
                <div class="editor-pane" id="editorPane">
                    <textarea class="editor-textarea" id="markdownEditor" placeholder="请输入规则内容..."></textarea>
                </div>
                <div class="preview-pane" id="previewPane" style="display: none;">
                    <div class="preview-content" id="previewContent">
                        <div class="text-muted text-center">
                            <i class="fas fa-eye-slash fa-2x mb-3"></i>
                            <p>暂无预览内容</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        let currentProjectId = null;
        let currentViewMode = 'edit';
        let apiEndpoint = 'rules';
        let contentField = 'rules_constraint';

        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 初始化
        $(document).ready(function() {
            currentProjectId = getUrlParameter('project_id');
            if (!currentProjectId) {
                alert('缺少项目ID参数');
                window.close();
                return;
            }
            
            loadProjectInfo();
            loadContent();
            
            // 绑定编辑器输入事件
            $('#markdownEditor').on('input', function() {
                if (currentViewMode === 'split' || currentViewMode === 'preview') {
                    updatePreview();
                }
            });
        });

        // 加载项目信息
        function loadProjectInfo() {
            fetch(`/api/projects/${currentProjectId}`)
                .then(response => response.json())
                .then(project => {
                    document.getElementById('projectName').textContent = project.name;
                })
                .catch(error => {
                    console.error('加载项目信息失败:', error);
                    document.getElementById('projectName').textContent = '未知项目';
                });
        }

        // 加载内容
        function loadContent() {
            fetch(`/api/projects/${currentProjectId}/${apiEndpoint}`)
                .then(response => response.json())
                .then(data => {
                    const content = data[contentField] || '';
                    document.getElementById('markdownEditor').value = content;
                    updatePreview();
                })
                .catch(error => {
                    console.error('加载内容失败:', error);
                    showAlert('加载内容失败', 'danger');
                });
        }

        // 保存内容
        function saveContent() {
            const content = document.getElementById('markdownEditor').value;
            const payload = {};
            payload[contentField] = content;
            
            fetch(`/api/projects/${currentProjectId}/${apiEndpoint}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    showAlert('保存成功', 'success');
                } else {
                    showAlert('保存失败: ' + result.message, 'danger');
                }
            })
            .catch(error => {
                console.error('保存失败:', error);
                showAlert('保存失败', 'danger');
            });
        }

        // 设置视图模式
        function setViewMode(mode) {
            currentViewMode = mode;
            
            const editorPane = document.getElementById('editorPane');
            const previewPane = document.getElementById('previewPane');
            const editBtn = document.getElementById('editModeBtn');
            const previewBtn = document.getElementById('previewModeBtn');
            const splitBtn = document.getElementById('splitModeBtn');
            
            // 重置按钮状态
            [editBtn, previewBtn, splitBtn].forEach(btn => {
                btn.classList.remove('btn-secondary');
                btn.classList.add('btn-outline-secondary');
            });
            
            switch (mode) {
                case 'edit':
                    editorPane.style.display = 'block';
                    editorPane.style.flex = '1';
                    previewPane.style.display = 'none';
                    editBtn.classList.remove('btn-outline-secondary');
                    editBtn.classList.add('btn-secondary');
                    break;
                case 'preview':
                    editorPane.style.display = 'none';
                    previewPane.style.display = 'block';
                    previewPane.style.flex = '1';
                    previewBtn.classList.remove('btn-outline-secondary');
                    previewBtn.classList.add('btn-secondary');
                    updatePreview();
                    break;
                case 'split':
                    editorPane.style.display = 'block';
                    editorPane.style.flex = '1';
                    previewPane.style.display = 'block';
                    previewPane.style.flex = '1';
                    splitBtn.classList.remove('btn-outline-secondary');
                    splitBtn.classList.add('btn-secondary');
                    updatePreview();
                    break;
            }
        }

        // 更新预览
        function updatePreview() {
            const content = document.getElementById('markdownEditor').value;
            const previewContent = document.getElementById('previewContent');
            
            if (!content.trim()) {
                previewContent.innerHTML = `
                    <div class="text-muted text-center">
                        <i class="fas fa-eye-slash fa-2x mb-3"></i>
                        <p>暂无预览内容</p>
                    </div>
                `;
                return;
            }
            
            try {
                const html = marked.parse(content);
                previewContent.innerHTML = html;
            } catch (error) {
                console.error('Markdown解析失败:', error);
                previewContent.innerHTML = `
                    <div class="text-danger text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                        <p>Markdown解析失败</p>
                    </div>
                `;
            }
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show position-fixed" 
                     style="top: 80px; right: 20px; z-index: 1050; min-width: 300px;" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', alertHtml);
            
            // 3秒后自动消失
            setTimeout(() => {
                const alerts = document.querySelectorAll('.alert');
                if (alerts.length > 0) {
                    alerts[alerts.length - 1].remove();
                }
            }, 3000);
        }

        // 生成功能（需求管理专用）
        function generateContent() {
            showAlert('生成功能开发中...', 'info');
        }

        // 优化功能
        function optimizeContent() {
            showAlert('优化功能开发中...', 'info');
        }
    </script>
</body>
</html>
