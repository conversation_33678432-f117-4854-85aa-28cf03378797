<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库详情 - AI任务管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background-color: #f8f9fa;
        }
        
        .main-content {
            margin-left: 240px;
            padding: 20px;
        }
        
        .search-result {
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
            margin-bottom: 1rem;
        }
        
        .search-score {
            font-size: 0.8rem;
            color: #6c757d;
        }
        
        .document-content {
            max-height: 200px;
            overflow-y: auto;
            font-size: 0.9rem;
        }
        
        .kb-info-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .search-section {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .add-document-section {
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="index.html">AI任务管理系统</a>
        <div class="navbar-nav">
            <div class="nav-item text-nowrap">
                <a class="nav-link px-3" href="knowledge_manager.html">返回知识库管理</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.html">
                                <i class="fas fa-tachometer-alt"></i> 仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="projects.html">
                                <i class="fas fa-folder-open"></i> 项目列表
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="knowledge_manager.html">
                                <i class="fas fa-brain"></i> 知识库管理
                            </a>
                        </li>
                    </ul>
                    
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>快速操作</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showAddDocumentModal()">
                                <i class="fas fa-plus"></i> 添加文档
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#searchSection">
                                <i class="fas fa-search"></i> 检索测试
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 知识库信息卡片 -->
                <div class="card kb-info-card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="card-title mb-2" id="kbName">知识库名称</h2>
                                <p class="card-text mb-2" id="kbDescription">知识库描述</p>
                                <small class="text-light" id="kbCreatedAt">创建时间</small>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="d-flex flex-column align-items-end">
                                    <div class="mb-2">
                                        <span class="badge bg-light text-dark fs-6" id="documentCount">0 个文档</span>
                                    </div>
                                    <div>
                                        <button class="btn btn-light btn-sm me-2" onclick="showAddDocumentModal()">
                                            <i class="fas fa-plus"></i> 添加文档
                                        </button>
                                        <button class="btn btn-outline-light btn-sm" onclick="deleteKnowledgeBase()">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 检索测试区域 -->
                <div class="search-section" id="searchSection">
                    <h4 class="mb-3">
                        <i class="fas fa-search text-primary"></i> 知识库检索测试
                    </h4>
                    <div class="row">
                        <div class="col-md-8">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchQuery" placeholder="输入搜索关键词...">
                                <button class="btn btn-primary" onclick="searchDocuments()">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select" id="searchLimit">
                                <option value="5">显示 5 条结果</option>
                                <option value="10" selected>显示 10 条结果</option>
                                <option value="20">显示 20 条结果</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 搜索结果 -->
                <div id="searchResults" style="display: none;">
                    <h5 class="mb-3">搜索结果</h5>
                    <div id="searchResultsList">
                        <!-- 搜索结果将在这里显示 -->
                    </div>
                </div>

                <!-- 添加文档区域 -->
                <div class="add-document-section">
                    <h4 class="mb-3">
                        <i class="fas fa-file-plus text-success"></i> 添加文档
                    </h4>
                    <form id="addDocumentForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="docTitle" class="form-label">文档标题 *</label>
                                    <input type="text" class="form-control" id="docTitle" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="docCategory" class="form-label">分类</label>
                                    <input type="text" class="form-control" id="docCategory" placeholder="例如：技术文档、需求说明等">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="docContent" class="form-label">文档内容 *</label>
                            <textarea class="form-control" id="docContent" rows="8" required placeholder="请输入文档内容..."></textarea>
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" onclick="clearDocumentForm()">清空</button>
                            <button type="button" class="btn btn-success" onclick="addDocument()">
                                <i class="fas fa-plus"></i> 添加文档
                            </button>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认删除知识库</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除知识库 "<span id="deleteKbName"></span>" 吗？</p>
                    <p class="text-danger"><small>此操作不可撤销，将删除知识库中的所有文档。</small></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="confirmDeleteKnowledgeBase()">删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- 通用JS -->
    <script src="/static/js/common.js"></script>
    <!-- 知识库管理JS -->
    <script src="/static/js/knowledge.js"></script>
    
    <script>
        // 页面加载完成后初始化
        $(document).ready(function() {
            loadKnowledgeBaseDetail();
            
            // 回车键搜索
            $('#searchQuery').keypress(function(e) {
                if (e.which == 13) {
                    searchDocuments();
                }
            });
        });
    </script>
</body>
</html>
