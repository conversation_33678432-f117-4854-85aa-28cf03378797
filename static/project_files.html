<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件浏览 - AI任务管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .main-content {
            min-height: 100vh;
        }
        .file-tree {
            max-height: 70vh;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            background-color: #f8f9fa;
        }
        .file-tree ul {
            list-style: none;
            padding-left: 1.5rem;
            margin: 0;
        }
        .file-tree > ul {
            padding-left: 0;
        }
        .file-item {
            padding: 0.25rem 0.5rem;
            cursor: pointer;
            border-radius: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .file-item:hover {
            background-color: #e9ecef;
        }
        .file-item.selected {
            background-color: #007bff;
            color: white;
        }
        .file-item i {
            width: 16px;
            text-align: center;
        }
        .file-content {
            max-height: 70vh;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            background-color: #f8f9fa;
        }
        .file-info {
            background-color: #e9ecef;
            padding: 0.5rem 1rem;
            border-bottom: 1px solid #dee2e6;
            font-size: 0.875rem;
        }
        .code-editor {
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            padding: 1rem;
            resize: none;
            outline: none;
        }
        .json-content {
            background-color: #f8f9fa;
            border: none;
            padding: 1rem;
            margin: 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .markdown-content {
            padding: 1rem;
            background-color: white;
        }
        .markdown-content h1,
        .markdown-content h2,
        .markdown-content h3,
        .markdown-content h4,
        .markdown-content h5,
        .markdown-content h6 {
            margin-top: 1.5rem;
            margin-bottom: 0.5rem;
        }
        .markdown-content h1:first-child,
        .markdown-content h2:first-child,
        .markdown-content h3:first-child {
            margin-top: 0;
        }
        .markdown-content pre {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            overflow-x: auto;
        }
        .markdown-content code {
            background-color: #f8f9fa;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-size: 0.875em;
        }
        .markdown-content pre code {
            background-color: transparent;
            padding: 0;
        }
        .loading {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-tasks"></i> AI任务管理系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">
                            <i class="fas fa-home"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="projects.html">
                            <i class="fas fa-folder"></i> 项目管理
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.html">
                                <i class="fas fa-tachometer-alt"></i> 仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="projects.html">
                                <i class="fas fa-folder-open"></i> 项目列表
                            </a>
                        </li>
                    </ul>
                    
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>快速操作</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="FileManager.refreshFileTree()">
                                <i class="fas fa-sync-alt"></i> 刷新文件
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="pt-3">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">
                            <a href="projects.html" class="text-decoration-none text-muted">
                                <i class="fas fa-arrow-left"></i>
                            </a>
                            <span id="project-name">项目名称</span> - 文件浏览
                        </h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <div class="btn-group me-2">
                                <button type="button" class="btn btn-outline-info" id="view-tasks-btn">
                                    <i class="fas fa-tasks"></i> 任务列表
                                </button>
                                <button type="button" class="btn btn-outline-secondary" id="refresh-files-btn">
                                    <i class="fas fa-sync-alt"></i> 刷新
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 项目信息 -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-body py-2">
                                    <div class="row align-items-center">
                                        <div class="col-md-6">
                                            <strong>项目ID:</strong> <span id="project-id">-</span>
                                        </div>
                                        <div class="col-md-6 text-end">
                                            <small class="text-muted">
                                                <i class="fas fa-info-circle"></i>
                                                点击左侧文件进行预览
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 文件浏览器 -->
                    <div class="row">
                        <!-- 文件树 -->
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-folder-tree"></i> 文件结构
                                    </h6>
                                </div>
                                <div class="card-body p-0">
                                    <div class="file-tree" id="fileTree">
                                        <div class="loading">
                                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 文件内容 -->
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-file-alt"></i> 文件内容<span id="fileName"></span>
                                    </h6>
                                </div>
                                <div class="card-body p-0">
                                    <div class="file-content" id="fileContent">
                                        <div class="loading">
                                            <i class="fas fa-info-circle"></i> 请选择左侧文件进行预览
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    
    <!-- 自定义JS -->
    <script src="static/js/common.js"></script>
    <script src="static/js/files.js"></script>
    
    <script>
        // 页面初始化
        $(document).ready(function() {
            const projectId = Router.getParam('id');
            if (!projectId) {
                Utils.showAlert('缺少项目ID参数', 'danger');
                Router.navigate('projects.html');
                return;
            }
            
            FileManager.initFilesPage(projectId);
            
            // 绑定任务列表按钮
            $('#view-tasks-btn').on('click', function() {
                Router.navigate(`project_tasks.html?id=${projectId}`);
            });
        });
    </script>
</body>
</html>
