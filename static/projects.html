<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目管理 - AI任务管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }

        .main-content {
            min-height: 100vh;
        }

        .card {
            transition: transform 0.2s;
        }

        .card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>

<body>

    <!-- 主内容区域 -->
    <div
        class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
                <button type="button" class="btn btn-primary" id="create-project-btn">
                    <i class="fas fa-plus"></i> 新建项目
                </button>
                <button type="button" class="btn btn-outline-secondary" id="refresh-projects-btn">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
        </div>
    </div>

    <!-- 项目列表容器 -->
    <div id="projects-container">
        <div class="text-center py-5">
            <i class="fas fa-spinner fa-spin fa-3x text-muted mb-3"></i>
            <div>加载中...</div>
        </div>
    </div>

    <!-- 创建项目模态框 -->
    <div class="modal fade" id="createProjectModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建新项目</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createProjectForm">
                        <div class="mb-3">
                            <label for="createProjectName" class="form-label">项目名称 <span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="createProjectName" required>
                        </div>
                        <div class="mb-3">
                            <label for="createProjectWorkDir" class="form-label">工作目录 <span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="createProjectWorkDir" required>
                            <div class="form-text">项目文件将存储在此目录中</div>
                        </div>
                        <div class="mb-3">
                            <label for="createProjectProvider" class="form-label">LLM Provider</label>
                            <select class="form-control" id="createProjectProvider">
                                <option value="local">内网</option>
                                <option value="zhipu">智谱</option>
                                <option value="claude">Claude</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="createProjectTaskType" class="form-label">任务类型</label>
                            <select class="form-control" id="createProjectTaskType">
                                <option value="新功能">新功能</option>
                                <option value="代码重构">代码重构</option>
                                <option value="PMO需求">PMO需求</option>
                                <option value="代码分析">代码分析</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="createProjectRequirement" class="form-label">项目需求</label>
                            <textarea class="form-control" id="createProjectRequirement" rows="6"
                                placeholder="支持Markdown语法"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="createProjectRulesConstraint" class="form-label">规则约束 (Markdown格式)</label>
                            <textarea class="form-control" id="createProjectRulesConstraint" rows="6"
                                placeholder="支持Markdown语法，例如代码规范、架构约束等"></textarea>
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i>
                                规则约束将被写入到项目目录的CLAUDE.md文件中
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="ProjectManager.submitCreate()">创建项目</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑项目模态框 -->
    <div class="modal fade" id="editProjectModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑项目</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editProjectForm">
                        <input type="hidden" id="editProjectId">
                        <div class="mb-3">
                            <label for="editProjectName" class="form-label">项目名称 <span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editProjectName" required>
                        </div>
                        <div class="mb-3">
                            <label for="editProjectDescription" class="form-label">项目描述</label>
                            <textarea class="form-control" id="editProjectDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="editProjectWorkDir" class="form-label">工作目录 <span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editProjectWorkDir" required>
                        </div>
                        <div class="mb-3">
                            <label for="editProjectProvider" class="form-label">LLM Provider</label>
                            <select class="form-control" id="editProjectProvider">
                                <option value="local">内网</option>
                                <option value="zhipu">智谱</option>
                                <option value="claude">Claude</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editProjectTaskType" class="form-label">任务类型</label>
                            <select class="form-control" id="editProjectTaskType">
                                <option value="新功能">新功能</option>
                                <option value="代码重构">代码重构</option>
                                <option value="PMO需求">PMO需求</option>
                                <option value="代码分析">代码分析</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editProjectRequirement" class="form-label">项目需求</label>
                            <textarea class="form-control" id="editProjectRequirement" rows="6"
                                placeholder="支持Markdown语法"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="editProjectRulesConstraint" class="form-label">规则约束 (Markdown格式)</label>
                            <textarea class="form-control" id="editProjectRulesConstraint" rows="6"
                                placeholder="支持Markdown语法，例如代码规范、架构约束等"></textarea>
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i>
                                规则约束将被写入到项目目录的.claude/CLAUDE.md文件中
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="ProjectManager.submitEdit()">保存修改</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目详情模态框 -->
    <div class="modal fade" id="projectDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">项目详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="projectDetailContent">
                        <!-- 项目详情内容将通过JavaScript加载 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="editProjectFromDetail()">编辑项目</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 生成任务确认模态框 -->
    <div class="modal fade" id="generateTasksModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">生成任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="generateTasksContent">
                        <!-- 内容将通过JavaScript加载 -->
                    </div>
                    <div class="mt-3" id="taskCountSection" style="display: none;">
                        <label for="taskCount" class="form-label">任务数量 (可选)</label>
                        <input type="number" class="form-control" id="taskCount" min="1" max="20"
                            placeholder="留空则自动确定任务数量">
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            建议生成5-10个任务，留空将根据需求自动确定
                        </div>
                    </div>
                    <input type="hidden" id="generateTasksProjectId">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary"
                        onclick="ProjectManager.confirmGenerateTasks()">确认生成</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

    <!-- 自定义JS -->
    <script src="static/js/common.js"></script>
    <script src="static/js/projects.js"></script>

    <script>
        // 页面初始化
        $(document).ready(function () {
            ProjectManager.initProjectsPage();
        });

        function editProjectFromDetail() {
            const projectId = $('#projectDetailModal').data('project-id');
            $('#projectDetailModal').modal('hide');
            setTimeout(() => ProjectManager.showEditModal(projectId), 300);
        }
    </script>
</body>

</html>