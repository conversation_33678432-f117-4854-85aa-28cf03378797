<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库管理 - AI任务管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background-color: #f8f9fa;
        }

        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }

        .sidebar .nav-link {
            font-weight: 500;
            color: #333;
            padding: 0.5rem 1rem;
        }

        .sidebar .nav-link:hover {
            color: #007bff;
            background-color: rgba(0, 123, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: #007bff;
            background-color: rgba(0, 123, 255, 0.1);
        }

        .sidebar-heading {
            font-size: .75rem;
            text-transform: uppercase;
        }

        .main-content {
            margin-left: 240px;
            padding: 20px;
        }

        .kb-card {
            transition: transform 0.2s;
            cursor: pointer;
        }

        .kb-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .kb-stats {
            font-size: 0.9rem;
            color: #6c757d;
        }

        .create-kb-card {
            border: 2px dashed #dee2e6;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 200px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .create-kb-card:hover {
            border-color: #007bff;
            background-color: rgba(0, 123, 255, 0.05);
        }

        .create-kb-icon {
            font-size: 3rem;
            color: #6c757d;
            margin-bottom: 1rem;
        }
    </style>
</head>

<body>
    <!-- 主内容区域 -->
    <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
        <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">知识库管理</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="loadKnowledgeBases()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <button type="button" class="btn btn-sm btn-primary" onclick="showCreateKnowledgeBaseModal()">
                        <i class="fas fa-plus"></i> 新建知识库
                    </button>
                </div>
            </div>
        </div>

        <!-- 项目选择器 -->
        <div class="row mb-3">
            <div class="col-md-6">
                <label for="projectSelector" class="form-label">选择项目</label>
                <select class="form-select" id="projectSelector" onchange="onProjectChange()">
                    <option value="">请选择项目...</option>
                </select>
            </div>
        </div>

        <!-- 知识库列表 -->
        <div id="knowledgeBasesList" class="row">
            <!-- 知识库卡片将在这里动态生成 -->
        </div>

        <!-- 空状态提示 -->
        <div id="emptyState" class="text-center py-5" style="display: none;">
            <i class="fas fa-brain fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">暂无知识库</h4>
            <p class="text-muted">请先选择项目，然后创建您的第一个知识库</p>
            <button class="btn btn-primary" onclick="showCreateKnowledgeBaseModal()">
                <i class="fas fa-plus"></i> 创建知识库
            </button>
        </div>
    </main>

    <!-- 创建知识库模态框 -->
    <div class="modal fade" id="createKnowledgeBaseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建知识库</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createKnowledgeBaseForm">
                        <div class="mb-3">
                            <label for="kbName" class="form-label">知识库名称 *</label>
                            <input type="text" class="form-control" id="kbName" required>
                        </div>
                        <div class="mb-3">
                            <label for="kbDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="kbDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createKnowledgeBase()">创建</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除知识库 "<span id="deleteKbName"></span>" 吗？</p>
                    <p class="text-danger"><small>此操作不可撤销，将删除知识库中的所有文档。</small></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="confirmDeleteKnowledgeBase()">删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- 通用JS -->
    <script src="/static/js/common.js"></script>
    <!-- 知识库管理JS -->
    <script src="/static/js/knowledge.js"></script>

    <script>
        // 页面加载完成后初始化
        $(document).ready(function () {
            // 加载项目列表
            loadProjectSelector();
            // 恢复上次选择的项目
            restoreLastProject();
        });

        // 加载项目选择器
        function loadProjectSelector() {
            API.projects.list()
                .then(response => {
                    if (response && Array.isArray(response)) {
                        const selector = document.getElementById('projectSelector');
                        selector.innerHTML = '<option value="">请选择项目...</option>';

                        response.forEach(project => {
                            const option = document.createElement('option');
                            option.value = project.project_id;
                            option.textContent = project.name;
                            selector.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('加载项目列表失败:', error);
                    Utils.showAlert('加载项目列表失败', 'error');
                });
        }

        // 项目选择改变时的处理
        function onProjectChange() {
            const selector = document.getElementById('projectSelector');
            currentProjectId = selector.value;

            if (currentProjectId) {
                localStorage.setItem('currentProjectId', currentProjectId);
                loadKnowledgeBases();
            } else {
                localStorage.removeItem('currentProjectId');
                // 显示空状态
                const container = document.getElementById('knowledgeBasesList');
                const emptyState = document.getElementById('emptyState');
                if (container) container.innerHTML = '';
                if (emptyState) emptyState.style.display = 'block';
            }
        }

        // 恢复上次选择的项目
        function restoreLastProject() {
            const lastProjectId = localStorage.getItem('currentProjectId');
            if (lastProjectId) {
                const selector = document.getElementById('projectSelector');
                // 等待项目列表加载完成后再设置
                setTimeout(() => {
                    if (selector.querySelector(`option[value="${lastProjectId}"]`)) {
                        selector.value = lastProjectId;
                        currentProjectId = lastProjectId;
                        loadKnowledgeBases();
                    }
                }, 100);
            }
        }
    </script>
</body>

</html>