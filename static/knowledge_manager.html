<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库管理 - AI任务管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background-color: #f8f9fa;
        }

        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }

        .sidebar .nav-link {
            font-weight: 500;
            color: #333;
            padding: 0.5rem 1rem;
        }

        .sidebar .nav-link:hover {
            color: #007bff;
            background-color: rgba(0, 123, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: #007bff;
            background-color: rgba(0, 123, 255, 0.1);
        }

        .sidebar-heading {
            font-size: .75rem;
            text-transform: uppercase;
        }

        .main-content {
            margin-left: 240px;
            padding: 20px;
        }

        .kb-card {
            transition: transform 0.2s;
            cursor: pointer;
        }

        .kb-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .kb-stats {
            font-size: 0.9rem;
            color: #6c757d;
        }

        .create-kb-card {
            border: 2px dashed #dee2e6;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 200px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .create-kb-card:hover {
            border-color: #007bff;
            background-color: rgba(0, 123, 255, 0.05);
        }

        .create-kb-icon {
            font-size: 3rem;
            color: #6c757d;
            margin-bottom: 1rem;
        }
    </style>
</head>

<body>
    <!-- 主内容区域 -->
    <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
        <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">知识库管理</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="loadKnowledgeBase()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
            </div>
        </div>

        <!-- 项目选择器 -->
        <div class="row mb-3">
            <div class="col-md-6">
                <label for="projectSelector" class="form-label">选择项目</label>
                <select class="form-select" id="projectSelector" onchange="onProjectChange()">
                    <option value="">请选择项目...</option>
                </select>
            </div>
        </div>

        <!-- 知识库信息 -->
        <div id="knowledgeBaseInfo" class="card mb-4" style="display: none;">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-brain"></i>
                    <span id="kbName">知识库</span>
                </h5>
            </div>
            <div class="card-body">
                <p id="kbDescription" class="text-muted">知识库描述</p>
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="fas fa-file-alt"></i>
                            <span id="documentCount">0 个文档</span>
                        </small>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="fas fa-calendar"></i>
                            <span id="kbCreatedAt">创建时间</span>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文档管理区域 -->
        <div id="documentManagement" style="display: none;">
            <!-- 添加文档 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-plus"></i> 添加文档
                    </h6>
                </div>
                <div class="card-body">
                    <form id="addDocumentForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="docTitle" class="form-label">文档标题 *</label>
                                    <input type="text" class="form-control" id="docTitle" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="docCategory" class="form-label">分类</label>
                                    <input type="text" class="form-control" id="docCategory" placeholder="可选">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="docContent" class="form-label">文档内容 *</label>
                            <textarea class="form-control" id="docContent" rows="6" required></textarea>
                        </div>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-primary" onclick="addDocument()">
                                <i class="fas fa-plus"></i> 添加文档
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="clearDocumentForm()">
                                <i class="fas fa-eraser"></i> 清空
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 文档搜索 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-search"></i> 文档检索
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="searchQuery" class="form-label">搜索关键词</label>
                                <input type="text" class="form-control" id="searchQuery" placeholder="输入要搜索的内容...">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="searchLimit" class="form-label">结果数量</label>
                                <select class="form-select" id="searchLimit">
                                    <option value="5">5个结果</option>
                                    <option value="10" selected>10个结果</option>
                                    <option value="20">20个结果</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-primary" onclick="searchDocuments()">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
            </div>

            <!-- 搜索结果 -->
            <div id="searchResults" class="card" style="display: none;">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-list"></i> 搜索结果
                    </h6>
                </div>
                <div class="card-body">
                    <div id="searchResultsList">
                        <!-- 搜索结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 空状态提示 -->
        <div id="emptyState" class="text-center py-5" style="display: none;">
            <i class="fas fa-brain fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">请选择项目</h4>
            <p class="text-muted">选择一个项目来管理其知识库</p>
        </div>
    </main>



    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- 通用JS -->
    <script src="/static/js/common.js"></script>
    <!-- 知识库管理JS -->
    <script src="/static/js/knowledge.js"></script>

    <script>
        // 页面加载完成后初始化
        $(document).ready(function () {
            // 加载项目列表
            loadProjectSelector();
            // 恢复上次选择的项目
            restoreLastProject();
        });

        // 加载项目选择器
        function loadProjectSelector() {
            API.projects.list()
                .then(response => {
                    if (response && Array.isArray(response)) {
                        const selector = document.getElementById('projectSelector');
                        selector.innerHTML = '<option value="">请选择项目...</option>';

                        response.forEach(project => {
                            const option = document.createElement('option');
                            option.value = project.project_id;
                            option.textContent = project.name;
                            selector.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('加载项目列表失败:', error);
                    Utils.showAlert('加载项目列表失败', 'error');
                });
        }

        // 项目选择改变时的处理
        function onProjectChange() {
            const selector = document.getElementById('projectSelector');
            currentProjectId = selector.value;

            if (currentProjectId) {
                localStorage.setItem('currentProjectId', currentProjectId);
                loadKnowledgeBase();
            } else {
                localStorage.removeItem('currentProjectId');
                showEmptyState();
            }
        }

        // 加载知识库信息
        function loadKnowledgeBase() {
            if (!currentProjectId) {
                showEmptyState();
                return;
            }

            API.request({
                url: `/api/projects/${currentProjectId}/knowledge_bases`,
                method: 'GET'
            })
            .then(response => {
                if (response.success && response.knowledge_base) {
                    currentKnowledgeBaseId = response.knowledge_base.kb_id;
                    showKnowledgeBase(response.knowledge_base);
                } else {
                    Utils.showAlert(response.message || '加载知识库失败', 'error');
                    showEmptyState();
                }
            })
            .catch(error => {
                console.error('加载知识库失败:', error);
                Utils.showAlert('加载知识库失败', 'error');
                showEmptyState();
            });
        }

        // 显示知识库信息
        function showKnowledgeBase(kb) {
            // 隐藏空状态
            document.getElementById('emptyState').style.display = 'none';

            // 显示知识库信息
            const kbInfo = document.getElementById('knowledgeBaseInfo');
            const docManagement = document.getElementById('documentManagement');

            document.getElementById('kbName').textContent = kb.name;
            document.getElementById('kbDescription').textContent = kb.description || '暂无描述';
            document.getElementById('documentCount').textContent = `${kb.document_count || 0} 个文档`;
            document.getElementById('kbCreatedAt').textContent = `创建于 ${new Date(kb.created_at).toLocaleDateString('zh-CN')}`;

            kbInfo.style.display = 'block';
            docManagement.style.display = 'block';
        }

        // 显示空状态
        function showEmptyState() {
            document.getElementById('emptyState').style.display = 'block';
            document.getElementById('knowledgeBaseInfo').style.display = 'none';
            document.getElementById('documentManagement').style.display = 'none';
        }

        // 恢复上次选择的项目
        function restoreLastProject() {
            const lastProjectId = localStorage.getItem('currentProjectId');
            if (lastProjectId) {
                const selector = document.getElementById('projectSelector');
                // 等待项目列表加载完成后再设置
                setTimeout(() => {
                    if (selector.querySelector(`option[value="${lastProjectId}"]`)) {
                        selector.value = lastProjectId;
                        currentProjectId = lastProjectId;
                        loadKnowledgeBase();
                    }
                }, 100);
            }
        }

        // 添加文档
        function addDocument() {
            if (!currentKnowledgeBaseId) {
                Utils.showAlert('知识库ID不存在', 'error');
                return;
            }

            const title = document.getElementById('docTitle').value.trim();
            const content = document.getElementById('docContent').value.trim();
            const category = document.getElementById('docCategory').value.trim();

            if (!title || !content) {
                Utils.showAlert('请输入文档标题和内容', 'warning');
                return;
            }

            const data = {
                title: title,
                content: content,
                metadata: {
                    category: category || '未分类'
                }
            };

            API.request({
                url: `/api/knowledge_bases/${currentKnowledgeBaseId}/documents`,
                method: 'POST',
                data: data
            })
            .then(response => {
                if (response.success) {
                    Utils.showAlert('文档添加成功', 'success');
                    clearDocumentForm();
                    // 重新加载知识库信息以更新文档数量
                    loadKnowledgeBase();
                } else {
                    Utils.showAlert(response.message || '添加文档失败', 'error');
                }
            })
            .catch(error => {
                console.error('添加文档失败:', error);
                Utils.showAlert('添加文档失败', 'error');
            });
        }

        // 清空文档表单
        function clearDocumentForm() {
            document.getElementById('addDocumentForm').reset();
        }

        // 搜索文档
        function searchDocuments() {
            if (!currentKnowledgeBaseId) {
                Utils.showAlert('知识库ID不存在', 'error');
                return;
            }

            const query = document.getElementById('searchQuery').value.trim();
            const limit = parseInt(document.getElementById('searchLimit').value) || 10;

            if (!query) {
                Utils.showAlert('请输入搜索关键词', 'warning');
                return;
            }

            const data = {
                query: query,
                limit: limit
            };

            API.request({
                url: `/api/knowledge_bases/${currentKnowledgeBaseId}/search`,
                method: 'POST',
                data: data
            })
            .then(response => {
                if (response.success) {
                    renderSearchResults(response.documents, query);
                } else {
                    Utils.showAlert(response.message || '搜索失败', 'error');
                }
            })
            .catch(error => {
                console.error('搜索失败:', error);
                Utils.showAlert('搜索失败', 'error');
            });
        }

        // 渲染搜索结果
        function renderSearchResults(documents, query) {
            const resultsContainer = document.getElementById('searchResults');
            const resultsList = document.getElementById('searchResultsList');

            if (!resultsContainer || !resultsList) return;

            if (documents.length === 0) {
                resultsList.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-search fa-2x text-muted mb-3"></i>
                        <p class="text-muted">没有找到相关文档</p>
                    </div>
                `;
            } else {
                let html = '';
                documents.forEach((doc, index) => {
                    const similarity = (1 - doc.score / 100).toFixed(3);
                    html += `
                        <div class="border rounded p-3 mb-3">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-0">${Utils.escapeHtml(doc.title)}</h6>
                                <span class="badge bg-primary">相似度: ${similarity}</span>
                            </div>
                            <div class="document-content mb-2">
                                ${Utils.escapeHtml(doc.content)}
                            </div>
                            ${doc.metadata && doc.metadata.category ?
                                `<small class="text-muted"><i class="fas fa-tag"></i> ${Utils.escapeHtml(doc.metadata.category)}</small>` :
                                ''
                            }
                        </div>
                    `;
                });
                resultsList.innerHTML = html;
            }

            resultsContainer.style.display = 'block';
            // 滚动到搜索结果
            resultsContainer.scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>

</html>