/**
 * 通用JavaScript函数库
 * 用于前后端分离的AI任务管理系统
 */

// 全局变量
window.AppConfig = {
    apiBase: '/api',
    currentProject: null,
    currentUser: null
};

// 通用工具函数
window.Utils = {
    /**
     * 显示提示消息
     */
    showAlert: function(message, type = 'info', duration = 5000) {
        const alertDiv = $(`
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);
        
        // 查找alert容器，如果没有则创建
        let container = $('#alert-container');
        if (container.length === 0) {
            container = $('<div id="alert-container" class="position-fixed" style="top: 20px; right: 20px; z-index: 9999; max-width: 400px;"></div>');
            $('body').append(container);
        }
        
        container.prepend(alertDiv);
        
        // 自动消失
        if (duration > 0) {
            setTimeout(() => {
                alertDiv.alert('close');
            }, duration);
        }
    },

    /**
     * 格式化日期时间
     */
    formatDateTime: function(isoString) {
        if (!isoString) return '';
        const date = new Date(isoString);
        return date.toLocaleString('zh-CN');
    },

    /**
     * 获取状态徽章HTML
     */
    getStatusBadge: function(status) {
        const statusMap = {
            'pending': { class: 'secondary', text: '待处理' },
            'in_progress': { class: 'primary', text: '进行中' },
            'running': { class: 'info', text: '运行中' },
            'completed': { class: 'success', text: '已完成' },
            'failed': { class: 'danger', text: '失败' },
            'cancelled': { class: 'warning', text: '已取消' },
            'stopped': { class: 'warning', text: '已停止' }
        };
        const info = statusMap[status] || { class: 'secondary', text: status };
        return `<span class="badge bg-${info.class}">${info.text}</span>`;
    },

    /**
     * 获取优先级徽章HTML
     */
    getPriorityBadge: function(priority) {
        const priorityMap = {
            'high': { class: 'danger', text: '高' },
            'medium': { class: 'warning', text: '中' },
            'low': { class: 'secondary', text: '低' }
        };
        const info = priorityMap[priority] || { class: 'secondary', text: priority };
        return `<span class="badge bg-${info.class}">${info.text}</span>`;
    },

    /**
     * 获取Provider徽章HTML
     */
    getProviderBadge: function(provider) {
        const providerMap = {
            'local': { class: 'secondary', text: '内网' },
            'zhipu': { class: 'info text-dark', text: '智谱' },
            'claude': { class: 'primary', text: 'Claude' }
        };
        const info = providerMap[provider] || { class: 'light text-dark', text: provider };
        return `<span class="badge bg-${info.class}">${info.text}</span>`;
    },

    /**
     * 获取任务类型徽章HTML
     */
    getTaskTypeBadge: function(taskType) {
        const typeMap = {
            '代码重构': { class: 'warning', text: '代码重构' },
            'PMO需求': { class: 'info', text: 'PMO需求' },
            '新功能': { class: 'success', text: '新功能' },
            '代码分析': { class: 'danger', text: '代码分析' }
        };
        const info = typeMap[taskType] || { class: 'secondary', text: taskType };
        return `<span class="badge bg-${info.class}">${info.text}</span>`;
    },

    /**
     * 格式化文件大小
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    },

    /**
     * 获取文件图标
     */
    getFileIcon: function(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        const iconMap = {
            'js': 'fab fa-js-square text-warning',
            'py': 'fab fa-python text-primary',
            'java': 'fab fa-java text-danger',
            'html': 'fab fa-html5 text-danger',
            'css': 'fab fa-css3-alt text-primary',
            'json': 'fas fa-code text-success',
            'md': 'fab fa-markdown text-dark',
            'txt': 'fas fa-file-alt text-secondary',
            'xml': 'fas fa-code text-warning',
            'yml': 'fas fa-cog text-info',
            'yaml': 'fas fa-cog text-info',
            'c': 'fas fa-code text-primary',
            'cpp': 'fas fa-code text-primary',
            'h': 'fas fa-code text-info'
        };
        return iconMap[ext] || 'fas fa-file text-secondary';
    },

    /**
     * 转义HTML
     */
    escapeHtml: function(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    /**
     * 设置按钮加载状态
     */
    setButtonLoading: function(button, loading = true, loadingText = '处理中...') {
        const $btn = $(button);
        if (loading) {
            $btn.data('original-html', $btn.html());
            $btn.prop('disabled', true).html(`<i class="fas fa-spinner fa-spin"></i> ${loadingText}`);
        } else {
            $btn.prop('disabled', false).html($btn.data('original-html') || $btn.html());
        }
    },

    /**
     * 确认对话框
     */
    confirm: function(message, callback) {
        if (confirm(message)) {
            callback();
        }
    }
};

// API调用封装
window.API = {
    /**
     * 通用AJAX请求
     */
    request: function(options) {
        const defaults = {
            method: 'GET',
            contentType: 'application/json',
            dataType: 'json'
        };
        
        const config = $.extend({}, defaults, options);
        
        // 如果是POST/PUT/DELETE且有data，转换为JSON字符串
        if (config.data && typeof config.data === 'object' && 
            ['POST', 'PUT', 'DELETE'].includes(config.method.toUpperCase())) {
            config.data = JSON.stringify(config.data);
        }
        
        return $.ajax(config);
    },

    /**
     * 项目相关API
     */
    projects: {
        list: () => API.request({ url: '/api/projects' }),
        get: (id) => API.request({ url: `/api/projects/${id}` }),
        create: (data) => API.request({ url: '/api/projects', method: 'POST', data }),
        update: (id, data) => API.request({ url: `/api/projects/${id}`, method: 'PUT', data }),
        delete: (id) => API.request({ url: `/api/projects/${id}`, method: 'DELETE' }),
        summary: (id) => API.request({ url: `/api/projects/${id}/summary` }),
        generateTasks: (id, data) => API.request({ url: `/api/projects/${id}/generate_tasks`, method: 'POST', data }),
        runTasks: (id, data) => API.request({ url: `/api/projects/${id}/run_tasks`, method: 'POST', data }),
        stopExecution: (id) => API.request({ url: `/api/projects/${id}/stop_execution`, method: 'POST' }),
        resetTasks: (id) => API.request({ url: `/api/projects/${id}/tasks/reset`, method: 'POST' })
    },

    /**
     * 任务相关API
     */
    tasks: {
        list: (projectId) => API.request({ url: `/api/projects/${projectId}/tasks` }),
        get: (projectId, taskId) => API.request({ url: `/api/projects/${projectId}/tasks/${taskId}` }),
        create: (projectId, data) => API.request({ url: `/api/projects/${projectId}/tasks`, method: 'POST', data }),
        update: (projectId, taskId, data) => API.request({ url: `/api/projects/${projectId}/tasks/${taskId}`, method: 'PUT', data }),
        delete: (projectId, taskId) => API.request({ url: `/api/projects/${projectId}/tasks/${taskId}`, method: 'DELETE' }),
        run: (projectId, taskId) => API.request({ url: `/api/projects/${projectId}/tasks/${taskId}/run`, method: 'POST' }),
        logs: (projectId, taskId) => API.request({ url: `/api/projects/${projectId}/tasks/${taskId}/logs` }),
        llmLogs: (projectId, taskId, since) => {
            const url = since ? 
                `/api/projects/${projectId}/tasks/${taskId}/llm-logs?since=${encodeURIComponent(since)}` :
                `/api/projects/${projectId}/tasks/${taskId}/llm-logs`;
            return API.request({ url });
        }
    },

    /**
     * 文件相关API
     */
    files: {
        list: (projectId) => API.request({ url: `/api/projects/${projectId}/files` }),
        content: (projectId, path) => API.request({ url: `/api/projects/${projectId}/files/content?path=${encodeURIComponent(path)}` }),
        stats: (projectId) => API.request({ url: `/api/projects/${projectId}/files/stats` })
    }
};

// 页面路由管理
window.Router = {
    /**
     * 导航到指定页面
     */
    navigate: function(path) {
        window.location.href = path;
    },

    /**
     * 获取URL参数
     */
    getParam: function(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    },

    /**
     * 获取路径参数
     */
    getPathParam: function(index) {
        const pathParts = window.location.pathname.split('/').filter(part => part);
        return pathParts[index] || null;
    }
};

// 页面加载完成后的初始化
$(document).ready(function() {
    // 初始化提示框容器
    if ($('#alert-container').length === 0) {
        $('body').append('<div id="alert-container" class="position-fixed" style="top: 20px; right: 20px; z-index: 9999; max-width: 400px;"></div>');
    }
});
