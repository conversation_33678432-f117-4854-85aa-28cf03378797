/**
 * 任务管理相关JavaScript
 */

// 任务管理模块
window.TaskManager = {
    currentProjectId: null,
    currentProject: null,

    /**
     * 初始化任务页面
     */
    initTasksPage: function(projectId) {
        this.currentProjectId = projectId;
        this.loadProject();
        this.loadTasks();
        this.bindEvents();
    },

    /**
     * 加载项目信息
     */
    loadProject: function() {
        API.projects.get(this.currentProjectId)
            .done(function(project) {
                TaskManager.currentProject = project;
                TaskManager.renderProjectInfo(project);
            })
            .fail(function(xhr) {
                const response = xhr.responseJSON || {};
                Utils.showAlert('加载项目信息失败: ' + (response.message || '未知错误'), 'danger');
            });
    },

    /**
     * 渲染项目信息
     */
    renderProjectInfo: function(project) {
        $('#project-provider').html(Utils.getProviderBadge(project.provider));
        $('#project-created-at').text(Utils.formatDateTime(project.created_at));
        $('#project-run-state').html(project.run_state ? 
            '<span class="badge bg-success">运行中</span>' : 
            '<span class="badge bg-secondary">未运行</span>');
        
        if (project.requirement) {
            $('#project-requirement').html(marked.parse(project.requirement));
            $('#requirement-section').show();
        } else {
            $('#requirement-section').hide();
        }
    },

    /**
     * 加载任务列表
     */
    loadTasks: function() {
        API.tasks.list(this.currentProjectId)
            .done(function(response) {
                TaskManager.renderTasksList(response.tasks || []);
                TaskManager.updateTaskStats(response.tasks || []);
            })
            .fail(function(xhr) {
                const response = xhr.responseJSON || {};
                Utils.showAlert('加载任务列表失败: ' + (response.error || '未知错误'), 'danger');
            });
    },

    /**
     * 渲染任务列表
     */
    renderTasksList: function(tasks) {
        const container = $('#tasks-container');
        
        if (tasks.length === 0) {
            container.html(`
                <div class="text-center py-5">
                    <i class="fas fa-tasks fa-5x text-muted mb-4"></i>
                    <h3 class="text-muted">还没有任务</h3>
                    <p class="text-muted mb-4">为项目生成任务来开始工作</p>
                    <button class="btn btn-primary btn-lg" onclick="TaskManager.generateTasks()">
                        <i class="fas fa-cogs"></i> 生成任务
                    </button>
                </div>
            `);
            return;
        }

        let html = `
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-tasks"></i> 任务列表</h6>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 5%">#</th>
                                    <th style="width: 25%">任务标题</th>
                                    <th style="width: 30%">任务描述</th>
                                    <th style="width: 10%">状态</th>
                                    <th style="width: 10%">优先级</th>
                                    <th style="width: 10%">执行时间</th>
                                    <th style="width: 10%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
        `;

        tasks.forEach((task, index) => {
            const executionTime = task.execution_time ? 
                (task.execution_time < 60 ? 
                    `${task.execution_time.toFixed(2)}秒` : 
                    `${(task.execution_time / 60).toFixed(1)}分钟`) : 
                '-';

            html += `
                <tr>
                    <td>${index + 1}</td>
                    <td>
                        <div class="fw-bold">${task.title || task.name || 'N/A'}</div>
                        ${task.dependencies && task.dependencies.length > 0 ? 
                            `<small class="text-muted"><i class="fas fa-link"></i> 依赖: ${task.dependencies.join(', ')}</small>` : 
                            ''}
                    </td>
                    <td>
                        <small>${(task.description || '').substring(0, 100)}${(task.description || '').length > 100 ? '...' : ''}</small>
                    </td>
                    <td>${Utils.getStatusBadge(task.status || 'pending')}</td>
                    <td>${Utils.getPriorityBadge(task.priority || 'medium')}</td>
                    <td><span class="text-muted">${executionTime}</span></td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            ${task.status === 'pending' ? `
                                <button type="button" class="btn btn-outline-primary"
                                        onclick="TaskManager.editTask('${task.id}')" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger"
                                        onclick="TaskManager.deleteTask('${task.id}')" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            ` : ''}
                            <button type="button" class="btn btn-outline-info"
                                    onclick="TaskManager.viewDetail('${task.id}')" title="详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button type="button" class="btn btn-outline-success"
                                    onclick="TaskManager.runTask('${task.id}')" title="运行">
                                <i class="fas fa-play"></i>
                            </button>
                            ${task.status === 'completed' || task.status === 'failed' ? `
                            <button type="button" class="btn btn-outline-warning"
                                    onclick="TaskManager.continueTask('${task.id}')" title="继续任务">
                                <i class="fas fa-redo"></i>
                            </button>
                            ` : ''}
                            <button type="button" class="btn btn-outline-warning"
                                    onclick="TaskManager.viewLLMLogs('${task.id}')" title="LLM日志">
                                <i class="fas fa-file-alt"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        container.html(html);
    },

    /**
     * 更新任务统计
     */
    updateTaskStats: function(tasks) {
        const total = tasks.length;
        const completed = tasks.filter(t => t.status === 'completed').length;
        const inProgress = tasks.filter(t => t.status === 'in_progress').length;
        const failed = tasks.filter(t => t.status === 'failed').length;

        $('#total-tasks').text(total);
        $('#completed-tasks').text(completed);
        $('#in-progress-tasks').text(inProgress);
        $('#failed-tasks').text(failed);
    },

    /**
     * 绑定事件
     */
    bindEvents: function() {
        // 各种按钮事件
        $(document).on('click', '#refresh-tasks-btn', () => this.loadTasks());
        $(document).on('click', '#add-task-btn', () => this.showAddTaskModal());
        $(document).on('click', '#run-tasks-btn', () => this.runAllTasks());
        $(document).on('click', '#reset-tasks-btn', () => this.resetTasks());
        $(document).on('click', '#stop-execution-btn', () => this.stopExecution());
        $(document).on('click', '#generate-tasks-btn', () => this.generateTasks());
    },

    /**
     * 显示添加任务模态框
     */
    showAddTaskModal: function() {
        $('#addTaskForm')[0].reset();
        $('#addTaskModal').modal('show');
    },

    /**
     * 提交添加任务
     */
    submitAddTask: function() {
        const formData = {
            title: $('#taskTitle').val().trim(),
            description: $('#taskDescription').val().trim(),
            priority: $('#taskPriority').val(),
            dependencies: $('#taskDependencies').val().trim().split(',').map(id => id.trim()).filter(id => id),
            execute_immediately: $('#executeImmediately').is(':checked')
        };

        if (!formData.title) {
            Utils.showAlert('请输入任务标题', 'warning');
            return;
        }

        const submitBtn = $('#addTaskModal .btn-primary');
        Utils.setButtonLoading(submitBtn, true, '添加中...');

        API.tasks.create(this.currentProjectId, formData)
            .done(function(response) {
                if (response.success) {
                    let message = '任务添加成功！';
                    if (formData.execute_immediately && response.execution_result) {
                        if (response.execution_result.success) {
                            message += ' 任务已开始执行。';
                        } else {
                            message += ` 但执行失败: ${response.execution_result.message}`;
                        }
                    }
                    Utils.showAlert(message, 'success');
                    $('#addTaskModal').modal('hide');
                    setTimeout(() => TaskManager.loadTasks(), 1000);
                } else {
                    Utils.showAlert('任务添加失败: ' + response.message, 'danger');
                }
            })
            .fail(function(xhr) {
                const response = xhr.responseJSON || {};
                Utils.showAlert('任务添加失败: ' + (response.error || '未知错误'), 'danger');
            })
            .always(function() {
                Utils.setButtonLoading(submitBtn, false);
            });
    },

    /**
     * 编辑任务
     */
    editTask: function(taskId) {
        API.tasks.list(this.currentProjectId)
            .done(function(response) {
                const task = response.tasks.find(t => t.id == taskId);
                if (task) {
                    TaskManager.showEditTaskModal(task);
                } else {
                    Utils.showAlert('任务不存在', 'danger');
                }
            })
            .fail(function() {
                Utils.showAlert('获取任务信息失败', 'danger');
            });
    },

    /**
     * 显示编辑任务模态框
     */
    showEditTaskModal: function(task) {
        const editModalHtml = `
            <div class="modal fade" id="editTaskModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">编辑任务</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editTaskForm">
                                <div class="mb-3">
                                    <label for="editTaskTitle" class="form-label">任务标题 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editTaskTitle" value="${task.title || task.name || ''}" required>
                                </div>
                                <div class="mb-3">
                                    <label for="editTaskDescription" class="form-label">任务描述</label>
                                    <textarea class="form-control" id="editTaskDescription" rows="4">${task.description || ''}</textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="editTaskDetails" class="form-label">详细信息</label>
                                    <textarea class="form-control" id="editTaskDetails" rows="3" placeholder="任务的详细实现步骤和要求">${task.details || ''}</textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="editTaskTestStrategy" class="form-label">测试策略</label>
                                    <input type="text" class="form-control" id="editTaskTestStrategy" placeholder="如何验证任务完成" value="${task.testStrategy || ''}">
                                </div>
                                <div class="mb-3">
                                    <label for="editTaskPriority" class="form-label">优先级</label>
                                    <select class="form-control" id="editTaskPriority">
                                        <option value="low" ${task.priority === 'low' ? 'selected' : ''}>低</option>
                                        <option value="medium" ${task.priority === 'medium' ? 'selected' : ''}>中</option>
                                        <option value="high" ${task.priority === 'high' ? 'selected' : ''}>高</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="editTaskDependencies" class="form-label">依赖任务 (可选)</label>
                                    <input type="text" class="form-control" id="editTaskDependencies"
                                           value="${task.dependencies ? task.dependencies.join(', ') : ''}"
                                           placeholder="输入依赖的任务ID，多个用逗号分隔">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="TaskManager.submitEditTask('${task.id}')">保存修改</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        $('#editTaskModal').remove();

        // 添加新的模态框并显示
        $('body').append(editModalHtml);
        $('#editTaskModal').modal('show');

        // 模态框关闭后移除
        $('#editTaskModal').on('hidden.bs.modal', function() {
            $(this).remove();
        });
    },

    /**
     * 提交编辑任务
     */
    submitEditTask: function(taskId) {
        const formData = {
            title: $('#editTaskTitle').val().trim(),
            description: $('#editTaskDescription').val().trim(),
            details: $('#editTaskDetails').val().trim(),
            testStrategy: $('#editTaskTestStrategy').val().trim(),
            priority: $('#editTaskPriority').val(),
            dependencies: $('#editTaskDependencies').val().trim().split(',').map(id => id.trim()).filter(id => id)
        };

        if (!formData.title) {
            Utils.showAlert('请输入任务标题', 'warning');
            return;
        }

        const submitBtn = $('#editTaskModal .btn-primary');
        Utils.setButtonLoading(submitBtn, true, '保存中...');

        API.tasks.update(this.currentProjectId, taskId, formData)
            .done(function(response) {
                if (response.success) {
                    Utils.showAlert('任务更新成功！', 'success');
                    $('#editTaskModal').modal('hide');
                    setTimeout(() => TaskManager.loadTasks(), 1000);
                } else {
                    Utils.showAlert('任务更新失败: ' + response.message, 'danger');
                }
            })
            .fail(function(xhr) {
                const response = xhr.responseJSON || {};
                Utils.showAlert('任务更新失败: ' + (response.message || '未知错误'), 'danger');
            })
            .always(function() {
                Utils.setButtonLoading(submitBtn, false);
            });
    },

    /**
     * 删除任务
     */
    deleteTask: function(taskId) {
        Utils.confirm('确定要删除这个任务吗？此操作不可撤销。', function() {
            API.tasks.delete(TaskManager.currentProjectId, taskId)
                .done(function(response) {
                    if (response.success) {
                        Utils.showAlert('任务删除成功！', 'success');
                        setTimeout(() => TaskManager.loadTasks(), 1000);
                    } else {
                        Utils.showAlert('任务删除失败: ' + response.error, 'danger');
                    }
                })
                .fail(function(xhr) {
                    const response = xhr.responseJSON || {};
                    Utils.showAlert('任务删除失败: ' + (response.error || '未知错误'), 'danger');
                });
        });
    },

    /**
     * 运行单个任务
     */
    runTask: function(taskId) {
        Utils.confirm('确定要运行这个任务吗？', function() {
            API.tasks.run(TaskManager.currentProjectId, taskId)
                .done(function(response) {
                    if (response.success) {
                        Utils.showAlert('任务运行成功！', 'success');
                        setTimeout(() => TaskManager.loadTasks(), 2000);
                    } else {
                        Utils.showAlert('任务运行失败: ' + response.message, 'danger');
                    }
                })
                .fail(function(xhr) {
                    const response = xhr.responseJSON || {};
                    Utils.showAlert('任务运行失败: ' + (response.error || '未知错误'), 'danger');
                });
        });
    },

    /**
     * 运行所有任务
     */
    runAllTasks: function() {
        Utils.confirm('确定要运行所有任务吗？', function() {
            API.projects.runTasks(TaskManager.currentProjectId, {})
                .done(function(response) {
                    if (response.success) {
                        Utils.showAlert('任务开始运行！', 'success');
                        setTimeout(() => TaskManager.loadTasks(), 1000);
                    } else {
                        Utils.showAlert('任务运行失败: ' + response.message, 'danger');
                    }
                })
                .fail(function(xhr) {
                    const response = xhr.responseJSON || {};
                    Utils.showAlert('任务运行失败: ' + (response.message || '未知错误'), 'danger');
                });
        });
    },

    /**
     * 停止执行
     */
    stopExecution: function() {
        Utils.confirm('确定要停止所有任务的执行吗？', function() {
            API.projects.stopExecution(TaskManager.currentProjectId)
                .done(function(response) {
                    if (response.success) {
                        Utils.showAlert('已发送停止执行命令！', 'success');
                        setTimeout(() => TaskManager.loadTasks(), 1000);
                    } else {
                        Utils.showAlert('停止执行失败: ' + response.message, 'danger');
                    }
                })
                .fail(function(xhr) {
                    const response = xhr.responseJSON || {};
                    Utils.showAlert('停止执行失败: ' + (response.message || '未知错误'), 'danger');
                });
        });
    },

    /**
     * 重置任务
     */
    resetTasks: function() {
        Utils.confirm('确定要重置所有任务的状态吗？这将清除所有执行结果。', function() {
            API.projects.resetTasks(TaskManager.currentProjectId)
                .done(function(response) {
                    if (response.success) {
                        Utils.showAlert('所有任务已重置！', 'success');
                        setTimeout(() => TaskManager.loadTasks(), 1000);
                    } else {
                        Utils.showAlert('任务重置失败: ' + response.message, 'danger');
                    }
                })
                .fail(function(xhr) {
                    const response = xhr.responseJSON || {};
                    Utils.showAlert('任务重置失败: ' + (response.message || '未知错误'), 'danger');
                });
        });
    },

    /**
     * 生成任务
     */
    generateTasks: function() {
        if (this.currentProjectId) {
            ProjectManager.generateTasks(this.currentProjectId);
        } else {
            Utils.showAlert('项目ID无效', 'danger');
        }
    },

    /**
     * 查看任务详情
     */
    viewDetail: function(taskId) {
        API.tasks.list(this.currentProjectId)
            .done(function(response) {
                const task = response.tasks.find(t => t.id == taskId);
                if (task) {
                    TaskManager.showTaskDetailModal(task);
                } else {
                    Utils.showAlert('任务不存在', 'danger');
                }
            })
            .fail(function() {
                Utils.showAlert('获取任务信息失败', 'danger');
            });
    },

    /**
     * 显示任务详情模态框
     */
    showTaskDetailModal: function(task) {
        const detailHtml = `
            <div class="modal fade" id="taskDetailModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">任务详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <table class="table">
                                <tr><td><strong>任务ID:</strong></td><td>${task.id}</td></tr>
                                <tr><td><strong>标题:</strong></td><td>${task.title || task.name || 'N/A'}</td></tr>
                                <tr><td><strong>描述:</strong></td><td>${task.description || '无'}</td></tr>
                                <tr><td><strong>状态:</strong></td><td>${Utils.getStatusBadge(task.status || '未知')}</td></tr>
                                <tr><td><strong>优先级:</strong></td><td>${Utils.getPriorityBadge(task.priority || '中')}</td></tr>
                                <tr><td><strong>依赖:</strong></td><td>${task.dependencies ? task.dependencies.join(', ') : '无'}</td></tr>
                                <tr><td><strong>创建时间:</strong></td><td>${Utils.formatDateTime(task.created_at) || '未知'}</td></tr>
                                <tr><td><strong>更新时间:</strong></td><td>${Utils.formatDateTime(task.updated_at) || '未知'}</td></tr>
                            </table>
                            ${task.result ? '<h6>执行结果:</h6><pre>' + Utils.escapeHtml(task.result) + '</pre>' : ''}
                            ${task.error_message ? '<h6>错误信息:</h6><pre class="text-danger">' + Utils.escapeHtml(task.error_message) + '</pre>' : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        $('#taskDetailModal').remove();

        // 添加新的模态框并显示
        $('body').append(detailHtml);
        $('#taskDetailModal').modal('show');

        // 模态框关闭后移除
        $('#taskDetailModal').on('hidden.bs.modal', function() {
            $(this).remove();
        });
    },

    /**
     * 查看LLM日志
     */
    viewLLMLogs: function(taskId) {
        Router.navigate(`/task_llm_logs.html?project_id=${this.currentProjectId}&task_id=${taskId}`);
    },

    /**
     * 显示继续任务模态框
     */
    continueTask: function(taskId) {
        // 设置当前任务ID
        $('#continueTaskId').val(taskId);
        // 清空之前的内容
        $('#continueTaskRequest').val('');
        // 显示模态框
        $('#continueTaskModal').modal('show');
    },

    /**
     * 提交继续任务请求
     */
    submitContinueTask: function() {
        const taskId = $('#continueTaskId').val();
        const request = $('#continueTaskRequest').val().trim();

        if (!request) {
            Utils.showAlert('请输入请求内容', 'warning');
            return;
        }

        const submitBtn = $('#continueTaskModal .btn-primary');
        Utils.setButtonLoading(submitBtn, true, '处理中...');

        API.request({
            url: `/api/projects/${this.currentProjectId}/tasks/${taskId}/continue`,
            method: 'POST',
            data: { request: request }
        })
        .done(function(response) {
            if (response.success) {
                Utils.showAlert('任务继续执行成功！', 'success');
                $('#continueTaskModal').modal('hide');
                setTimeout(() => TaskManager.loadTasks(), 2000);
            } else {
                Utils.showAlert('任务继续执行失败: ' + response.message, 'danger');
            }
        })
        .fail(function(xhr) {
            const response = xhr.responseJSON || {};
            Utils.showAlert('任务继续执行失败: ' + (response.error || '未知错误'), 'danger');
        })
        .always(function() {
            Utils.setButtonLoading(submitBtn, false);
        });
    }
};

// 页面加载完成后的初始化
$(document).ready(function() {
    // 绑定继续任务提交事件
    $(document).on('click', '#confirmContinueTask', function() {
        TaskManager.submitContinueTask();
    });
});
