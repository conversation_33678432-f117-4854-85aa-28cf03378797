/**
 * 通用Markdown编辑器组件
 */

class MarkdownEditor {
    constructor(containerId, type, title, showGenerateButton = false, onlyShowSave = false) {
        this.containerId = containerId;
        this.type = type; // 'requirements', 'design', 'rules'
        this.title = title;
        this.showGenerateButton = showGenerateButton;
        this.onlyShowSave = onlyShowSave;
        this.viewMode = 'edit'; // 'edit', 'preview', 'split'
        this.content = '';
        this.init();
    }

    init() {
        this.render();
        this.loadContent();
        this.bindEvents();
    }

    render() {
        const container = document.getElementById(this.containerId);
        if (!container) {
            console.error('Container not found:', this.containerId);
            return;
        }

        container.innerHTML = `
            <div class="markdown-editor">
                <!-- 工具栏 -->
                <div class="editor-toolbar">
                    <div class="toolbar-left">
                        <h5 class="mb-0">${this.title}</h5>
                    </div>
                    <div class="toolbar-right">
                        ${this.renderButtons()}
                        <div class="view-mode-toggle">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-secondary btn-sm" 
                                        onclick="markdownEditor.setViewMode('edit')" 
                                        id="editModeBtn">
                                    <i class="bi bi-pencil"></i> 编辑
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" 
                                        onclick="markdownEditor.setViewMode('preview')" 
                                        id="previewModeBtn">
                                    <i class="bi bi-eye"></i> 预览
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" 
                                        onclick="markdownEditor.setViewMode('split')" 
                                        id="splitModeBtn">
                                    <i class="bi bi-layout-split"></i> 混合
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 编辑区域 -->
                <div class="editor-content">
                    <div class="editor-pane" id="editorPane">
                        <textarea class="form-control editor-textarea" 
                                  id="markdownTextarea" 
                                  placeholder="请输入${this.title}内容..."></textarea>
                    </div>
                    <div class="preview-pane" id="previewPane" style="display: none;">
                        <div class="preview-content" id="previewContent">
                            <div class="text-muted text-center p-4">
                                <i class="bi bi-eye-slash"></i>
                                <p>暂无预览内容</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.updateViewMode();
    }

    renderButtons() {
        let buttons = '';
        
        if (this.showGenerateButton) {
            buttons += `
                <button class="btn btn-success btn-sm me-2" onclick="markdownEditor.generateContent()">
                    <i class="bi bi-magic"></i> 生成${this.title.replace('管理', '')}
                </button>
            `;
        }
        
        if (!this.onlyShowSave) {
            buttons += `
                <button class="btn btn-primary btn-sm me-2" onclick="markdownEditor.optimizeContent()">
                    <i class="bi bi-arrow-up-circle"></i> 一键优化
                </button>
            `;
        }
        
        buttons += `
            <button class="btn btn-outline-primary btn-sm me-2" onclick="markdownEditor.saveContent()">
                <i class="bi bi-save"></i> 保存
            </button>
        `;
        
        return buttons;
    }

    setViewMode(mode) {
        this.viewMode = mode;
        this.updateViewMode();
        this.updatePreview();
    }

    updateViewMode() {
        const editorPane = document.getElementById('editorPane');
        const previewPane = document.getElementById('previewPane');
        const editBtn = document.getElementById('editModeBtn');
        const previewBtn = document.getElementById('previewModeBtn');
        const splitBtn = document.getElementById('splitModeBtn');

        // 重置按钮状态
        [editBtn, previewBtn, splitBtn].forEach(btn => {
            btn.classList.remove('active');
            btn.classList.add('btn-outline-secondary');
            btn.classList.remove('btn-secondary');
        });

        switch (this.viewMode) {
            case 'edit':
                editorPane.style.display = 'block';
                editorPane.style.width = '100%';
                previewPane.style.display = 'none';
                editBtn.classList.add('active', 'btn-secondary');
                editBtn.classList.remove('btn-outline-secondary');
                break;
            case 'preview':
                editorPane.style.display = 'none';
                previewPane.style.display = 'block';
                previewPane.style.width = '100%';
                previewBtn.classList.add('active', 'btn-secondary');
                previewBtn.classList.remove('btn-outline-secondary');
                break;
            case 'split':
                editorPane.style.display = 'block';
                editorPane.style.width = '50%';
                previewPane.style.display = 'block';
                previewPane.style.width = '50%';
                splitBtn.classList.add('active', 'btn-secondary');
                splitBtn.classList.remove('btn-outline-secondary');
                break;
        }
    }

    bindEvents() {
        const textarea = document.getElementById('markdownTextarea');
        if (textarea) {
            textarea.addEventListener('input', () => {
                this.content = textarea.value;
                if (this.viewMode === 'split' || this.viewMode === 'preview') {
                    this.updatePreview();
                }
            });
        }
    }

    async loadContent() {
        if (!currentProject) return;

        try {
            const response = await fetch(`/api/projects/${currentProject}/${this.type}`);
            const data = await response.json();
            
            let content = '';
            if (this.type === 'requirements') {
                content = data.requirement || '';
            } else if (this.type === 'design') {
                content = data.design || '';
            } else if (this.type === 'rules') {
                content = data.rules_constraint || '';
            }
            
            this.content = content;
            const textarea = document.getElementById('markdownTextarea');
            if (textarea) {
                textarea.value = content;
            }
            
            this.updatePreview();
        } catch (error) {
            console.error('加载内容失败:', error);
            this.showMessage('加载内容失败', 'error');
        }
    }

    async saveContent() {
        if (!currentProject) {
            this.showMessage('请先选择项目', 'error');
            return;
        }

        const textarea = document.getElementById('markdownTextarea');
        if (!textarea) return;

        const content = textarea.value;
        
        try {
            const payload = {};
            if (this.type === 'requirements') {
                payload.requirement = content;
            } else if (this.type === 'design') {
                payload.design = content;
            } else if (this.type === 'rules') {
                payload.rules_constraint = content;
            }

            const response = await fetch(`/api/projects/${currentProject}/${this.type}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            });

            const result = await response.json();
            if (result.success) {
                this.showMessage('保存成功', 'success');
            } else {
                this.showMessage('保存失败: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('保存失败:', error);
            this.showMessage('保存失败', 'error');
        }
    }

    async generateContent() {
        this.showMessage('生成功能开发中...', 'info');
    }

    async optimizeContent() {
        this.showMessage('优化功能开发中...', 'info');
    }

    updatePreview() {
        const previewContent = document.getElementById('previewContent');
        if (!previewContent) return;

        if (!this.content.trim()) {
            previewContent.innerHTML = `
                <div class="text-muted text-center p-4">
                    <i class="bi bi-eye-slash"></i>
                    <p>暂无预览内容</p>
                </div>
            `;
            return;
        }

        // 简单的Markdown渲染（可以后续集成更完整的Markdown解析器）
        let html = this.content
            .replace(/^### (.*$)/gim, '<h3>$1</h3>')
            .replace(/^## (.*$)/gim, '<h2>$1</h2>')
            .replace(/^# (.*$)/gim, '<h1>$1</h1>')
            .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
            .replace(/\*(.*)\*/gim, '<em>$1</em>')
            .replace(/\n/gim, '<br>');

        previewContent.innerHTML = `<div class="markdown-preview">${html}</div>`;
    }

    showMessage(message, type = 'info') {
        // 创建消息提示
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 'alert-info';
        
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // 在编辑器顶部显示消息
        const toolbar = document.querySelector('.editor-toolbar');
        if (toolbar) {
            const existingAlert = toolbar.nextElementSibling;
            if (existingAlert && existingAlert.classList.contains('alert')) {
                existingAlert.remove();
            }
            toolbar.insertAdjacentHTML('afterend', alertHtml);
            
            // 3秒后自动消失
            setTimeout(() => {
                const alert = toolbar.nextElementSibling;
                if (alert && alert.classList.contains('alert')) {
                    alert.remove();
                }
            }, 3000);
        }
    }
}

// 全局变量
let markdownEditor = null;

// 加载Markdown编辑器
function loadMarkdownEditor(type, title, showGenerateButton = false, onlyShowSave = false) {
    const containerId = type + 'Editor';
    markdownEditor = new MarkdownEditor(containerId, type, title, showGenerateButton, onlyShowSave);
}
