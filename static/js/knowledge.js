/**
 * 知识库管理JavaScript模块
 * 用于前端知识库管理和检索测试功能
 */

// 全局变量
let currentProjectId = null;
let currentKnowledgeBaseId = null;
let knowledgeBases = [];

// 知识库管理器
window.KnowledgeManager = {
    
    /**
     * 加载项目列表
     */
    loadProjects: function() {
        API.projects.list()
            .then(response => {
                if (response.success !== false) {
                    const selector = document.getElementById('projectSelector');
                    if (selector) {
                        selector.innerHTML = '<option value="">请选择项目...</option>';
                        response.forEach(project => {
                            const option = document.createElement('option');
                            option.value = project.project_id;
                            option.textContent = project.name;
                            selector.appendChild(option);
                        });
                    }
                }
            })
            .catch(error => {
                console.error('加载项目列表失败:', error);
                Utils.showAlert('加载项目列表失败', 'error');
            });
    },

    /**
     * 加载知识库列表
     */
    loadKnowledgeBases: function(projectId = null) {
        const targetProjectId = projectId || currentProjectId;
        if (!targetProjectId) {
            this.showEmptyState();
            return;
        }

        API.request({
            url: `/api/projects/${targetProjectId}/knowledge_bases`,
            method: 'GET'
        })
        .then(response => {
            if (response.success) {
                knowledgeBases = response.knowledge_bases || [];
                this.renderKnowledgeBases();
            } else {
                Utils.showAlert(response.message || '加载知识库列表失败', 'error');
                this.showEmptyState();
            }
        })
        .catch(error => {
            console.error('加载知识库列表失败:', error);
            Utils.showAlert('加载知识库列表失败', 'error');
            this.showEmptyState();
        });
    },

    /**
     * 渲染知识库列表
     */
    renderKnowledgeBases: function() {
        const container = document.getElementById('knowledgeBasesList');
        const emptyState = document.getElementById('emptyState');
        
        if (!container) return;

        if (knowledgeBases.length === 0) {
            this.showEmptyState();
            return;
        }

        // 隐藏空状态
        if (emptyState) {
            emptyState.style.display = 'none';
        }

        // 生成知识库卡片
        let html = '';
        
        // 添加创建新知识库的卡片
        html += `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card create-kb-card h-100" onclick="showCreateKnowledgeBaseModal()">
                    <div class="card-body text-center">
                        <i class="fas fa-plus create-kb-icon"></i>
                        <h5 class="card-title">创建新知识库</h5>
                        <p class="card-text text-muted">点击创建您的第一个知识库</p>
                    </div>
                </div>
            </div>
        `;

        // 添加现有知识库卡片
        knowledgeBases.forEach(kb => {
            const createdDate = new Date(kb.created_at).toLocaleDateString('zh-CN');
            html += `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card kb-card h-100" onclick="openKnowledgeBaseDetail('${kb.kb_id}')">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="card-title">${Utils.escapeHtml(kb.name)}</h5>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown" onclick="event.stopPropagation()">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="event.stopPropagation(); openKnowledgeBaseDetail('${kb.kb_id}')">
                                            <i class="fas fa-eye"></i> 查看详情
                                        </a></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="event.stopPropagation(); deleteKnowledgeBase('${kb.kb_id}', '${Utils.escapeHtml(kb.name)}')">
                                            <i class="fas fa-trash"></i> 删除
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                            <p class="card-text">${Utils.escapeHtml(kb.description || '暂无描述')}</p>
                            <div class="kb-stats">
                                <small class="text-muted">
                                    <i class="fas fa-file-alt"></i> ${kb.document_count || 0} 个文档
                                    <br>
                                    <i class="fas fa-calendar"></i> 创建于 ${createdDate}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    },

    /**
     * 显示空状态
     */
    showEmptyState: function() {
        const container = document.getElementById('knowledgeBasesList');
        const emptyState = document.getElementById('emptyState');
        
        if (container) {
            container.innerHTML = '';
        }
        if (emptyState) {
            emptyState.style.display = 'block';
        }
    },

    /**
     * 创建知识库
     */
    createKnowledgeBase: function() {
        if (!currentProjectId) {
            Utils.showAlert('请先选择项目', 'warning');
            return;
        }

        const name = document.getElementById('kbName').value.trim();
        const description = document.getElementById('kbDescription').value.trim();

        if (!name) {
            Utils.showAlert('请输入知识库名称', 'warning');
            return;
        }

        const data = {
            name: name,
            description: description
        };

        API.request({
            url: `/api/projects/${currentProjectId}/knowledge_bases`,
            method: 'POST',
            data: data
        })
        .then(response => {
            if (response.success) {
                Utils.showAlert('知识库创建成功', 'success');
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('createKnowledgeBaseModal'));
                if (modal) {
                    modal.hide();
                }
                // 清空表单
                document.getElementById('createKnowledgeBaseForm').reset();
                // 重新加载知识库列表
                this.loadKnowledgeBases();
            } else {
                Utils.showAlert(response.message || '创建知识库失败', 'error');
            }
        })
        .catch(error => {
            console.error('创建知识库失败:', error);
            Utils.showAlert('创建知识库失败', 'error');
        });
    },

    /**
     * 删除知识库
     */
    deleteKnowledgeBase: function(kbId, kbName) {
        // 设置删除确认信息
        document.getElementById('deleteKbName').textContent = kbName;
        
        // 保存要删除的知识库ID
        window.deleteKbId = kbId;
        
        // 显示确认模态框
        const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
        modal.show();
    },

    /**
     * 确认删除知识库
     */
    confirmDeleteKnowledgeBase: function() {
        const kbId = window.deleteKbId;
        if (!kbId) return;

        API.request({
            url: `/api/knowledge_bases/${kbId}`,
            method: 'DELETE'
        })
        .then(response => {
            if (response.success) {
                Utils.showAlert('知识库删除成功', 'success');
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
                if (modal) {
                    modal.hide();
                }
                // 重新加载知识库列表
                this.loadKnowledgeBases();
            } else {
                Utils.showAlert(response.message || '删除知识库失败', 'error');
            }
        })
        .catch(error => {
            console.error('删除知识库失败:', error);
            Utils.showAlert('删除知识库失败', 'error');
        });
    },

    /**
     * 加载知识库详情
     */
    loadKnowledgeBaseDetail: function() {
        const kbId = Router.getParam('kb_id');
        if (!kbId) {
            Utils.showAlert('知识库ID不存在', 'error');
            return;
        }

        currentKnowledgeBaseId = kbId;

        API.request({
            url: `/api/knowledge_bases/${kbId}`,
            method: 'GET'
        })
        .then(response => {
            if (response.success) {
                this.renderKnowledgeBaseDetail(response.knowledge_base);
            } else {
                Utils.showAlert(response.message || '加载知识库详情失败', 'error');
            }
        })
        .catch(error => {
            console.error('加载知识库详情失败:', error);
            Utils.showAlert('加载知识库详情失败', 'error');
        });
    },

    /**
     * 渲染知识库详情
     */
    renderKnowledgeBaseDetail: function(kb) {
        document.getElementById('kbName').textContent = kb.name;
        document.getElementById('kbDescription').textContent = kb.description || '暂无描述';
        document.getElementById('kbCreatedAt').textContent = `创建于 ${new Date(kb.created_at).toLocaleString('zh-CN')}`;
        document.getElementById('documentCount').textContent = `${kb.document_count || 0} 个文档`;
        
        // 设置删除按钮的知识库名称
        window.currentKbName = kb.name;
    },

    /**
     * 添加文档
     */
    addDocument: function() {
        if (!currentKnowledgeBaseId) {
            Utils.showAlert('知识库ID不存在', 'error');
            return;
        }

        const title = document.getElementById('docTitle').value.trim();
        const content = document.getElementById('docContent').value.trim();
        const category = document.getElementById('docCategory').value.trim();

        if (!title || !content) {
            Utils.showAlert('请输入文档标题和内容', 'warning');
            return;
        }

        const data = {
            title: title,
            content: content,
            metadata: {
                category: category || '未分类'
            }
        };

        API.request({
            url: `/api/knowledge_bases/${currentKnowledgeBaseId}/documents`,
            method: 'POST',
            data: data
        })
        .then(response => {
            if (response.success) {
                Utils.showAlert('文档添加成功', 'success');
                // 清空表单
                this.clearDocumentForm();
                // 重新加载知识库详情以更新文档数量
                this.loadKnowledgeBaseDetail();
            } else {
                Utils.showAlert(response.message || '添加文档失败', 'error');
            }
        })
        .catch(error => {
            console.error('添加文档失败:', error);
            Utils.showAlert('添加文档失败', 'error');
        });
    },

    /**
     * 清空文档表单
     */
    clearDocumentForm: function() {
        document.getElementById('addDocumentForm').reset();
    },

    /**
     * 搜索文档
     */
    searchDocuments: function() {
        if (!currentKnowledgeBaseId) {
            Utils.showAlert('知识库ID不存在', 'error');
            return;
        }

        const query = document.getElementById('searchQuery').value.trim();
        const limit = parseInt(document.getElementById('searchLimit').value) || 10;

        if (!query) {
            Utils.showAlert('请输入搜索关键词', 'warning');
            return;
        }

        const data = {
            query: query,
            limit: limit
        };

        API.request({
            url: `/api/knowledge_bases/${currentKnowledgeBaseId}/search`,
            method: 'POST',
            data: data
        })
        .then(response => {
            if (response.success) {
                this.renderSearchResults(response.documents, query);
            } else {
                Utils.showAlert(response.message || '搜索失败', 'error');
            }
        })
        .catch(error => {
            console.error('搜索失败:', error);
            Utils.showAlert('搜索失败', 'error');
        });
    },

    /**
     * 渲染搜索结果
     */
    renderSearchResults: function(documents, query) {
        const resultsContainer = document.getElementById('searchResults');
        const resultsList = document.getElementById('searchResultsList');
        
        if (!resultsContainer || !resultsList) return;

        if (documents.length === 0) {
            resultsList.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-search fa-2x text-muted mb-3"></i>
                    <p class="text-muted">没有找到相关文档</p>
                </div>
            `;
        } else {
            let html = '';
            documents.forEach((doc, index) => {
                html += `
                    <div class="search-result p-3 mb-3">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="mb-0">${Utils.escapeHtml(doc.title)}</h6>
                            <span class="search-score">相似度: ${(1 - doc.score).toFixed(3)}</span>
                        </div>
                        <div class="document-content">
                            ${Utils.escapeHtml(doc.content)}
                        </div>
                        ${doc.metadata && doc.metadata.category ? 
                            `<small class="text-muted"><i class="fas fa-tag"></i> ${Utils.escapeHtml(doc.metadata.category)}</small>` : 
                            ''
                        }
                    </div>
                `;
            });
            resultsList.innerHTML = html;
        }

        resultsContainer.style.display = 'block';
        // 滚动到搜索结果
        resultsContainer.scrollIntoView({ behavior: 'smooth' });
    }
};

// 全局函数（供HTML调用）
function loadProjects() {
    KnowledgeManager.loadProjects();
}

function loadKnowledgeBases() {
    KnowledgeManager.loadKnowledgeBases();
}

function showCreateKnowledgeBaseModal() {
    if (!currentProjectId) {
        Utils.showAlert('请先选择项目', 'warning');
        return;
    }
    const modal = new bootstrap.Modal(document.getElementById('createKnowledgeBaseModal'));
    modal.show();
}

function createKnowledgeBase() {
    KnowledgeManager.createKnowledgeBase();
}

function deleteKnowledgeBase(kbId, kbName) {
    KnowledgeManager.deleteKnowledgeBase(kbId, kbName);
}

function confirmDeleteKnowledgeBase() {
    KnowledgeManager.confirmDeleteKnowledgeBase();
}

function openKnowledgeBaseDetail(kbId) {
    window.location.href = `knowledge_detail.html?kb_id=${kbId}`;
}

function loadKnowledgeBaseDetail() {
    KnowledgeManager.loadKnowledgeBaseDetail();
}

function addDocument() {
    KnowledgeManager.addDocument();
}

function clearDocumentForm() {
    KnowledgeManager.clearDocumentForm();
}

function searchDocuments() {
    KnowledgeManager.searchDocuments();
}

function deleteKnowledgeBase() {
    if (!currentKnowledgeBaseId || !window.currentKbName) {
        Utils.showAlert('知识库信息不完整', 'error');
        return;
    }
    
    document.getElementById('deleteKbName').textContent = window.currentKbName;
    window.deleteKbId = currentKnowledgeBaseId;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    modal.show();
}
