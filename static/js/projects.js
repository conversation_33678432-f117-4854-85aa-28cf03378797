/**
 * 项目管理相关JavaScript
 */

// 项目管理模块
window.ProjectManager = {
    /**
     * 初始化项目列表页面
     */
    initProjectsPage: function() {
        this.loadProjects();
        this.bindEvents();
    },

    /**
     * 加载项目列表
     */
    loadProjects: function() {
        API.projects.list()
            .done(function(projects) {
                ProjectManager.renderProjectsList(projects);
            })
            .fail(function(xhr) {
                const response = xhr.responseJSON || {};
                Utils.showAlert('加载项目列表失败: ' + (response.message || '未知错误'), 'danger');
            });
    },

    /**
     * 渲染项目列表
     */
    renderProjectsList: function(projects) {
        const container = $('#projects-container');
        
        if (projects.length === 0) {
            container.html(`
                <div class="text-center py-5">
                    <i class="fas fa-folder-open fa-5x text-muted mb-4"></i>
                    <h3 class="text-muted">还没有项目</h3>
                    <p class="text-muted mb-4">创建您的第一个项目来开始使用AI任务管理系统</p>
                    <button class="btn btn-primary btn-lg" onclick="ProjectManager.showCreateModal()">
                        <i class="fas fa-plus"></i> 创建项目
                    </button>
                </div>
            `);
            return;
        }

        let html = `
            <div class="card">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 20%">项目名称</th>
                                    <th style="width: 12%">创建时间</th>
                                    <th style="width: 12%">更新时间</th>
                                    <th style="width: 10%">LLM Provider</th>
                                    <th style="width: 10%">任务类型</th>
                                    <th style="width: 8%">任务数量</th>
                                    <th style="width: 8%">已完成</th>
                                    <th style="width: 20%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
        `;

        projects.forEach(project => {
            html += `
                <tr class="project-row" data-project-id="${project.project_id}">
                    <td>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-folder text-primary me-2"></i>
                            <div>
                                <div class="fw-bold">
                                    <a href="/project_tasks.html?project_id=${project.project_id}" class="text-decoration-none">
                                        ${project.name}
                                    </a>
                                </div>
                                <small class="text-muted">
                                    ${project.description ? 
                                        (project.description.length > 50 ? 
                                            project.description.substring(0, 50) + '...' : 
                                            project.description) : 
                                        '暂无描述'}
                                </small>
                            </div>
                        </div>
                    </td>
                    <td>
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            ${Utils.formatDateTime(project.created_at)}
                        </small>
                    </td>
                    <td>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            ${Utils.formatDateTime(project.updated_at)}
                        </small>
                    </td>
                    <td>${Utils.getProviderBadge(project.provider)}</td>
                    <td>${Utils.getTaskTypeBadge(project.task_type)}</td>
                    <td><span class="badge bg-info">${project.total_tasks || 0}</span></td>
                    <td><span class="badge bg-success">${project.completed_tasks || 0}</span></td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary"
                                    onclick="ProjectManager.viewDetail('${project.project_id}')"
                                    title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button type="button" class="btn btn-outline-success"
                                    onclick="ProjectManager.generateTasks('${project.project_id}')"
                                    title="生成任务">
                                <i class="fas fa-cogs"></i>
                            </button>
                            <button type="button" class="btn btn-outline-info"
                                    onclick="ProjectManager.viewTasks('${project.project_id}')"
                                    title="任务列表">
                                <i class="fas fa-tasks"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary"
                                    onclick="ProjectManager.showEditModal('${project.project_id}')"
                                    title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger"
                                    onclick="ProjectManager.deleteProject('${project.project_id}')"
                                    title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        container.html(html);
    },

    /**
     * 绑定事件
     */
    bindEvents: function() {
        // 创建项目按钮
        $(document).on('click', '#create-project-btn', function() {
            ProjectManager.showCreateModal();
        });

        // 刷新按钮
        $(document).on('click', '#refresh-projects-btn', function() {
            ProjectManager.loadProjects();
        });
    },

    /**
     * 显示创建项目模态框
     */
    showCreateModal: function() {
        $('#createProjectModal').modal('show');
        
        // 自动填充工作目录
        $('#createProjectName').off('input').on('input', function() {
            const name = $(this).val().trim();
            if (name) {
                const workDir = `/tmp/projects/${name.replace(/\s+/g, '_')}`;
                $('#createProjectWorkDir').val(workDir);
            }
        });
    },

    /**
     * 提交创建项目
     */
    submitCreate: function() {
        const formData = {
            name: $('#createProjectName').val().trim(),
            work_dir: $('#createProjectWorkDir').val().trim(),
            description: $('#createProjectDescription').val().trim(),
            requirement: $('#createProjectRequirement').val().trim(),
            provider: $('#createProjectProvider').val(),
            task_type: $('#createProjectTaskType').val(),
            rules_constraint: $('#createProjectRulesConstraint').val().trim()
        };

        if (!formData.name || !formData.work_dir) {
            Utils.showAlert('项目名称和工作目录不能为空', 'warning');
            return;
        }

        const submitBtn = $('#createProjectModal .btn-primary');
        Utils.setButtonLoading(submitBtn, true, '创建中...');

        API.projects.create(formData)
            .done(function(response) {
                if (response.success) {
                    Utils.showAlert('项目创建成功！', 'success');
                    $('#createProjectModal').modal('hide');
                    setTimeout(() => {
                        ProjectManager.loadProjects();
                    }, 1000);
                } else {
                    Utils.showAlert('项目创建失败: ' + response.message, 'danger');
                }
            })
            .fail(function(xhr) {
                const response = xhr.responseJSON || {};
                Utils.showAlert('项目创建失败: ' + (response.message || '未知错误'), 'danger');
            })
            .always(function() {
                Utils.setButtonLoading(submitBtn, false);
            });
    },

    /**
     * 查看项目详情
     */
    viewDetail: function(projectId) {
        API.projects.get(projectId)
            .done(function(project) {
                const content = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle"></i> 基本信息</h6>
                            <table class="table table-sm">
                                <tr><td><strong>项目名称:</strong></td><td>${project.name}</td></tr>
                                <tr><td><strong>工作目录:</strong></td><td>${project.work_dir}</td></tr>
                                <tr><td><strong>创建时间:</strong></td><td>${Utils.formatDateTime(project.created_at)}</td></tr>
                                <tr><td><strong>更新时间:</strong></td><td>${Utils.formatDateTime(project.updated_at)}</td></tr>
                                <tr><td><strong>任务已生成:</strong></td><td>${project.tasks_generated ? '是' : '否'}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-chart-bar"></i> 统计信息</h6>
                            <table class="table table-sm">
                                <tr><td><strong>总任务数:</strong></td><td><span class="badge bg-info">${project.total_tasks || 0}</span></td></tr>
                                <tr><td><strong>已完成:</strong></td><td><span class="badge bg-success">${project.completed_tasks || 0}</span></td></tr>
                                <tr><td><strong>完成率:</strong></td><td>${project.total_tasks > 0 ? Math.round((project.completed_tasks || 0) / project.total_tasks * 100) : 0}%</td></tr>
                            </table>
                        </div>
                    </div>
                    <div class="mt-3">
                        <h6><i class="fas fa-file-alt"></i> 项目需求</h6>
                        <div class="border rounded p-3 bg-light">
                            ${project.requirement ? marked.parse(project.requirement) : '<em class="text-muted">暂无需求</em>'}
                        </div>
                    </div>
                `;
                $('#projectDetailContent').html(content);
                $('#projectDetailModal').modal('show');
                $('#projectDetailModal').data('project-id', projectId);
            })
            .fail(function() {
                Utils.showAlert('获取项目信息失败', 'danger');
            });
    },

    /**
     * 显示编辑项目模态框
     */
    showEditModal: function(projectId) {
        API.projects.get(projectId)
            .done(function(project) {
                // 填充表单
                $('#editProjectId').val(project.project_id);
                $('#editProjectName').val(project.name);
                $('#editProjectDescription').val(project.description || '');
                $('#editProjectWorkDir').val(project.work_dir);
                $('#editProjectRequirement').val(project.requirement || '');
                $('#editProjectProvider').val(project.provider || 'local');
                $('#editProjectTaskType').val(project.task_type || '新功能');
                $('#editProjectRulesConstraint').val(project.rules_constraint || '');
                
                $('#editProjectModal').modal('show');
            })
            .fail(function() {
                Utils.showAlert('获取项目信息失败', 'danger');
            });
    },

    /**
     * 提交编辑项目
     */
    submitEdit: function() {
        const projectId = $('#editProjectId').val();
        const formData = {
            name: $('#editProjectName').val().trim(),
            description: $('#editProjectDescription').val().trim(),
            work_dir: $('#editProjectWorkDir').val().trim(),
            requirement: $('#editProjectRequirement').val().trim(),
            provider: $('#editProjectProvider').val(),
            task_type: $('#editProjectTaskType').val(),
            rules_constraint: $('#editProjectRulesConstraint').val().trim()
        };

        if (!formData.name || !formData.work_dir) {
            Utils.showAlert('项目名称和工作目录不能为空', 'warning');
            return;
        }

        const submitBtn = $('#editProjectModal .btn-primary');
        Utils.setButtonLoading(submitBtn, true, '保存中...');

        API.projects.update(projectId, formData)
            .done(function(response) {
                if (response.success) {
                    Utils.showAlert('项目更新成功！', 'success');
                    $('#editProjectModal').modal('hide');
                    setTimeout(() => {
                        ProjectManager.loadProjects();
                    }, 1000);
                } else {
                    Utils.showAlert('项目更新失败: ' + response.message, 'danger');
                }
            })
            .fail(function(xhr) {
                const response = xhr.responseJSON || {};
                Utils.showAlert('项目更新失败: ' + (response.message || '未知错误'), 'danger');
            })
            .always(function() {
                Utils.setButtonLoading(submitBtn, false);
            });
    },

    /**
     * 删除项目
     */
    deleteProject: function(projectId) {
        Utils.confirm('确定要删除这个项目吗？此操作将同时删除项目下的所有任务。', function() {
            API.projects.delete(projectId)
                .done(function(response) {
                    if (response.success) {
                        Utils.showAlert('项目删除成功！', 'success');
                        setTimeout(() => {
                            ProjectManager.loadProjects();
                        }, 1000);
                    } else {
                        Utils.showAlert('项目删除失败: ' + response.message, 'danger');
                    }
                })
                .fail(function(xhr) {
                    const response = xhr.responseJSON || {};
                    Utils.showAlert('项目删除失败: ' + (response.message || '未知错误'), 'danger');
                });
        });
    },

    /**
     * 生成任务
     */
    generateTasks: function(projectId) {
        API.projects.get(projectId)
            .done(function(project) {
                let content = `<p>确定要为项目 <strong>${project.name}</strong> 生成任务吗？</p>`;

                if (!project.requirement || !project.requirement.trim()) {
                    content = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            项目 <strong>${project.name}</strong> 还没有设置需求，无法生成任务。
                        </div>
                        <p>请先编辑项目并添加需求内容。</p>
                    `;
                    $('#generateTasksModal .modal-footer .btn-primary').hide();
                    $('#taskCountSection').hide();
                } else if (project.tasks_generated) {
                    content = `
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            项目 <strong>${project.name}</strong> 已经生成过任务。
                        </div>
                        <p>重新生成将覆盖现有任务，确定要继续吗？</p>
                    `;
                    $('#generateTasksModal .modal-footer .btn-primary').show();
                    $('#taskCountSection').show();
                } else {
                    $('#generateTasksModal .modal-footer .btn-primary').show();
                    $('#taskCountSection').show();
                }

                $('#generateTasksContent').html(content);
                $('#generateTasksProjectId').val(projectId);
                $('#generateTasksModal').modal('show');
            })
            .fail(function() {
                Utils.showAlert('获取项目信息失败', 'danger');
            });
    },

    /**
     * 确认生成任务
     */
    confirmGenerateTasks: function() {
        const projectId = $('#generateTasksProjectId').val();
        const taskCount = $('#taskCount').val();

        const submitBtn = $('#generateTasksModal .btn-primary');
        Utils.setButtonLoading(submitBtn, true, '生成中...');

        const requestData = {};
        if (taskCount && taskCount.trim()) {
            requestData.num_tasks = parseInt(taskCount);
        }

        API.projects.generateTasks(projectId, requestData)
            .done(function(response) {
                if (response.success) {
                    Utils.showAlert('任务生成成功！', 'success');
                    $('#generateTasksModal').modal('hide');
                    setTimeout(() => {
                        ProjectManager.loadProjects();
                    }, 1000);
                } else {
                    Utils.showAlert('任务生成失败: ' + response.message, 'danger');
                }
            })
            .fail(function(xhr) {
                const response = xhr.responseJSON || {};
                Utils.showAlert('任务生成失败: ' + (response.message || '未知错误'), 'danger');
            })
            .always(function() {
                Utils.setButtonLoading(submitBtn, false);
            });
    },

    /**
     * 查看任务列表
     */
    viewTasks: function(projectId) {
        Router.navigate(`/project_tasks.html?id=${projectId}`);
    }
};
