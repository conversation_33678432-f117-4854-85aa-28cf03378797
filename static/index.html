<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板 - AI任务管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .main-content {
            min-height: 100vh;
        }
        .card {
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .border-left-primary {
            border-left: 4px solid #007bff !important;
        }
        .border-left-success {
            border-left: 4px solid #28a745 !important;
        }
        .border-left-info {
            border-left: 4px solid #17a2b8 !important;
        }
        .border-left-warning {
            border-left: 4px solid #ffc107 !important;
        }

        /* Markdown编辑器样式 */
        .editor-container {
            height: calc(100vh - 120px);
            display: flex;
            flex-direction: column;
        }

        .editor-toolbar {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 8px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }

        .editor-content {
            flex: 1;
            display: flex;
            min-height: 0;
        }

        .editor-pane {
            flex: 1;
            border-right: 1px solid #dee2e6;
        }

        .editor-pane:last-child {
            border-right: none;
        }

        .editor-textarea {
            width: 100%;
            height: 100%;
            border: none;
            resize: none;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.6;
            padding: 15px;
        }

        .editor-textarea:focus {
            outline: none;
            box-shadow: none;
        }

        .preview-pane {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            background-color: #ffffff;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-tasks"></i> AI任务管理系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- 当前项目选择器移动到左侧 -->
                <div class="navbar-nav">
                    <select class="form-select form-select-sm" id="currentProjectSelector" onchange="switchCurrentProject()" style="min-width: 200px;">
                        <option value="">请选择项目...</option>
                    </select>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" onclick="showDashboard()">
                                <i class="fas fa-tachometer-alt"></i> 仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showProjectManagement()">
                                <i class="fas fa-folder-open"></i> 项目管理
                            </a>
                        </li>
                    </ul>

                    <!-- 当前项目相关菜单 -->
                    <div id="currentProjectMenu" style="display: none;">
                        <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                            <span>当前项目</span>
                        </h6>
                        <ul class="nav flex-column mb-2">
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="showRequirementManager()">
                                    <i class="fas fa-file-alt"></i> 需求管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="showDesignManager()">
                                    <i class="fas fa-palette"></i> 设计管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="showRulesManager()">
                                    <i class="fas fa-shield-alt"></i> 规则管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="showTaskManager()">
                                    <i class="fas fa-tasks"></i> 任务管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" onclick="showKnowledgeManager()">
                                    <i class="fas fa-brain"></i> 知识库管理
                                </a>
                            </li>
                        </ul>
                    </div>

                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>快速操作</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showCreateProjectModal()">
                                <i class="fas fa-plus"></i> 新建项目
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 动态内容区域 -->
                <div id="mainContentArea">
                    <!-- 默认显示仪表板 -->
                    <div id="dashboardContent">
                        <div class="pt-3">
                            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                                <h1 class="h2">仪表板</h1>
                                <div class="btn-toolbar mb-2 mb-md-0">
                                    <div class="btn-group me-2">
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshDashboard()">
                                            <i class="fas fa-sync-alt"></i> 刷新
                                        </button>
                                    </div>
                                </div>
                            </div>

                    <!-- 统计卡片 -->
                    <div class="row mb-4">
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                项目总数
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-projects">
                                                0
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-folder fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                活跃项目
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-projects">
                                                0
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-play fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-info shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                总任务数
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-tasks">
                                                0
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-list fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                运行中任务
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="running-tasks">
                                                0
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-cog fa-spin fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 最近项目 -->
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                                    <h6 class="m-0 font-weight-bold text-primary">最近项目</h6>
                                    <a href="projects.html" class="btn btn-sm btn-primary">查看全部</a>
                                </div>
                                <div class="card-body" id="recent-projects-container">
                                    <div class="text-center py-4">
                                        <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                                        <div>加载中...</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">快速操作</h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-primary" onclick="showCreateProjectModal()">
                                            <i class="fas fa-plus"></i> 新建项目
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="showQuickTask()">
                                            <i class="fas fa-bolt"></i> 快速任务
                                        </button>
                                        <a href="projects.html" class="btn btn-outline-info">
                                            <i class="fas fa-list"></i> 浏览项目
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="card shadow">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">系统状态</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>系统运行状态</span>
                                            <span class="badge bg-success">正常</span>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>Claude连接</span>
                                            <span class="badge bg-success">已连接</span>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between">
                                            <span>数据存储</span>
                                            <span class="badge bg-success">正常</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>
                    </div>
                    <!-- 仪表板内容结束 -->

                    <!-- 项目管理内容 -->
                    <div id="projectManagementContent" style="display: none;">
                        <iframe src="projects.html" style="width: 100%; height: calc(100vh - 120px); border: none;"></iframe>
                    </div>

                    <!-- 需求管理内容 -->
                    <div id="requirementContent" style="display: none; background-color: #f8f9fa; border: 2px solid #007bff; min-height: 400px; padding: 20px;">
                        <div id="requirementEditor"></div>
                    </div>

                    <!-- 设计管理内容 -->
                    <div id="designContent" style="display: none; background-color: #f8f9fa; border: 2px solid #28a745; min-height: 400px; padding: 20px;">
                        <div id="designEditor"></div>
                    </div>

                    <!-- 规则管理内容 -->
                    <div id="rulesContent" style="display: none; background-color: #f8f9fa; border: 2px solid #ffc107; min-height: 400px; padding: 20px;">
                        <div id="rulesEditor"></div>
                    </div>

                    <!-- 任务管理内容 -->
                    <div id="taskContent" style="display: none;">
                        <iframe id="taskFrame" style="width: 100%; height: calc(100vh - 120px); border: none;"></iframe>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 创建项目模态框 -->
    <div class="modal fade" id="createProjectModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建新项目</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createProjectForm">
                        <div class="mb-3">
                            <label for="createProjectName" class="form-label">项目名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="createProjectName" required
                                   placeholder="请输入项目名称">
                        </div>
                        <div class="mb-3">
                            <label for="createProjectWorkDir" class="form-label">工作目录 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="createProjectWorkDir" required
                                   placeholder="例如: /tmp/projects/my_project">
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i>
                                项目文件将存储在此目录中
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="createProjectProvider" class="form-label">LLM Provider</label>
                            <select class="form-control" id="createProjectProvider">
                                <option value="local">内网</option>
                                <option value="zhipu">智谱</option>
                                <option value="claude">Claude</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="createProjectTaskType" class="form-label">任务类型</label>
                            <select class="form-control" id="createProjectTaskType">
                                <option value="新功能">新功能</option>
                                <option value="代码重构">代码重构</option>
                                <option value="PMO需求">PMO需求</option>
                                <option value="代码分析">代码分析</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="ProjectManager.submitCreate()">创建项目</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- Marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    
    <!-- 自定义JS -->
    <script src="static/js/common.js"></script>
    <script src="static/js/projects.js"></script>
    
    <script>
        // 页面初始化
        $(document).ready(function() {
            loadDashboardData();
        });

        function loadDashboardData() {
            // 加载项目列表
            API.projects.list()
                .done(function(projects) {
                    updateDashboardStats(projects);
                    renderRecentProjects(projects);
                })
                .fail(function(xhr) {
                    const response = xhr.responseJSON || {};
                    Utils.showAlert('加载仪表板数据失败: ' + (response.message || '未知错误'), 'danger');
                });
        }

        function updateDashboardStats(projects) {
            const totalProjects = projects.length;
            const activeProjects = projects.filter(p => p.requirement && p.requirement.trim()).length;
            const totalTasks = projects.reduce((sum, p) => sum + (p.total_tasks || 0), 0);
            const runningTasks = 0; // 这里可以通过API获取运行中的任务数量

            $('#total-projects').text(totalProjects);
            $('#active-projects').text(activeProjects);
            $('#total-tasks').text(totalTasks);
            $('#running-tasks').text(runningTasks);
        }

        function renderRecentProjects(projects) {
            const container = $('#recent-projects-container');
            
            if (projects.length === 0) {
                container.html(`
                    <div class="text-center py-4">
                        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                        <p class="text-muted">还没有项目，<a href="#" onclick="showCreateProjectModal()">创建第一个项目</a></p>
                    </div>
                `);
                return;
            }

            const recentProjects = projects.slice(0, 5);
            let html = `
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>项目名称</th>
                                <th>任务数量</th>
                                <th>创建时间</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            recentProjects.forEach(project => {
                html += `
                    <tr>
                        <td>
                            <a href="project_tasks.html?project_id=${project.project_id}" class="text-decoration-none">
                                <strong>${project.name}</strong>
                            </a>
                            ${project.description ? 
                                `<br><small class="text-muted">${project.description.substring(0, 50)}${project.description.length > 50 ? '...' : ''}</small>` : 
                                ''}
                        </td>
                        <td>
                            <span class="badge bg-info">${project.total_tasks || 0}</span>
                        </td>
                        <td>${Utils.formatDateTime(project.created_at)}</td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            container.html(html);
        }

        function refreshDashboard() {
            loadDashboardData();
        }

        function showCreateProjectModal() {
            ProjectManager.showCreateModal();
        }

        function showQuickTask() {
            Utils.showAlert('快速任务功能正在开发中...', 'info');
        }

        // 全局变量
        let currentProjectId = null;

        // 加载项目选择器
        function loadProjectSelector() {
            fetch('/api/projects')
                .then(response => response.json())
                .then(projects => {
                    const selector = document.getElementById('currentProjectSelector');
                    selector.innerHTML = '<option value="">请选择项目...</option>';

                    projects.forEach(project => {
                        const option = document.createElement('option');
                        option.value = project.project_id;
                        option.textContent = project.name;
                        selector.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('加载项目列表失败:', error);
                });
        }

        // 切换当前项目
        function switchCurrentProject() {
            const selector = document.getElementById('currentProjectSelector');
            const projectId = selector.value;

            if (projectId) {
                currentProjectId = projectId;
                // 保存到localStorage
                localStorage.setItem('currentProjectId', projectId);
                document.getElementById('currentProjectMenu').style.display = 'block';
                Utils.showAlert(`已切换到项目: ${selector.options[selector.selectedIndex].text}`, 'success');
            } else {
                currentProjectId = null;
                localStorage.removeItem('currentProjectId');
                document.getElementById('currentProjectMenu').style.display = 'none';
            }
        }

        // 恢复上次选择的项目
        function restoreLastProject() {
            const lastProjectId = localStorage.getItem('currentProjectId');
            if (lastProjectId) {
                const selector = document.getElementById('currentProjectSelector');
                // 等待项目列表加载完成后再设置
                setTimeout(() => {
                    if (selector.querySelector(`option[value="${lastProjectId}"]`)) {
                        selector.value = lastProjectId;
                        currentProjectId = lastProjectId;
                        document.getElementById('currentProjectMenu').style.display = 'block';
                        console.log('已恢复上次选择的项目:', lastProjectId);
                    }
                }, 100);
            }
        }

        // 显示仪表板
        function showDashboard() {
            // 直接在主内容区域显示仪表板内容
            const mainArea = document.getElementById('mainContentArea');

            // 获取原始仪表板内容
            const originalDashboard = document.getElementById('dashboardContent');
            if (originalDashboard) {
                // 清空主内容区域
                mainArea.innerHTML = '';

                // 克隆仪表板内容
                const dashboardClone = originalDashboard.cloneNode(true);
                dashboardClone.style.display = 'block';

                // 添加到主内容区域
                mainArea.appendChild(dashboardClone);
            } else {
                // 如果找不到原始仪表板，创建一个简单的仪表板
                mainArea.innerHTML = `
                    <div style="padding: 20px;">
                        <h1 class="h2">仪表板</h1>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">项目概览</h5>
                                        <p class="card-text">查看项目的基本信息和进度。</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">快速操作</h5>
                                        <p class="card-text">快速访问常用功能。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            updateActiveMenu('仪表板');
        }

        // 显示项目管理
        function showProjectManagement() {
            // 直接在主内容区域创建内容
            const mainArea = document.getElementById('mainContentArea');
            mainArea.innerHTML = '';

            // 创建项目管理容器
            const projectDiv = document.createElement('div');
            projectDiv.id = 'projectManagementContent';
            projectDiv.style.cssText = `
                display: block !important;
                background-color: #f8f9fa !important;
                border: 2px solid #6f42c1 !important;
                min-height: 400px !important;
                padding: 0 !important;
                margin: 20px !important;
                border-radius: 8px !important;
                overflow: hidden !important;
            `;

            // 创建iframe
            const iframe = document.createElement('iframe');
            iframe.src = 'projects.html';
            iframe.style.cssText = `
                width: 100% !important;
                height: calc(100vh - 120px) !important;
                border: none !important;
                display: block !important;
            `;

            projectDiv.appendChild(iframe);
            mainArea.appendChild(projectDiv);

            updateActiveMenu('项目管理');
        }

        // 显示需求管理
        function showRequirementManager() {
            console.log('showRequirementManager called, currentProjectId:', currentProjectId);
            if (!currentProjectId) {
                Utils.showAlert('请先选择一个项目', 'warning');
                return;
            }

            // 直接在主内容区域创建内容
            const mainArea = document.getElementById('mainContentArea');
            mainArea.innerHTML = '';

            // 创建需求管理容器
            const requirementDiv = document.createElement('div');
            requirementDiv.id = 'requirementContent';
            requirementDiv.style.cssText = `
                display: block !important;
                background-color: #f8f9fa !important;
                border: 2px solid #007bff !important;
                min-height: 400px !important;
                padding: 20px !important;
                margin: 20px !important;
                border-radius: 8px !important;
            `;

            // 添加到主内容区域
            mainArea.appendChild(requirementDiv);

            // 加载Markdown编辑器
            loadMarkdownEditor('requirementContent', 'requirement', '需求管理', ['生成设计', '一键优化', '保存']);
            updateActiveMenu('需求管理');
        }

        // 显示设计管理
        function showDesignManager() {
            if (!currentProjectId) {
                Utils.showAlert('请先选择一个项目', 'warning');
                return;
            }

            // 直接在主内容区域创建内容
            const mainArea = document.getElementById('mainContentArea');
            mainArea.innerHTML = '';

            // 创建设计管理容器
            const designDiv = document.createElement('div');
            designDiv.id = 'designContent';
            designDiv.style.cssText = `
                display: block !important;
                background-color: #f8f9fa !important;
                border: 2px solid #28a745 !important;
                min-height: 400px !important;
                padding: 20px !important;
                margin: 20px !important;
                border-radius: 8px !important;
            `;

            // 添加到主内容区域
            mainArea.appendChild(designDiv);

            // 加载Markdown编辑器
            loadMarkdownEditor('designContent', 'design', '设计管理', ['一键优化', '保存']);
            updateActiveMenu('设计管理');
        }

        // 显示规则管理
        function showRulesManager() {
            if (!currentProjectId) {
                Utils.showAlert('请先选择一个项目', 'warning');
                return;
            }

            // 直接在主内容区域创建内容
            const mainArea = document.getElementById('mainContentArea');
            mainArea.innerHTML = '';

            // 创建规则管理容器
            const rulesDiv = document.createElement('div');
            rulesDiv.id = 'rulesContent';
            rulesDiv.style.cssText = `
                display: block !important;
                background-color: #f8f9fa !important;
                border: 2px solid #ffc107 !important;
                min-height: 400px !important;
                padding: 20px !important;
                margin: 20px !important;
                border-radius: 8px !important;
            `;

            // 添加到主内容区域
            mainArea.appendChild(rulesDiv);

            // 加载Markdown编辑器
            loadMarkdownEditor('rulesContent', 'rules', '规则管理', ['保存']);
            updateActiveMenu('规则管理');
        }

        // 显示任务管理
        function showTaskManager() {
            if (!currentProjectId) {
                Utils.showAlert('请先选择一个项目', 'warning');
                return;
            }

            // 直接在主内容区域创建内容
            const mainArea = document.getElementById('mainContentArea');
            mainArea.innerHTML = '';

            // 创建任务管理容器
            const taskDiv = document.createElement('div');
            taskDiv.id = 'taskContent';
            taskDiv.style.cssText = `
                display: block !important;
                background-color: #f8f9fa !important;
                border: 2px solid #dc3545 !important;
                min-height: 400px !important;
                padding: 0 !important;
                margin: 20px !important;
                border-radius: 8px !important;
                overflow: hidden !important;
            `;

            // 创建iframe
            const iframe = document.createElement('iframe');
            iframe.id = 'taskFrame';
            iframe.src = `project_tasks.html?project_id=${currentProjectId}`;
            iframe.style.cssText = `
                width: 100% !important;
                height: calc(100vh - 120px) !important;
                border: none !important;
                display: block !important;
            `;

            taskDiv.appendChild(iframe);
            mainArea.appendChild(taskDiv);

            updateActiveMenu('任务管理');
        }

        // 显示知识库管理
        function showKnowledgeManager() {
            if (!currentProjectId) {
                Utils.showAlert('请先选择一个项目', 'warning');
                return;
            }
            // 直接在主内容区域创建内容
            const mainArea = document.getElementById('mainContentArea');
            mainArea.innerHTML = '';

            // 创建任务管理容器
            const knowledgeDiv = document.createElement('div');
            knowledgeDiv.id = 'knowledgeContent';
            knowledgeDiv.style.cssText = `
                display: block !important;
                background-color: #f8f9fa !important;
                border: 2px solid #dc3545 !important;
                min-height: 400px !important;
                padding: 0 !important;
                margin: 20px !important;
                border-radius: 8px !important;
                overflow: hidden !important;
            `;

            // 创建iframe
            const iframe = document.createElement('iframe');
            iframe.id = 'taskFrame';
            iframe.src = `knowledge_manager.html?project_id=${currentProjectId}`;
            iframe.style.cssText = `
                width: 100% !important;
                height: calc(100vh - 120px) !important;
                border: none !important;
                display: block !important;
            `;

            knowledgeDiv.appendChild(iframe);
            mainArea.appendChild(knowledgeDiv);

            updateActiveMenu('知识库管理');
        }

        // 隐藏所有内容区域
        function hideAllContent() {
            document.getElementById('dashboardContent').style.display = 'none';
            document.getElementById('projectManagementContent').style.display = 'none';
            document.getElementById('requirementContent').style.display = 'none';
            document.getElementById('designContent').style.display = 'none';
            document.getElementById('rulesContent').style.display = 'none';
            document.getElementById('taskContent').style.display = 'none';
        }

        // 更新活动菜单
        function updateActiveMenu(activeText) {
            // 移除所有活动状态
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // 添加活动状态到当前菜单
            document.querySelectorAll('.nav-link').forEach(link => {
                if (link.textContent.trim().includes(activeText)) {
                    link.classList.add('active');
                }
            });
        }

        // 加载Markdown编辑器
        function loadMarkdownEditor(containerId, apiEndpoint, title, buttons) {
            console.log('loadMarkdownEditor called:', containerId, apiEndpoint, title, buttons);
            const container = document.getElementById(containerId);

            if (!container) {
                console.error('Container not found:', containerId);
                return;
            }

            // 构建按钮HTML
            let buttonsHtml = '';
            buttons.forEach(buttonText => {
                if (buttonText === '生成需求') {
                    buttonsHtml += `<button class="btn btn-success btn-sm me-2" onclick="generateContent('${apiEndpoint}')">
                        <i class="fas fa-magic"></i> ${buttonText}
                    </button>`;
                } else if (buttonText === '一键优化') {
                    buttonsHtml += `<button class="btn btn-primary btn-sm me-2" onclick="optimizeContent('${apiEndpoint}')">
                        <i class="fas fa-arrow-up"></i> ${buttonText}
                    </button>`;
                } else if (buttonText === '保存') {
                    buttonsHtml += `<button class="btn btn-outline-primary btn-sm" onclick="saveContent('${apiEndpoint}')">
                        <i class="fas fa-save"></i> ${buttonText}
                    </button>`;
                }
            });

            // 创建编辑器HTML
            const editorHtml = `
                <div class="editor-container">
                    <div class="editor-toolbar">
                        <div class="toolbar-left">
                            <h6 class="mb-0">${title}</h6>
                        </div>
                        <div class="toolbar-right d-flex align-items-center">
                            <div class="me-3">
                                ${buttonsHtml}
                            </div>
                            <div class="btn-group btn-group-sm" role="group">
                                <input type="radio" class="btn-check" name="viewMode_${containerId}" id="edit_${containerId}" autocomplete="off" checked onchange="switchViewMode('${containerId}', 'edit')">
                                <label class="btn btn-outline-secondary" for="edit_${containerId}">编辑</label>

                                <input type="radio" class="btn-check" name="viewMode_${containerId}" id="preview_${containerId}" autocomplete="off" onchange="switchViewMode('${containerId}', 'preview')">
                                <label class="btn btn-outline-secondary" for="preview_${containerId}">预览</label>

                                <input type="radio" class="btn-check" name="viewMode_${containerId}" id="split_${containerId}" autocomplete="off" onchange="switchViewMode('${containerId}', 'split')">
                                <label class="btn btn-outline-secondary" for="split_${containerId}">混合</label>
                            </div>
                        </div>
                    </div>
                    <div class="editor-content">
                        <div class="editor-pane" id="editorPane_${containerId}">
                            <textarea class="editor-textarea" id="editor_${containerId}" placeholder="请输入${title}内容..." oninput="updatePreview('${containerId}')"></textarea>
                        </div>
                        <div class="preview-pane" id="previewPane_${containerId}" style="display: none;">
                            <div id="preview_${containerId}_content"></div>
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML = editorHtml;
            container.style.display = 'block';

            console.log('Container after setting display block:', container);
            console.log('Container display style:', container.style.display);
            console.log('Container computed style:', window.getComputedStyle(container).display);

            // 加载内容
            loadEditorContent(containerId, apiEndpoint);
        }

        // 切换视图模式
        function switchViewMode(containerId, mode) {
            const editorPane = document.getElementById(`editorPane_${containerId}`);
            const previewPane = document.getElementById(`previewPane_${containerId}`);

            if (mode === 'edit') {
                editorPane.style.display = 'block';
                previewPane.style.display = 'none';
                editorPane.style.flex = '1';
            } else if (mode === 'preview') {
                editorPane.style.display = 'none';
                previewPane.style.display = 'block';
                previewPane.style.flex = '1';
                updatePreview(containerId);
            } else if (mode === 'split') {
                editorPane.style.display = 'block';
                previewPane.style.display = 'block';
                editorPane.style.flex = '1';
                previewPane.style.flex = '1';
                updatePreview(containerId);
            }
        }

        // 更新预览
        function updatePreview(containerId) {
            const editor = document.getElementById(`editor_${containerId}`);
            const preview = document.getElementById(`preview_${containerId}_content`);

            if (editor && preview) {
                const markdownText = editor.value;
                const htmlContent = marked.parse(markdownText);
                preview.innerHTML = htmlContent;
            }
        }

        // 加载编辑器内容
        function loadEditorContent(containerId, apiEndpoint) {
            console.log('loadEditorContent called:', containerId, apiEndpoint, 'currentProjectId:', currentProjectId);
            if (!currentProjectId) {
                console.error('No currentProjectId set');
                return;
            }

            const fieldMap = {
                'requirement': 'requirement',
                'design': 'design',
                'rules': 'rules_constraint'
            };

            const url = `/api/projects/${currentProjectId}/${apiEndpoint}`;
            console.log('Fetching:', url);

            fetch(url)
                .then(response => {
                    console.log('Response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);
                    const editor = document.getElementById(`editor_${containerId}`);
                    console.log('Editor element:', editor);
                    if (editor) {
                        const fieldName = fieldMap[apiEndpoint];
                        const content = data[fieldName] || '';
                        console.log('Setting content:', content);
                        editor.value = content;
                        updatePreview(containerId);
                    } else {
                        console.error('Editor element not found:', `editor_${containerId}`);
                    }
                })
                .catch(error => {
                    console.error('加载内容失败:', error);
                    Utils.showAlert('加载内容失败', 'error');
                });
        }

        // 保存内容
        function saveContent(apiEndpoint) {
            if (!currentProjectId) return;

            const containerId = apiEndpoint + 'Content';
            const editor = document.getElementById(`editor_${containerId}`);
            if (!editor) return;

            const fieldMap = {
                'requirement': 'requirement',
                'design': 'design',
                'rules': 'rules_constraint'
            };

            const fieldName = fieldMap[apiEndpoint];
            const content = editor.value;

            const data = {};
            data[fieldName] = content;

            fetch(`/api/projects/${currentProjectId}/${apiEndpoint}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    Utils.showAlert('保存成功', 'success');
                } else {
                    Utils.showAlert('保存失败: ' + result.message, 'error');
                }
            })
            .catch(error => {
                console.error('保存失败:', error);
                Utils.showAlert('保存失败', 'error');
            });
        }

        // 生成内容（需求管理专用）
        function generateContent(apiEndpoint) {
            Utils.showAlert('生成功能正在开发中...', 'info');
        }

        // 优化内容
        function optimizeContent(apiEndpoint) {
            Utils.showAlert('优化功能正在开发中...', 'info');
        }

        // 页面加载时初始化
        $(document).ready(function() {
            loadProjectSelector();
            // 恢复上次选择的项目
            restoreLastProject();
        });
    </script>
</body>
</html>
