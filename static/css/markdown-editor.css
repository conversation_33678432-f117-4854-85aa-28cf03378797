/* Markdown编辑器样式 */
.markdown-editor {
    height: calc(100vh - 160px);
    display: flex;
    flex-direction: column;
}

.editor-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
    min-height: 60px;
}

.toolbar-left h5 {
    color: #495057;
    font-weight: 600;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.view-mode-toggle .btn-group {
    border-radius: 6px;
    overflow: hidden;
}

.view-mode-toggle .btn {
    border-radius: 0;
    font-size: 0.875rem;
    padding: 6px 12px;
}

.view-mode-toggle .btn:first-child {
    border-radius: 6px 0 0 6px;
}

.view-mode-toggle .btn:last-child {
    border-radius: 0 6px 6px 0;
}

.editor-content {
    flex: 1;
    display: flex;
    min-height: 0;
}

.editor-pane {
    flex: 1;
    border-right: 1px solid #dee2e6;
}

.editor-pane:last-child {
    border-right: none;
}

.editor-textarea {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 0;
    resize: none;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.6;
    padding: 20px;
    background-color: #ffffff;
}

.editor-textarea:focus {
    box-shadow: none;
    border-color: transparent;
    outline: none;
}

.preview-pane {
    flex: 1;
    background-color: #ffffff;
    overflow-y: auto;
}

.preview-content {
    padding: 20px;
    height: 100%;
}

.markdown-preview {
    line-height: 1.8;
    color: #333;
}

.markdown-preview h1 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
    color: #2c3e50;
}

.markdown-preview h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: #34495e;
}

.markdown-preview h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    color: #34495e;
}

.markdown-preview p {
    margin-bottom: 1rem;
    text-align: justify;
}

.markdown-preview strong {
    font-weight: 600;
    color: #2c3e50;
}

.markdown-preview em {
    font-style: italic;
    color: #7f8c8d;
}

.markdown-preview ul,
.markdown-preview ol {
    margin-bottom: 1rem;
    padding-left: 2rem;
}

.markdown-preview li {
    margin-bottom: 0.5rem;
}

.markdown-preview blockquote {
    border-left: 4px solid #3498db;
    padding-left: 1rem;
    margin: 1rem 0;
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0 4px 4px 0;
}

.markdown-preview code {
    background-color: #f1f2f6;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    color: #e74c3c;
}

.markdown-preview pre {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 1rem;
    border-radius: 6px;
    overflow-x: auto;
    margin: 1rem 0;
}

.markdown-preview pre code {
    background: none;
    color: inherit;
    padding: 0;
}

.markdown-preview table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

.markdown-preview th,
.markdown-preview td {
    border: 1px solid #dee2e6;
    padding: 0.75rem;
    text-align: left;
}

.markdown-preview th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.markdown-preview tr:nth-child(even) {
    background-color: #f8f9fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .editor-toolbar {
        flex-direction: column;
        gap: 10px;
        padding: 10px;
    }
    
    .toolbar-right {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .editor-content {
        flex-direction: column;
    }
    
    .editor-pane,
    .preview-pane {
        border-right: none;
        border-bottom: 1px solid #dee2e6;
    }
    
    .editor-pane:last-child,
    .preview-pane:last-child {
        border-bottom: none;
    }
    
    .view-mode-toggle .btn {
        font-size: 0.75rem;
        padding: 4px 8px;
    }
}

/* 滚动条样式 */
.preview-pane::-webkit-scrollbar,
.editor-textarea::-webkit-scrollbar {
    width: 8px;
}

.preview-pane::-webkit-scrollbar-track,
.editor-textarea::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.preview-pane::-webkit-scrollbar-thumb,
.editor-textarea::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.preview-pane::-webkit-scrollbar-thumb:hover,
.editor-textarea::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 加载和错误状态 */
.editor-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6c757d;
}

.editor-error {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #dc3545;
}

/* 按钮样式增强 */
.toolbar-right .btn {
    transition: all 0.2s ease;
}

.toolbar-right .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.toolbar-right .btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
}

.toolbar-right .btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
    border: none;
}

/* 消息提示样式 */
.alert {
    margin: 0;
    border-radius: 0;
    border-left: none;
    border-right: none;
}

.alert-success {
    background-color: #d1edff;
    border-color: #bee5eb;
    color: #0c5460;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}
