完成以下全部需求，不用询问，直接依次完成:

# 完善本AI任务管理系统
1. 完成代码中的TODO的任务
2. 自动化顺序执行任务过程中，可以停止任务执行。
3. 增加任务执行的日志管理功能，可通过progress_callback记录日志
4. 增加项目管理功能，包括：项目名称、工作目录、需求等
  - 4.1 每个项目可以有多个需求，每个需求会调用新TaskManager实现AI任务管理功能。
  
5. 增加UI界面管理，可实现项目管理、需求管理、任务管理。
  - 5.1 项目管理：新增项目、查看项目、删除项目(删除关联需求)
  - 5.2 需求管理：新增需求、查看需求、删除需求(删除关联任务)
  - 5.3 任务管理：任务拆解、查看任务、修改任务、删除任务（需要检查依赖）
  - 5.3.1 任务运行管理：启动任务（可选择自动化执行还是顺序执行）、停止任务、查看任务进度、查看任务日志等
  - 5.3.2 任务可以重置状态，然后可以再次执行
  - 5.3.3.只有pending状态的任务可以修改
  - 5.3.4.任务拆解后，也可以增加新的任务
  - 5.3.5.可以实现快速任务，一句话描述需求，系统自动生成任务并启动运行。
  - 5.3.6.任务可以重新开始，会使用新的会话ID，并总结之前的结果。
6. 可在线预览项目目录的各类文件，包括AI生成的文件。

# 迭代1
完成以下全部功能，不用询问，直接依次完成:

1. "增加项目"的界面使用表单界面，而不是一个一个对话框输入属性。[text](../.claude)
2. TaskManager init方法session_id从self.task_file_name的meta.session_id中获取。
4. 修改当前项目和需求的逻辑：
   - 删除需求管理功能
   - 每个项目只管理一个需求，不需要独立的需求管理功能，把需求直接作为项目属性即可
   - 项目需求以markdown渲染和编辑，编辑框尽量大
5. 项目列表界面改为“列表”形式，而不用表框：
   - 可显示：项目名称、创建时间、更新时间（检查项目目录/.aitest/tasks.json文件的meta数据-可参考/mnt/d/agent/auto-claude-tasks/demo/.taskai/task.json）、任务数量、已完成任务数
   - 选中一个项目可以：查看编辑项目的详情，包括项目的需求，可以选中生成/重新生成任务，可以跳转到任务列表。
   - 选中项目生成任务时，如果已经有任务，提示：已经有任务是否重新生成？如果任务在运行中，则需要中断任务运行。
6. 任务列表是与一个项目关联的：
   - 可显示项目目录/.aitest/tasks.json文件中的各任务详情
   - pending状态的任务可编辑、删除（需检查依赖）
7. 修复错误：
   - AttributeError: 'TaskManager' object has no attribute 'get_task_logs'
   - "GET /requirement/85242ce3-c852-49f4-8ec3-8a2cc1cf8fd6 HTTP/1.1" 500

完成以下全部功能，不用询问:
# 迭代2
1. 项目属性里要包括：工作目录，并可以在界面修改、查看详情。
2. 不要再修改TaskManager的init函数，不要自己生成session_id
3. TaskManager增加listTasks方法，返回所有任务(加载self.task_file_name文件的内容)，项目的任务列表调用这个方法查看任务。
4. 生成任务对话框，增加一个任务数量的输入框，可空。
5. 任务列表页面，点击任务，编辑和查看详情都提示：任务不存在
6. 任务列表页面，点击某个任务，可以运行（需要检查依赖）
7. 任务列表页面，点击某个任务，可查看这个任务的运行日志（可通过progress_callback捕获任务的运行日志）
8. 任务执行、完成等流程后，需要修改任务的状态
9. 解决错误：项目删除时 AttributeError: 'ProjectManager' object has no attribute 'requirements'
10. 任务列表可增加任务，弹出任务框输入新的任务，并可选是否立即执行。

完成以下全部功能，不用询问:
# 迭代3
1. 项目列表增加一个项目工作目录文件预览功能
  - 左侧树型结构的展示项目的文件目录
  - 点击文件，可在右侧预览文件内容，支持java/c/json/markdown/python等类型文件的优化展示
2. 在任何代码里都不要用uuid生成session_id，只能从task_file_name变量的文件里获取
  - task_file_name = 项目工作目录的 "/.taskai/task.json"文件，不要修改这个路径。
3. task_manager.py中任务属性的名称请以文件中/mnt/d/agent/auto-claude-tasks/demo/.taskai/task.json的为准进行修改。
4. 前端UI选中任务，查看日志错误： "GET /api/projects/fd23027e-aaf1-4d2b-ac63-ce93c0d5a354/tasks//logs HTTP/1.1" 405。
  - 通过progress_callback捕获任务的运行日志-大模型的返回消息，参考/mnt/d/agent/auto-claude-tasks/src/claude/claude_agent.py中调用progress_callback输出的内容。
  - 日志存储到data目录，可在界面查看运行日志

# 迭代4
完成以下全部功能，不用询问:

1. 优化任务的LLM交互日志管理功能：
  - LLM日志文件的写入过程参见：task_manager.py的_run_single_task方法
  - 当前所有log_manager.log_event()的日志写入都改用logging的info、warning、error、debug方法写入
  - LLM交互日志的格式可参考文件: data/logs/csdk项目C语言重构/task_1.log
  - UI界面的日志查看以友好格式显示LLM的交互日志：分左右，左侧是LLM的各种类型的输出（Assistant/User/Result/Stream），右侧是用户请求(Request)
  - 任务日志的展示列表中每一条最多显示3行(换行符标识为 "↵ ")，超过3行则显示"...还有n行"，然后点击查看所有行。展开所有行也可以收起。
  - 日志查看页面能需要实时刷新
  - LLM日志管理的功能可以都抽取到log_manager中（不要保留当前log_manager功能，当前log_manager功能都直接使用logging即可）。  

2. 项目的任务列表增加：重置功能，可以把项目的所有任务状态修改为pending状态，并去除result属性，设置meta的session_id属性=None。
3. 任务列表UI增加：任务的更新时间
4. 任务列表UI的任务修改功能，优化为与新增任务类似：表单样式展示任务的全部内容，可修改并保存。
5. 前端UI不要使用render_template渲染，修改为前端UI是纯静态页面，通过ajax请求后端REST API交互数据。
6. 解决以下错误：
  - 文件预览功能错误：   加载文件列表失败: 未知错误  "GET /api/projects/1758977931726/files?path= HTTP/1.1" 500

  问题1：
  1. 界面的重构基于当前的templates目录下的所有html文件，不是在static目录下另外创建新的UI界面。
  2. 完善之前的功能要求，通过浏览器检查功能是否正常。

  改进1:
  完成以下全部功能，不用询问:
  1. templates/project_tasks.html的日志查看修改为查看任务的LLM交互日志（src/log_manager.py的LLMLogManager）
  2. LLM交互日志采用独立的页面，日志格式参考文件: data/logs/俄罗斯方块/task_1.log
     - LLM日志页面，每条日志支持多行展示：把换行符↵ 替换为 在界面可以换行的样式
     - 优化展示样式，根据不同的类型(Request/Assistant/User/Result/Stream)显示不同的颜色(浅色系)
     - 每一条日志默认最多显示3行(换行符标识为 "↵ ")，超过3行则显示"...还有n行"，然后点击查看所有行。展开所有行也可以收起。
     - 在LLM交互页面，运行状态的任务支持实时刷新日志：只获取最新更新的日志，不要拉取整个日志。
  3. templates/project_tasks.html任务列表页面，编辑任务应该弹出任务编辑的表单进行编辑，可参考任务详情。
  4. 完成以上功能后，通过浏览器检查功能是否正常。

   改进2：
  完成以下全部功能，不用询问:
   1.重构templates目录下的所有html和src/web_app.py，不使用render_template渲染页面，修改为前后端分离模式：前端页面通过ajax调用后端api的方式，所有js脚本放在static目录下,可以让各html复用。
   2.保留render_template模式下的所有功能，web_app.py中已有/api的功能需要修改为与render_template模式相同的功能。 
   3.重构完毕，删除web_app.py中render_template模式的方法

# 迭代5
完成以下全部功能，不用询问:
1. 如图（docs/界面示意.png）所示重构界面, 左上角可下拉选择项目后作为当前项目，左边菜单的操作都是与当前这个项目相关的。
  - 项目管理列表，也可以选择一个项目切换为当前项目
  - 左侧菜单可以收起、展开
2. 需求管理菜单：对当前项目的需求(requirement)进行markdown格式编辑、保存，并支持预览功能。
3. 设计管理菜单：对当前项目的设计进行markdown格式编辑、保存，并支持预览功能。
  - src/project_manager.py中Project增加设计文档的属性
4. 规则管理：对当前项目的约束规则（rules_constraint）进行markdown格式编辑、保存，并支持预览功能。
5. 需求和设计管理的makrdown编辑功能，可复用一套编辑功能。
6. makrdown编辑功能示意如图："docs/markdown编辑区.png"
   - 顶部的操作栏窄一些，留出最大的编辑区域
   - 右上角的切换支持：仅编辑、仅预览、编辑+预览混合
   - 设计管理时：没有“生成设计”按钮
   - 规则管理时：只有“保存”按钮
7. 任务管理菜单保持现在static/project_tasks.html的功能不变。

 问题： 回退之前的修改，docs/界面示意.png只是一个示意图，就是在左上部增加一个当前项目的下拉框，可选择当前项目，然后左侧菜单的：需求管理、设计管理、规则管理、任务管理都是该项目的。
 继续完成以下任务，不用询问:
2. 需求管理菜单：对当前项目的需求(requirement)进行markdown格式编辑、保存，并支持预览功能。
3. 设计管理菜单：对当前项目的设计进行markdown格式编辑、保存，并支持预览功能。
  - src/project_manager.py中Project增加设计文档的属性
4. 规则管理：对当前项目的约束规则（rules_constraint）进行markdown格式编辑、保存，并支持预览功能。
5. 需求和设计管理的makrdown编辑功能，可复用一套编辑功能。
6. makrdown编辑功能示意如图："docs/markdown编辑区.png"
   - 顶部的操作栏窄一些，留出最大的编辑区域
   - 右上角的切换支持：仅编辑、仅预览、编辑+预览混合
   - 设计管理时：没有“生成设计”按钮
   - 规则管理时：只有“保存”按钮
7. 任务管理菜单保持现在static/project_tasks.html的功能不变。

解决以下问题，不用询问:：
1.点击左侧的需求管理,发送api调用的网络请求，返回：
{requirement: "使用html生成一个俄罗斯方块游戏"}，但右侧区域是空白。
项目管理菜单、设计管理菜单、规则管理菜单、任务管理菜单右侧区域也都是空白。
2.记住当前项目，访问主页是自动切换到当前项目，不要每次都下拉选择项目。
3.解决全部问题后，通过浏览器检查功能是否正常。

解决以下问题，不用询问：
1.如图所示，左侧菜单点击项目管理，右侧里又套了一层菜单，应该只显示项目列表。
2.左侧任务管理菜单，右侧显示的与"项目管理"菜单，应该修改为static/project_tasks.html的任务列表，但是要移除这个页面里的多余菜单(<!-- 侧边栏 -->部分)，只保留任务列表及任务列表上方的各类任务操作按钮。




# 迭代6
实现以下功能，不用询问：
增加知识库管理功能，关联到项目
   - 使用Milvus Lite的python包：pip install milvus-lite pymilvus
   - 左侧菜单增加知识库管理菜单（与当前项目关联），点击后打开一个页面，页面上显示当前项目的知识库列表，每个列表项可点击进入知识库详情页面。
   - 界面支持知识库检索测试

 解决以下问题，无限询问：
 1. 点击左侧知识库管理菜单，右侧区域（static/knowledge_manager.html）空白，控制台错误：
 
2. 请解决这个错误，并检查知识库的功能
- 知识库是与 static/index.html中下拉选择的项目关联的
- static/knowledge_manager.html显示当前项目的知识库列表，每个列表项是一个已经加到知识库里的文档，可删除
- 知识库列表显示文档的统计信息：大小、分块数量等
- static/knowledge_manager.html页面可上传文档到知识库
- 知识库界面支持检索功能，可检索当前知识库的信息

问题：
1. static/knowledge_manager.html中不需要新建知识库这些操作，每个项目默认都是自动创建一个知识库，这个界面管理的就是index.html中下拉选择项目的知识库。