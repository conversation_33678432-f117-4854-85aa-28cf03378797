from docx import Document

def find_parameter_tables(docx_path):
    """
    在Word文档中查找包含"参数"、"类型"、"说明"列的表格
    
    参数:
        docx_path (str): Word文档的路径
        
    返回:
        list: 包含所有匹配表格的列表，每个表格表示为行列表的列表
    """
    doc = Document(docx_path)
    matched_tables = []
    
    for table in doc.tables:
        # 检查表格是否有足够的行和列
        if len(table.rows) < 1 or len(table.columns) < 3:
            continue
            
        # 获取表头行（假设是第一行）
        header_cells = [cell.text.strip() for cell in table.rows[0].cells]
        
        # 检查表头是否包含目标列（不区分顺序）
        if "参数" in header_cells and "类型" in header_cells and "说明" in header_cells:
            # 提取整个表格内容
            table_data = []
            for row in table.rows:
                row_data = [cell.text.strip() for cell in row.cells]
                table_data.append(row_data)
            
            matched_tables.append(table_data)
    
    return matched_tables

# 使用示例
if __name__ == "__main__":
    docx_file = "your_document.docx"  # 替换为你的Word文档路径
    tables = find_parameter_tables(docx_file)
    
    print(f"找到 {len(tables)} 个匹配的表格:")
    for i, table in enumerate(tables, 1):
        print(f"\n表格 {i}:")
        for row in table:
            print(row)