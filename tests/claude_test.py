#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基础功能测试脚本
测试AI智能测试平台的核心功能
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.task_manager import TaskManager

def test_gen_tasks(task_manager:TaskManager, prd, num_tasks=None):
    print("\n" + "=" * 50)
    print("生成任务")
    print("=" * 50)
    
    try:
        def update_progress(progress: int, title:str, message: str=''):
            print(f"进度: {progress}% {title}:{message}")                    
        
        knowledge_base = None
        result = task_manager.gen_tasks(prd, num_tasks, knowledge_base, update_progress)
        if result["success"]:
            print(f"✓ 测试用例生成结果: {result}")
        else:
            print(f"✗ 测试用例失败: {result}")

    except Exception as e:
        print(f"✗ 测试用例失败: {e}")
        import traceback
        traceback.print_exc()
        return False
def test_run_tasks(task_manager:TaskManager):
    print("\n" + "=" * 50)
    print("运行任务")
    print("=" * 50)
    
    try:
        def update_progress(progress: int, title:str, message: str=''):
            print(f"进度: {progress}% {title}:{message}")
        result = task_manager.auto_run_tasks(False, update_progress)
        if result["success"]:
            print(f"✓ 测试用例生成结果: {result}")
        else:
            print(f"✗ 测试用例失败: {result}")
    
    except Exception as e:
        print(f"✗ 测试用例失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
def main():
    """主测试函数"""
    test_results = []
    demo_prd = "使用html生成一个俄罗斯方块游戏"
    csdk_prd = """# Overview
本文档旨在解决CCSP SDK中测试用例失败的问题。通过对测试失败用例进行分析和修复，确保SDK功能的正确性和稳定性，提高代码质量。

# Core Features
## 测试用例失败分析与修复
- **功能描述**：分析自动化测试中失败的测试用例，区分正常用例和异常用例的失败原因，并进行相应修复
- **重要性**：确保SDK功能正确性，避免错误的测试用例掩盖真实的功能缺陷
- **工作原理**：运行自动化测试，识别失败用例，分析失败原因，修复测试代码或数据

## 错误码一致性检查
- **功能描述**：检查并修复REST算法和软算法实现中错误码不一致的问题
- **重要性**：统一不同实现方式的错误码返回，保证接口一致性
- **工作原理**：对比[pki_service_restimpl.cpp]和[pki_service_softimpl.cpp]中的错误码返回逻辑，确保一致性

# Technical Architecture
## 系统组件
- 自动化测试程序：autotest/src/auto_CCSP.c
- 测试用例数据：autotest/data/目录下的csv文件,通过autotest/src/caseData.c加载。
- 待测试组件：CCSP SDK各功能模块

## 数据模型
- 测试用例数据结构
- 正常用例与异常用例分类
- 错误码定义与映射关系

# Development Roadmap
## MVP要求
1. 运行自动化测试并识别所有"[测试失败]"用例
2. 分析失败原因，区分正常用例和异常用例
3. 修复测试用例代码或数据问题
4. 解决REST和软算法错误码不一致问题
5. 验证所有测试用例通过

# Logical Dependency Chain
1. **基础工作**：首先需要理解现有测试框架和用例结构
2. **快速可见成果**：尽快运行测试并识别失败用例，建立问题清单
3. **原子性功能开发**：
   - 分析正常用例失败原因并修复
   - 分析异常用例失败原因并修复
   - 统一不同算法实现的错误码

# Risks and Mitigations
## 技术挑战
- **风险**：可能难以区分测试用例本身的错误和SDK功能缺陷
- **缓解措施**：仔细分析测试逻辑和预期结果，确保修复方向正确

## MVP构建
- **风险**：错误码不一致问题可能涉及多个模块和接口
- **缓解措施**：建立完整的错误码对照表，逐一验证接口返回

## 资源约束
- **风险**：需要大量时间分析和验证测试用例
- **缓解措施**：优先处理高频使用的功能模块测试用例

# Appendix
## 技术规范
1. 编译程序后在build/tests目录下运行自动化测试：`cd build/tests && ./auto_CCSP 1`
2. 正常用例测试失败需检查测试用例代码
3. 异常用例测试失败需检查错误码与API返回的一致性
4. 修复后需确保没有"[测试失败]"的用例
        """
        
    task_manager = TaskManager("demo", "/mnt/d/agent/auto-claude-tasks/demo")
    #task_manager = TaskManager("/mnt/d/aicode/csdk")
    #test_gen_tasks(task_manager,demo_prd,3)
    test_run_tasks(task_manager)
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
