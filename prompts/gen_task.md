# 目标与约束
请结合当前项目分析用户提交的需求，要求:
- {num_tasks}
- 每个任务应该是原子化的，专注于单一职责，有明确的执行目标和验证目标。
- 按逻辑顺序排列任务 - 考虑依赖关系和实现顺序。
- 早期任务应关注设置，首先实现核心功能，然后是高级特性。
- 为每个任务包含清晰的验证/测试方法。
- 设置适当的依赖ID（一个任务只能依赖ID较低的任务）
- 输出如下json结构, 写入{task_file_name}：
```json
{
"tasks": [
      {
        "id": 4, //任务ID,整数
        "title": "{任务标题}",
        "description": "{任务的详细描述}",
        "status": "pending|in_progress|completed|cancelled|failed", // 任务状执行态
        "dependencies": [2,3], // 该任务执行依赖的任务编号(整数数组)
        "priority": "high|medium|low", // 优先级
        "details": "{详细的实现指导}",
        "testStrategy": "{任务执行结果的验证策略}"
      }
  ],
"meta":{
  "projectName": "PRD Implementation", // 总结一个项目名
  "parallelizable": [[1,2,3],[4,5,6]], // 可并行处理的任务
  "generatedAt": "YYYY-MM-DD", // 生成时间
  "updatedAt": "YYYY-MM-DD", // 更新时间
  "session_id": "1234567890" // 最后更新的会话ID，初始为None
}
}
```
# 用户需求
