<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全栈编程助手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/markdown-editor.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f8f9fa;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header .navbar-brand {
            color: white !important;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .project-selector {
            min-width: 200px;
        }
        
        .sidebar {
            background-color: #343a40;
            min-height: calc(100vh - 60px);
            transition: margin-left 0.3s;
            width: 250px;
            position: fixed;
            left: 0;
            top: 60px;
            z-index: 1000;
        }
        
        .sidebar.collapsed {
            margin-left: -250px;
        }
        
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 12px 20px;
            border-bottom: 1px solid #495057;
            transition: all 0.3s;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: #495057;
        }
        
        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
        }
        
        .main-content {
            margin-left: 250px;
            padding: 20px;
            transition: margin-left 0.3s;
        }
        
        .main-content.expanded {
            margin-left: 0;
        }
        
        .toggle-sidebar {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            margin-right: 15px;
        }
        
        .content-frame {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-height: calc(100vh - 120px);
        }
        
        .breadcrumb {
            background: none;
            padding: 0;
            margin-bottom: 20px;
        }
        
        .breadcrumb-item a {
            color: #667eea;
            text-decoration: none;
        }
        
        .breadcrumb-item.active {
            color: #6c757d;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        
        .error {
            text-align: center;
            padding: 50px;
            color: #dc3545;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg header">
        <div class="container-fluid">
            <button class="toggle-sidebar" onclick="toggleSidebar()">
                <i class="bi bi-list"></i>
            </button>
            <a class="navbar-brand" href="#">
                <i class="bi bi-robot"></i>
                全栈编程助手
            </a>
            
            <!-- 项目选择器 -->
            <div class="ms-auto">
                <select class="form-select project-selector" id="projectSelector" onchange="switchProject()">
                    <option value="">选择项目...</option>
                </select>
            </div>
        </div>
    </nav>

    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="#" onclick="loadPage('projects')">
                    <i class="bi bi-folder"></i>
                    项目管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" onclick="loadPage('requirements')" id="requirementsLink">
                    <i class="bi bi-file-text"></i>
                    需求管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" onclick="loadPage('design')" id="designLink">
                    <i class="bi bi-palette"></i>
                    设计管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" onclick="loadPage('rules')" id="rulesLink">
                    <i class="bi bi-shield-check"></i>
                    规则管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" onclick="loadPage('tasks')" id="tasksLink">
                    <i class="bi bi-list-task"></i>
                    任务管理
                </a>
            </li>
        </ul>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content" id="mainContent">
        <!-- 面包屑导航 -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb" id="breadcrumb">
                <li class="breadcrumb-item"><a href="#" onclick="loadPage('projects')">首页</a></li>
            </ol>
        </nav>

        <!-- 内容区域 -->
        <div class="content-frame" id="contentFrame">
            <div class="loading">
                <i class="bi bi-hourglass-split"></i>
                <p>正在加载...</p>
            </div>
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/markdown-editor.js"></script>
    <script>
        let currentProject = null;
        let sidebarCollapsed = false;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadProjects();
            loadPage('projects');
        });

        // 切换侧边栏
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            
            sidebarCollapsed = !sidebarCollapsed;
            
            if (sidebarCollapsed) {
                sidebar.classList.add('collapsed');
                mainContent.classList.add('expanded');
            } else {
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('expanded');
            }
        }

        // 加载项目列表
        async function loadProjects() {
            try {
                const response = await fetch('/api/projects');
                const projects = await response.json();
                
                const selector = document.getElementById('projectSelector');
                selector.innerHTML = '<option value="">选择项目...</option>';
                
                projects.forEach(project => {
                    const option = document.createElement('option');
                    option.value = project.project_id;
                    option.textContent = project.name;
                    selector.appendChild(option);
                });
            } catch (error) {
                console.error('加载项目列表失败:', error);
            }
        }

        // 切换项目
        function switchProject() {
            const selector = document.getElementById('projectSelector');
            const projectId = selector.value;
            
            if (projectId) {
                currentProject = projectId;
                updateMenuState(true);
                // 如果当前不在项目管理页面，则切换到需求管理
                if (getCurrentPage() !== 'projects') {
                    loadPage('requirements');
                }
            } else {
                currentProject = null;
                updateMenuState(false);
                loadPage('projects');
            }
        }

        // 更新菜单状态
        function updateMenuState(hasProject) {
            const links = ['requirementsLink', 'designLink', 'rulesLink', 'tasksLink'];
            links.forEach(linkId => {
                const link = document.getElementById(linkId);
                if (hasProject) {
                    link.classList.remove('disabled');
                    link.style.opacity = '1';
                } else {
                    link.classList.add('disabled');
                    link.style.opacity = '0.5';
                }
            });
        }

        // 获取当前页面
        function getCurrentPage() {
            const activeLink = document.querySelector('.sidebar .nav-link.active');
            if (!activeLink) return 'projects';
            
            const onclick = activeLink.getAttribute('onclick');
            const match = onclick.match(/loadPage\('([^']+)'\)/);
            return match ? match[1] : 'projects';
        }

        // 加载页面内容
        async function loadPage(page) {
            // 更新菜单激活状态
            document.querySelectorAll('.sidebar .nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // 如果需要项目但没有选择项目，则提示
            if (['requirements', 'design', 'rules', 'tasks'].includes(page) && !currentProject) {
                showError('请先选择一个项目');
                return;
            }
            
            // 设置激活状态
            const currentLink = document.querySelector(`[onclick="loadPage('${page}')"]`);
            if (currentLink) {
                currentLink.classList.add('active');
            }
            
            // 更新面包屑
            updateBreadcrumb(page);
            
            // 显示加载状态
            showLoading();
            
            try {
                let content = '';
                switch (page) {
                    case 'projects':
                        content = await loadProjectsPage();
                        break;
                    case 'requirements':
                        content = await loadRequirementsPage();
                        break;
                    case 'design':
                        content = await loadDesignPage();
                        break;
                    case 'rules':
                        content = await loadRulesPage();
                        break;
                    case 'tasks':
                        content = await loadTasksPage();
                        break;
                    default:
                        content = '<div class="error">页面不存在</div>';
                }
                
                document.getElementById('contentFrame').innerHTML = content;
            } catch (error) {
                console.error('加载页面失败:', error);
                showError('加载页面失败: ' + error.message);
            }
        }

        // 更新面包屑导航
        function updateBreadcrumb(page) {
            const breadcrumb = document.getElementById('breadcrumb');
            const pageNames = {
                'projects': '项目管理',
                'requirements': '需求管理',
                'design': '设计管理',
                'rules': '规则管理',
                'tasks': '任务管理'
            };
            
            let html = '<li class="breadcrumb-item"><a href="#" onclick="loadPage(\'projects\')">首页</a></li>';
            
            if (page !== 'projects') {
                if (currentProject) {
                    const selector = document.getElementById('projectSelector');
                    const projectName = selector.options[selector.selectedIndex].text;
                    html += `<li class="breadcrumb-item">${projectName}</li>`;
                }
                html += `<li class="breadcrumb-item active">${pageNames[page]}</li>`;
            }
            
            breadcrumb.innerHTML = html;
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('contentFrame').innerHTML = `
                <div class="loading">
                    <i class="bi bi-hourglass-split"></i>
                    <p>正在加载...</p>
                </div>
            `;
        }

        // 显示错误信息
        function showError(message) {
            document.getElementById('contentFrame').innerHTML = `
                <div class="error">
                    <i class="bi bi-exclamation-triangle"></i>
                    <p>${message}</p>
                </div>
            `;
        }

        // 加载项目管理页面
        async function loadProjectsPage() {
            const response = await fetch('/projects.html');
            return await response.text();
        }

        // 加载需求管理页面
        async function loadRequirementsPage() {
            setTimeout(() => {
                loadMarkdownEditor('requirements', '需求管理', true, false);
            }, 100);
            return '<div id="requirementsEditor"></div>';
        }

        // 加载设计管理页面
        async function loadDesignPage() {
            setTimeout(() => {
                loadMarkdownEditor('design', '设计管理', false, false);
            }, 100);
            return '<div id="designEditor"></div>';
        }

        // 加载规则管理页面
        async function loadRulesPage() {
            setTimeout(() => {
                loadMarkdownEditor('rules', '规则管理', false, true);
            }, 100);
            return '<div id="rulesEditor"></div>';
        }

        // 加载任务管理页面
        async function loadTasksPage() {
            const response = await fetch('/project_tasks.html');
            return await response.text();
        }
    </script>
</body>
</html>
