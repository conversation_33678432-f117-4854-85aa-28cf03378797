<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务列表 - AI任务管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .main-content {
            min-height: 100vh;
        }
        .task-card {
            transition: transform 0.2s;
        }
        .task-card:hover {
            transform: translateY(-2px);
        }
        .requirement-container {
            max-height: 150px;
            overflow: hidden;
            position: relative;
        }
        .requirement-container.expanded {
            max-height: none;
        }
        .requirement-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: linear-gradient(transparent, #f8f9fa);
            display: flex;
            justify-content: center;
            align-items: end;
            padding-bottom: 8px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-tasks"></i> AI任务管理系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">
                            <i class="fas fa-home"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="projects.html">
                            <i class="fas fa-folder"></i> 项目管理
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.html">
                                <i class="fas fa-tachometer-alt"></i> 仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="projects.html">
                                <i class="fas fa-folder-open"></i> 项目列表
                            </a>
                        </li>
                    </ul>
                    
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>快速操作</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="TaskManager.showAddTaskModal()">
                                <i class="fas fa-plus"></i> 添加任务
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="pt-3">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">
                            <a href="projects.html" class="text-decoration-none text-muted">
                                <i class="fas fa-arrow-left"></i>
                            </a>
                            <span id="project-name">项目名称</span> - 任务列表
                        </h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <div class="btn-group me-2">
                                <button type="button" class="btn btn-success" id="run-tasks-btn">
                                    <i class="fas fa-play"></i> 运行任务
                                </button>
                                <button type="button" class="btn btn-warning" id="reset-tasks-btn">
                                    <i class="fas fa-undo"></i> 重置任务
                                </button>
                                <button type="button" class="btn btn-info" id="stop-execution-btn">
                                    <i class="fas fa-stop"></i> 停止运行
                                </button>
                                <button type="button" class="btn btn-danger" id="generate-tasks-btn">
                                    <i class="fas fa-redo"></i> 生成任务
                                </button>
                                <button type="button" class="btn btn-outline-info" id="view-files-btn">
                                    <i class="fas fa-folder-open"></i> 文件预览
                                </button>
                                <button type="button" class="btn btn-outline-secondary" id="refresh-tasks-btn">
                                    <i class="fas fa-sync-alt"></i> 刷新
                                </button>
                                <button type="button" class="btn btn-outline-primary" id="add-task-btn">
                                    <i class="fas fa-plus"></i> 添加任务
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 项目信息卡片 -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> 项目信息</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>项目名称:</strong> <span id="project-name-detail">-</span></p>
                                            <p><strong>LLM Provider:</strong> <span id="project-provider">-</span></p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>创建时间:</strong> <span id="project-created-at">-</span></p>
                                            <p><strong>Agent状态:</strong> <span id="project-run-state">-</span></p>
                                        </div>
                                    </div>
                                    <div class="mt-3" id="requirement-section" style="display: none;">
                                        <h6><i class="fas fa-file-alt"></i> 项目需求</h6>
                                        <div class="border rounded p-3 bg-light requirement-container" id="requirement-container">
                                            <div id="project-requirement"></div>
                                            <div class="requirement-overlay" id="requirement-overlay" style="display: none;">
                                                <button id="toggle-requirement" class="btn btn-sm btn-outline-primary" type="button">查看更多</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-chart-bar"></i> 任务统计</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="h4 mb-0 text-info" id="total-tasks">0</div>
                                            <small class="text-muted">总任务</small>
                                        </div>
                                        <div class="col-6">
                                            <div class="h4 mb-0 text-success" id="completed-tasks">0</div>
                                            <small class="text-muted">已完成</small>
                                        </div>
                                    </div>
                                    <div class="row text-center mt-3">
                                        <div class="col-6">
                                            <div class="h4 mb-0 text-warning" id="in-progress-tasks">0</div>
                                            <small class="text-muted">进行中</small>
                                        </div>
                                        <div class="col-6">
                                            <div class="h4 mb-0 text-danger" id="failed-tasks">0</div>
                                            <small class="text-muted">失败</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 任务列表容器 -->
                    <div id="tasks-container">
                        <div class="text-center py-5">
                            <i class="fas fa-spinner fa-spin fa-3x text-muted mb-3"></i>
                            <div>加载中...</div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 添加任务模态框 -->
    <div class="modal fade" id="addTaskModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加新任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addTaskForm">
                        <div class="mb-3">
                            <label for="taskTitle" class="form-label">任务标题 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="taskTitle" required
                                   placeholder="请输入任务标题">
                        </div>
                        <div class="mb-3">
                            <label for="taskDescription" class="form-label">任务描述</label>
                            <textarea class="form-control" id="taskDescription" rows="4"
                                      placeholder="请输入任务描述"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="taskPriority" class="form-label">优先级</label>
                            <select class="form-control" id="taskPriority">
                                <option value="low">低</option>
                                <option value="medium" selected>中</option>
                                <option value="high">高</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="taskDependencies" class="form-label">依赖任务 (可选)</label>
                            <input type="text" class="form-control" id="taskDependencies"
                                   placeholder="输入依赖的任务ID，多个用逗号分隔">
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i>
                                如果此任务依赖其他任务，请输入依赖任务的ID
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="executeImmediately">
                                <label class="form-check-label" for="executeImmediately">
                                    立即执行此任务
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="TaskManager.submitAddTask()">添加任务</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    
    <!-- 自定义JS -->
    <script src="static/js/common.js"></script>
    <script src="static/js/projects.js"></script>
    <script src="static/js/tasks.js"></script>
    
    <script>
        // 页面初始化
        $(document).ready(function() {
            const projectId = Router.getParam('id');
            if (!projectId) {
                Utils.showAlert('缺少项目ID参数', 'danger');
                Router.navigate('projects.html');
                return;
            }
            
            TaskManager.initTasksPage(projectId);
            
            // 绑定文件预览按钮
            $('#view-files-btn').on('click', function() {
                Router.navigate(`project_files.html?id=${projectId}`);
            });
            
            // 绑定需求展开/收起功能
            $(document).on('click', '#toggle-requirement', function() {
                const container = $('#requirement-container');
                const overlay = $('#requirement-overlay');
                const button = $(this);
                
                if (container.hasClass('expanded')) {
                    // 收起
                    container.removeClass('expanded');
                    button.text('查看更多');
                } else {
                    // 展开
                    container.addClass('expanded');
                    overlay.hide();
                    button.text('收起');
                }
            });
        });
        
        // 检查需求内容高度
        function checkRequirementHeight() {
            const container = $('#requirement-container');
            const content = $('#project-requirement');
            const overlay = $('#requirement-overlay');
            
            if (container.length && content.length) {
                // 临时移除max-height限制来测量实际高度
                container.css('max-height', 'none');
                const actualHeight = content.outerHeight();
                container.css('max-height', '150px');
                
                // 如果内容超出150px，显示展开按钮
                if (actualHeight > 150) {
                    overlay.show();
                }
            }
        }
        
        // 重写TaskManager的renderProjectInfo方法以支持需求高度检查
        const originalRenderProjectInfo = TaskManager.renderProjectInfo;
        TaskManager.renderProjectInfo = function(project) {
            originalRenderProjectInfo.call(this, project);
            $('#project-name-detail').text(project.name);
            setTimeout(checkRequirementHeight, 100);
        };
    </script>
</body>
</html>
