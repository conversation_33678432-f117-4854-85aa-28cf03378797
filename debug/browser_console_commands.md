# 浏览器控制台调试命令

请在浏览器开发者工具的控制台中依次执行以下命令来调试问题：

## 1. 基本容器检查
```javascript
// 获取容器
const container = document.getElementById('requirementContent');
console.log('Container:', container);

// 检查容器是否存在
if (container) {
    console.log('Container exists');
} else {
    console.log('Container NOT found');
}
```

## 2. 强制显示测试
```javascript
// 强制设置容器样式
const container = document.getElementById('requirementContent');
container.style.display = 'block';
container.style.position = 'fixed';
container.style.top = '100px';
container.style.left = '300px';
container.style.width = '500px';
container.style.height = '300px';
container.style.backgroundColor = 'red';
container.style.zIndex = '99999';
container.innerHTML = '<h1 style="color: white; padding: 20px;">强制显示测试</h1>';
```

## 3. 检查父容器
```javascript
// 检查主内容区域
const mainArea = document.getElementById('mainContentArea');
console.log('Main area:', mainArea);
console.log('Main area display:', window.getComputedStyle(mainArea).display);
console.log('Main area visibility:', window.getComputedStyle(mainArea).visibility);
console.log('Main area position:', mainArea.getBoundingClientRect());

// 检查main元素
const mainElement = document.querySelector('main.main-content');
console.log('Main element:', mainElement);
console.log('Main element display:', window.getComputedStyle(mainElement).display);
console.log('Main element position:', mainElement.getBoundingClientRect());
```

## 4. 检查容器层级
```javascript
const container = document.getElementById('requirementContent');
let current = container;
while (current) {
    console.log('Element:', current.tagName, current.id, current.className);
    console.log('Display:', window.getComputedStyle(current).display);
    console.log('Visibility:', window.getComputedStyle(current).visibility);
    console.log('Position:', window.getComputedStyle(current).position);
    console.log('Z-index:', window.getComputedStyle(current).zIndex);
    console.log('---');
    current = current.parentElement;
    if (current === document.body) break;
}
```

## 5. 检查所有内容容器
```javascript
const containers = [
    'dashboardContent',
    'projectManagementContent', 
    'requirementContent',
    'designContent',
    'rulesContent',
    'taskContent'
];

containers.forEach(id => {
    const element = document.getElementById(id);
    if (element) {
        const style = window.getComputedStyle(element);
        console.log(`${id}:`, {
            display: style.display,
            visibility: style.visibility,
            position: element.getBoundingClientRect(),
            zIndex: style.zIndex
        });
    } else {
        console.log(`${id}: NOT FOUND`);
    }
});
```

## 6. 滚动和视口检查
```javascript
// 检查页面滚动
console.log('Page scroll:', {
    scrollTop: document.documentElement.scrollTop,
    scrollLeft: document.documentElement.scrollLeft,
    clientHeight: document.documentElement.clientHeight,
    clientWidth: document.documentElement.clientWidth
});

// 检查容器是否在视口内
const container = document.getElementById('requirementContent');
const rect = container.getBoundingClientRect();
const inViewport = (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= window.innerHeight &&
    rect.right <= window.innerWidth
);
console.log('Container in viewport:', inViewport);
console.log('Container rect:', rect);
```

## 7. 手动创建测试元素
```javascript
// 在主内容区域创建一个测试元素
const mainArea = document.getElementById('mainContentArea');
const testDiv = document.createElement('div');
testDiv.style.cssText = `
    background: lime;
    color: black;
    padding: 20px;
    margin: 20px;
    border: 3px solid black;
    font-size: 20px;
    position: relative;
    z-index: 9999;
`;
testDiv.innerHTML = '手动创建的测试元素 - 如果你能看到这个，说明主内容区域正常';
mainArea.appendChild(testDiv);
```

## 8. 重置所有样式测试
```javascript
// 重置容器的所有样式
const container = document.getElementById('requirementContent');
container.style.cssText = `
    display: block !important;
    visibility: visible !important;
    position: static !important;
    width: auto !important;
    height: auto !important;
    background: yellow !important;
    border: 5px solid red !important;
    padding: 20px !important;
    margin: 20px !important;
    z-index: 9999 !important;
`;
container.innerHTML = '<h2>重置样式测试</h2><p>如果能看到这个黄色背景的内容，说明容器可以正常显示</p>';
```

## 执行顺序

1. 先执行命令1-3，检查基本情况
2. 如果强制显示测试（命令2）能看到红色区域，说明容器本身没问题
3. 如果看不到，执行命令4-6检查父容器和层级关系
4. 执行命令7创建测试元素，验证主内容区域是否正常
5. 最后执行命令8进行完全重置测试

根据每个命令的结果，我们可以逐步定位问题的具体原因。
