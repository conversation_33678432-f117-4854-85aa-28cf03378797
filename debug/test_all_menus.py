#!/usr/bin/env python3
"""
测试所有菜单功能
"""

import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

def test_all_menus():
    """测试所有菜单功能"""
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    try:
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        wait = WebDriverWait(driver, 10)
        
        print("🌐 访问主页...")
        driver.get('http://localhost:5000')
        
        # 等待页面加载
        time.sleep(2)
        
        print("📋 测试项目选择...")
        # 选择项目
        project_selector = wait.until(EC.presence_of_element_located((By.ID, 'currentProjectSelector')))
        if len(project_selector.find_elements(By.TAG_NAME, 'option')) > 1:
            project_selector.find_elements(By.TAG_NAME, 'option')[1].click()
            time.sleep(1)
            print("✅ 项目选择成功")
        else:
            print("❌ 没有可选择的项目")
            return
        
        # 测试菜单列表
        menus = [
            ('仪表板', 'showDashboard()'),
            ('项目管理', 'showProjectManagement()'),
            ('需求管理', 'showRequirementManager()'),
            ('设计管理', 'showDesignManager()'),
            ('规则管理', 'showRulesManager()'),
            ('任务管理', 'showTaskManager()')
        ]
        
        results = []
        
        for menu_name, js_function in menus:
            print(f"\n🔍 测试 {menu_name}...")
            
            try:
                # 执行JavaScript函数
                driver.execute_script(js_function)
                time.sleep(2)
                
                # 检查主内容区域是否有内容
                main_area = driver.find_element(By.ID, 'mainContentArea')
                content = main_area.get_attribute('innerHTML')
                
                if content and len(content.strip()) > 0:
                    print(f"✅ {menu_name} - 内容加载成功")
                    results.append((menu_name, True, "内容加载成功"))
                else:
                    print(f"❌ {menu_name} - 内容为空")
                    results.append((menu_name, False, "内容为空"))
                    
            except Exception as e:
                print(f"❌ {menu_name} - 错误: {str(e)}")
                results.append((menu_name, False, f"错误: {str(e)}"))
        
        # 输出测试结果
        print("\n" + "="*50)
        print("📊 测试结果总结")
        print("="*50)
        
        success_count = 0
        for menu_name, success, message in results:
            status = "✅" if success else "❌"
            print(f"{status} {menu_name}: {message}")
            if success:
                success_count += 1
        
        print(f"\n🎯 总计: {success_count}/{len(results)} 个菜单测试通过")
        
        if success_count == len(results):
            print("🎉 所有菜单功能正常！")
        else:
            print("⚠️  部分菜单需要修复")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        
    finally:
        if 'driver' in locals():
            driver.quit()

def test_api_endpoints():
    """测试API端点"""
    print("\n🔗 测试API端点...")
    
    endpoints = [
        '/api/projects',
        '/api/projects/test-project/requirement',
        '/api/projects/test-project/design',
        '/api/projects/test-project/rules'
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f'http://localhost:5000{endpoint}', timeout=5)
            if response.status_code == 200:
                print(f"✅ {endpoint} - 状态码: {response.status_code}")
            else:
                print(f"⚠️  {endpoint} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint} - 错误: {str(e)}")

if __name__ == '__main__':
    print("🚀 开始测试所有菜单功能...")
    
    # 首先测试API
    test_api_endpoints()
    
    # 然后测试前端菜单
    test_all_menus()
    
    print("\n✨ 测试完成！")
