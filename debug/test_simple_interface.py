#!/usr/bin/env python3
"""
测试简化版界面功能的脚本
"""

import sys
import os
import requests
import json
import time

def test_api_endpoints():
    """测试API端点"""
    base_url = "http://localhost:5000"
    
    print("=== 测试API端点 ===")
    
    # 测试项目列表
    try:
        response = requests.get(f"{base_url}/api/projects")
        print(f"项目列表API: {response.status_code}")
        if response.status_code == 200:
            projects = response.json()
            print(f"  找到 {len(projects)} 个项目")
            return projects
        else:
            print(f"  错误: {response.text}")
            return []
    except Exception as e:
        print(f"  连接失败: {e}")
        return []

def create_test_project():
    """创建测试项目"""
    base_url = "http://localhost:5000"
    
    print("\n=== 创建测试项目 ===")
    
    test_project = {
        "name": "测试项目",
        "work_dir": "/tmp/test_project",
        "description": "这是一个用于测试简化界面的项目",
        "requirement": "# 测试需求\n\n这是测试需求内容\n\n## 功能要求\n\n1. 用户登录\n2. 数据管理\n3. 报表生成",
        "design": "# 测试设计\n\n这是测试设计内容\n\n## 系统架构\n\n- 前端：React\n- 后端：Python Flask\n- 数据库：MySQL",
        "rules_constraint": "# 测试规则\n\n这是测试规则内容\n\n## 编码规范\n\n1. 使用PEP8规范\n2. 函数名使用下划线\n3. 类名使用驼峰命名",
        "provider": "local",
        "task_type": "新功能"
    }
    
    try:
        response = requests.post(f"{base_url}/api/projects", json=test_project)
        print(f"创建项目: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                project_id = result.get('project_id')
                print(f"  项目ID: {project_id}")
                return project_id
            else:
                print(f"  失败: {result}")
        else:
            print(f"  错误: {response.text}")
    except Exception as e:
        print(f"  连接失败: {e}")
    
    return None

def test_markdown_apis(project_id):
    """测试Markdown编辑器相关API"""
    if not project_id:
        print("\n=== 跳过Markdown编辑器API测试（无项目ID） ===")
        return
    
    base_url = "http://localhost:5000"
    
    print(f"\n=== 测试Markdown编辑器API (项目ID: {project_id}) ===")
    
    # 测试获取需求
    try:
        response = requests.get(f"{base_url}/api/projects/{project_id}/requirement")
        print(f"获取需求: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  需求内容长度: {len(data.get('requirement', ''))}")
        else:
            print(f"  错误: {response.text}")
    except Exception as e:
        print(f"  连接失败: {e}")
    
    # 测试获取设计
    try:
        response = requests.get(f"{base_url}/api/projects/{project_id}/design")
        print(f"获取设计: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  设计内容长度: {len(data.get('design', ''))}")
        else:
            print(f"  错误: {response.text}")
    except Exception as e:
        print(f"  连接失败: {e}")
    
    # 测试获取规则
    try:
        response = requests.get(f"{base_url}/api/projects/{project_id}/rules")
        print(f"获取规则: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  规则内容长度: {len(data.get('rules_constraint', ''))}")
        else:
            print(f"  错误: {response.text}")
    except Exception as e:
        print(f"  连接失败: {e}")
    
    # 测试更新需求
    requirement_data = {"requirement": "# 更新的需求\n\n这是通过API更新的需求内容\n\n## 新增功能\n\n1. 用户权限管理\n2. 数据导入导出"}
    try:
        response = requests.put(f"{base_url}/api/projects/{project_id}/requirement", json=requirement_data)
        print(f"更新需求: {response.status_code}")
        if response.status_code == 200:
            print("  需求更新成功")
        else:
            print(f"  错误: {response.text}")
    except Exception as e:
        print(f"  连接失败: {e}")

def test_static_files():
    """测试静态文件"""
    base_url = "http://localhost:5000"
    
    print("\n=== 测试静态文件 ===")
    
    files_to_test = [
        "/index.html",
        "/requirement_manager.html",
        "/design_manager.html", 
        "/rules_manager.html",
        "/project_tasks.html"
    ]
    
    for file_path in files_to_test:
        try:
            response = requests.get(f"{base_url}{file_path}")
            print(f"{file_path}: {response.status_code}")
        except Exception as e:
            print(f"{file_path}: 连接失败 - {e}")

def main():
    """主函数"""
    print("开始测试简化版界面功能...")
    print("请确保Web服务器正在运行 (python src/web_app.py)")
    print()
    
    # 等待用户确认
    input("按Enter键开始测试...")
    
    # 测试API端点
    projects = test_api_endpoints()
    
    # 测试静态文件
    test_static_files()
    
    # 创建测试项目（如果没有项目）
    project_id = None
    if not projects:
        project_id = create_test_project()
    else:
        project_id = projects[0]['project_id']
        print(f"\n使用现有项目进行测试: {project_id}")
    
    # 测试Markdown编辑器API
    test_markdown_apis(project_id)
    
    print("\n=== 测试完成 ===")
    print("请在浏览器中访问 http://localhost:5000 查看界面")
    print("操作步骤:")
    print("1. 在左上角选择一个项目")
    print("2. 点击左侧菜单中的需求管理、设计管理或规则管理")
    print("3. 测试Markdown编辑器的三种视图模式")
    print("4. 测试保存功能")

if __name__ == "__main__":
    main()
