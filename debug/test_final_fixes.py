#!/usr/bin/env python3
"""
测试最终修复的功能
"""

import sys
import os
import requests
import json
import time

def test_project_memory():
    """测试项目记忆功能"""
    base_url = "http://localhost:5000"
    
    print("=== 测试项目记忆功能 ===")
    
    try:
        response = requests.get(f"{base_url}/")
        print(f"主界面访问: {response.status_code}")
        if response.status_code == 200:
            content = response.text
            # 检查localStorage相关代码
            checks = [
                ('localStorage保存', 'localStorage.setItem' in content),
                ('localStorage恢复', 'localStorage.getItem' in content),
                ('恢复项目函数', 'restoreLastProject' in content),
                ('页面初始化调用', 'restoreLastProject();' in content)
            ]
            
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"  {check_name}: {status}")
                
            return all(result for _, result in checks)
        else:
            print(f"  错误: {response.text}")
            return False
    except Exception as e:
        print(f"  连接失败: {e}")
        return False

def test_api_with_content():
    """测试API返回内容"""
    base_url = "http://localhost:5000"
    
    print("\n=== 测试API内容 ===")
    
    # 获取项目列表
    try:
        response = requests.get(f"{base_url}/api/projects")
        if response.status_code == 200:
            projects = response.json()
            if projects:
                project_id = projects[0]['project_id']
                print(f"使用项目ID: {project_id}")
                
                # 更新需求内容为俄罗斯方块游戏
                requirement_data = {
                    "requirement": "使用html生成一个俄罗斯方块游戏"
                }
                
                response = requests.put(f"{base_url}/api/projects/{project_id}/requirement", json=requirement_data)
                print(f"更新需求: {response.status_code}")
                
                if response.status_code == 200:
                    # 验证更新成功
                    response = requests.get(f"{base_url}/api/projects/{project_id}/requirement")
                    if response.status_code == 200:
                        data = response.json()
                        content = data.get('requirement', '')
                        print(f"需求内容: {content}")
                        if "俄罗斯方块游戏" in content:
                            print("  ✅ 需求内容更新成功")
                            return project_id
                        else:
                            print("  ❌ 需求内容不匹配")
                    else:
                        print(f"  获取需求失败: {response.status_code}")
                else:
                    print(f"  更新需求失败: {response.text}")
            else:
                print("  没有找到项目")
        else:
            print(f"  获取项目列表失败: {response.status_code}")
    except Exception as e:
        print(f"  API测试失败: {e}")
    
    return None

def test_debug_logs():
    """测试调试日志功能"""
    base_url = "http://localhost:5000"
    
    print("\n=== 测试调试日志 ===")
    
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            content = response.text
            # 检查调试相关代码
            checks = [
                ('loadMarkdownEditor调试', 'console.log(\'loadMarkdownEditor called:' in content),
                ('loadEditorContent调试', 'console.log(\'loadEditorContent called:' in content),
                ('容器检查', 'Container not found' in content),
                ('编辑器检查', 'Editor element not found' in content),
                ('响应状态检查', 'Response status:' in content)
            ]
            
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"  {check_name}: {status}")
                
            return all(result for _, result in checks)
        else:
            print(f"  错误: {response.text}")
            return False
    except Exception as e:
        print(f"  连接失败: {e}")
        return False

def test_html_structure():
    """测试HTML结构"""
    base_url = "http://localhost:5000"
    
    print("\n=== 测试HTML结构 ===")
    
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            content = response.text
            # 检查关键HTML元素
            checks = [
                ('需求内容容器', 'id="requirementContent"' in content),
                ('设计内容容器', 'id="designContent"' in content),
                ('规则内容容器', 'id="rulesContent"' in content),
                ('任务内容容器', 'id="taskContent"' in content),
                ('主内容区域', 'id="mainContentArea"' in content),
                ('项目菜单', 'id="currentProjectMenu"' in content)
            ]
            
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"  {check_name}: {status}")
                
            return all(result for _, result in checks)
        else:
            print(f"  错误: {response.text}")
            return False
    except Exception as e:
        print(f"  连接失败: {e}")
        return False

def main():
    """主函数"""
    print("开始测试最终修复功能...")
    print("请确保Web服务器正在运行 (python src/web_app.py)")
    print()
    
    # 等待用户确认
    input("按Enter键开始测试...")
    
    # 测试项目记忆功能
    memory_ok = test_project_memory()
    
    # 测试API内容
    project_id = test_api_with_content()
    
    # 测试调试日志
    debug_ok = test_debug_logs()
    
    # 测试HTML结构
    structure_ok = test_html_structure()
    
    print("\n=== 测试完成 ===")
    print("请在浏览器中测试以下功能:")
    print("1. 刷新页面，检查是否自动选择上次的项目")
    print("2. 选择一个项目，点击需求管理")
    print("3. 检查右侧是否显示Markdown编辑器")
    print("4. 打开浏览器开发者工具，查看控制台日志")
    print("5. 测试其他管理功能（设计、规则、任务）")
    print()
    
    if all([memory_ok, debug_ok, structure_ok]):
        print("✅ 所有测试通过")
        if project_id:
            print(f"✅ API测试通过，项目ID: {project_id}")
        else:
            print("⚠️  API测试部分失败")
    else:
        print("❌ 部分测试失败，请检查代码")

if __name__ == "__main__":
    main()
