<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单显示测试</title>
    <style>
        .container {
            background-color: #f8f9fa;
            border: 2px solid #007bff;
            min-height: 400px;
            padding: 20px;
            margin: 20px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>简单显示测试</h1>
    
    <button onclick="showContainer()">显示容器</button>
    <button onclick="hideContainer()">隐藏容器</button>
    <button onclick="loadEditor()">加载编辑器</button>
    
    <div id="testContent" class="container hidden">
        <div id="testEditor">初始内容</div>
    </div>
    
    <div id="log"></div>
    
    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML += '<p>' + new Date().toLocaleTimeString() + ': ' + message + '</p>';
            console.log(message);
        }
        
        function showContainer() {
            const container = document.getElementById('testContent');
            log('显示容器前 - display: ' + container.style.display);
            log('显示容器前 - computed: ' + window.getComputedStyle(container).display);
            
            container.style.display = 'block';
            
            log('显示容器后 - display: ' + container.style.display);
            log('显示容器后 - computed: ' + window.getComputedStyle(container).display);
        }
        
        function hideContainer() {
            const container = document.getElementById('testContent');
            container.style.display = 'none';
            log('隐藏容器 - display: ' + container.style.display);
        }
        
        function loadEditor() {
            const container = document.getElementById('testContent');
            
            // 模拟loadMarkdownEditor的操作
            const editorHtml = `
                <div style="border: 1px solid #ccc; padding: 10px;">
                    <h3>模拟编辑器</h3>
                    <textarea style="width: 100%; height: 200px;">这是模拟的编辑器内容</textarea>
                    <button>保存</button>
                </div>
            `;
            
            log('加载编辑器前 - display: ' + container.style.display);
            container.innerHTML = editorHtml;
            container.style.display = 'block';
            log('加载编辑器后 - display: ' + container.style.display);
            log('加载编辑器后 - computed: ' + window.getComputedStyle(container).display);
        }
        
        // 页面加载时的状态
        window.onload = function() {
            const container = document.getElementById('testContent');
            log('页面加载 - display: ' + container.style.display);
            log('页面加载 - computed: ' + window.getComputedStyle(container).display);
            log('页面加载 - className: ' + container.className);
        };
    </script>
</body>
</html>
