#!/usr/bin/env python3
"""
知识库管理功能测试脚本
测试知识库的创建、存储、检索等功能
"""

import os
import sys
import json
import time
import requests
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from project_manager import ProjectManager
from knowledge_manager import KnowledgeManager

class KnowledgeManagerTester:
    """知识库管理器测试类"""
    
    def __init__(self):
        self.data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
        self.project_manager = ProjectManager(self.data_dir)
        self.knowledge_manager = KnowledgeManager(self.data_dir)
        self.test_project_id = None
        self.test_kb_id = None
        self.base_url = "http://localhost:5000"
        
    def log(self, message, level="INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
    
    def test_create_project(self):
        """测试创建项目"""
        self.log("开始测试创建项目...")
        
        try:
            project_name = f"知识库测试项目_{int(time.time())}"
            work_dir = os.path.join(self.data_dir, "test_kb_project")
            
            self.test_project_id = self.project_manager.create_project(
                name=project_name,
                work_dir=work_dir,
                description="用于测试知识库功能的项目",
                requirement="测试知识库的创建、文档添加和检索功能"
            )
            
            self.log(f"项目创建成功，ID: {self.test_project_id}")
            return True
        except Exception as e:
            self.log(f"项目创建失败: {e}", "ERROR")
            return False
    
    def test_create_knowledge_base(self):
        """测试创建知识库"""
        self.log("开始测试创建知识库...")
        
        try:
            kb_name = "测试知识库"
            kb_description = "这是一个用于测试的知识库"
            
            self.test_kb_id = self.knowledge_manager.create_knowledge_base(
                name=kb_name,
                description=kb_description,
                project_id=self.test_project_id
            )
            
            self.log(f"知识库创建成功，ID: {self.test_kb_id}")
            return True
        except Exception as e:
            self.log(f"知识库创建失败: {e}", "ERROR")
            return False
    
    def test_add_documents(self):
        """测试添加文档"""
        self.log("开始测试添加文档...")
        
        test_documents = [
            {
                "title": "Python基础教程",
                "content": "Python是一种高级编程语言，具有简洁的语法和强大的功能。它广泛应用于Web开发、数据科学、人工智能等领域。",
                "metadata": {"category": "编程教程", "language": "Python"}
            },
            {
                "title": "机器学习入门",
                "content": "机器学习是人工智能的一个分支，通过算法让计算机从数据中学习模式。常见的机器学习算法包括线性回归、决策树、神经网络等。",
                "metadata": {"category": "机器学习", "difficulty": "入门"}
            },
            {
                "title": "Web开发最佳实践",
                "content": "Web开发涉及前端和后端技术。前端主要使用HTML、CSS、JavaScript，后端可以使用Python、Java、Node.js等。RESTful API是现代Web应用的标准。",
                "metadata": {"category": "Web开发", "type": "最佳实践"}
            }
        ]
        
        success_count = 0
        for doc in test_documents:
            try:
                doc_id = self.knowledge_manager.add_document(
                    kb_id=self.test_kb_id,
                    title=doc["title"],
                    content=doc["content"],
                    metadata=doc["metadata"]
                )
                self.log(f"文档添加成功: {doc['title']} (ID: {doc_id})")
                success_count += 1
            except Exception as e:
                self.log(f"文档添加失败: {doc['title']} - {e}", "ERROR")
        
        self.log(f"文档添加完成，成功: {success_count}/{len(test_documents)}")
        return success_count == len(test_documents)
    
    def test_search_documents(self):
        """测试文档搜索"""
        self.log("开始测试文档搜索...")
        
        test_queries = [
            "Python编程",
            "机器学习算法",
            "Web开发技术",
            "人工智能",
            "数据科学"
        ]
        
        success_count = 0
        for query in test_queries:
            try:
                results = self.knowledge_manager.search_documents(
                    kb_id=self.test_kb_id,
                    query=query,
                    limit=5
                )
                self.log(f"搜索查询: '{query}' - 找到 {len(results)} 个结果")
                
                for i, result in enumerate(results[:3]):  # 只显示前3个结果
                    self.log(f"  结果 {i+1}: {result['title']} (相似度: {1-result['score']:.3f})")
                
                success_count += 1
            except Exception as e:
                self.log(f"搜索失败: '{query}' - {e}", "ERROR")
        
        self.log(f"搜索测试完成，成功: {success_count}/{len(test_queries)}")
        return success_count == len(test_queries)
    
    def test_knowledge_base_operations(self):
        """测试知识库基本操作"""
        self.log("开始测试知识库基本操作...")
        
        try:
            # 测试获取知识库列表
            kbs = self.knowledge_manager.list_knowledge_bases(self.test_project_id)
            self.log(f"项目知识库数量: {len(kbs)}")
            
            # 测试获取知识库详情
            kb = self.knowledge_manager.get_knowledge_base(self.test_kb_id)
            if kb:
                self.log(f"知识库详情: {kb.name} - {kb.document_count} 个文档")
            else:
                self.log("获取知识库详情失败", "ERROR")
                return False
            
            return True
        except Exception as e:
            self.log(f"知识库操作测试失败: {e}", "ERROR")
            return False
    
    def test_api_endpoints(self):
        """测试API端点"""
        self.log("开始测试API端点...")
        
        try:
            # 测试获取知识库列表API
            response = requests.get(f"{self.base_url}/api/projects/{self.test_project_id}/knowledge_bases")
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log(f"API测试成功: 获取知识库列表 - {len(data.get('knowledge_bases', []))} 个知识库")
                else:
                    self.log(f"API返回错误: {data.get('message')}", "ERROR")
                    return False
            else:
                self.log(f"API请求失败: HTTP {response.status_code}", "ERROR")
                return False
            
            # 测试搜索API
            search_data = {
                "query": "Python编程",
                "limit": 5
            }
            response = requests.post(
                f"{self.base_url}/api/knowledge_bases/{self.test_kb_id}/search",
                json=search_data
            )
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.log(f"API测试成功: 搜索文档 - {len(data.get('documents', []))} 个结果")
                else:
                    self.log(f"搜索API返回错误: {data.get('message')}", "ERROR")
                    return False
            else:
                self.log(f"搜索API请求失败: HTTP {response.status_code}", "ERROR")
                return False
            
            return True
        except Exception as e:
            self.log(f"API测试失败: {e}", "ERROR")
            return False
    
    def cleanup(self):
        """清理测试数据"""
        self.log("开始清理测试数据...")
        
        try:
            # 删除知识库
            if self.test_kb_id:
                success = self.knowledge_manager.delete_knowledge_base(self.test_kb_id)
                if success:
                    self.log("测试知识库删除成功")
                else:
                    self.log("测试知识库删除失败", "ERROR")
            
            # 删除项目
            if self.test_project_id:
                success = self.project_manager.delete_project(self.test_project_id)
                if success:
                    self.log("测试项目删除成功")
                else:
                    self.log("测试项目删除失败", "ERROR")
        except Exception as e:
            self.log(f"清理测试数据失败: {e}", "ERROR")
    
    def run_all_tests(self):
        """运行所有测试"""
        self.log("=" * 60)
        self.log("开始知识库管理功能测试")
        self.log("=" * 60)
        
        test_results = []
        
        # 运行测试
        tests = [
            ("创建项目", self.test_create_project),
            ("创建知识库", self.test_create_knowledge_base),
            ("添加文档", self.test_add_documents),
            ("搜索文档", self.test_search_documents),
            ("知识库操作", self.test_knowledge_base_operations),
            ("API端点", self.test_api_endpoints)
        ]
        
        for test_name, test_func in tests:
            self.log(f"\n开始测试: {test_name}")
            try:
                result = test_func()
                test_results.append((test_name, result))
                if result:
                    self.log(f"✓ {test_name} 测试通过")
                else:
                    self.log(f"✗ {test_name} 测试失败", "ERROR")
            except Exception as e:
                self.log(f"✗ {test_name} 测试异常: {e}", "ERROR")
                test_results.append((test_name, False))
        
        # 清理测试数据
        self.cleanup()
        
        # 输出测试结果
        self.log("\n" + "=" * 60)
        self.log("测试结果汇总")
        self.log("=" * 60)
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✓ 通过" if result else "✗ 失败"
            self.log(f"{test_name}: {status}")
            if result:
                passed += 1
        
        self.log(f"\n总计: {passed}/{total} 个测试通过")
        
        if passed == total:
            self.log("🎉 所有测试通过！知识库功能正常工作。")
        else:
            self.log("⚠️  部分测试失败，请检查相关功能。")
        
        return passed == total


def main():
    """主函数"""
    tester = KnowledgeManagerTester()
    success = tester.run_all_tests()
    
    # 写入测试报告
    report_file = os.path.join(os.path.dirname(__file__), "knowledge_manager_test_report.md")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(f"# 知识库管理功能测试报告\n\n")
        f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write(f"测试结果: {'✓ 全部通过' if success else '✗ 部分失败'}\n\n")
        f.write("## 测试内容\n\n")
        f.write("1. 创建测试项目\n")
        f.write("2. 创建知识库\n")
        f.write("3. 添加测试文档\n")
        f.write("4. 测试文档搜索\n")
        f.write("5. 测试知识库基本操作\n")
        f.write("6. 测试API端点\n\n")
        f.write("## 功能验证\n\n")
        f.write("- [x] 知识库创建和删除\n")
        f.write("- [x] 文档添加和向量化\n")
        f.write("- [x] 文档搜索和相似度计算\n")
        f.write("- [x] API接口正常工作\n")
        f.write("- [x] 项目关联功能\n\n")
        f.write("测试完成。\n")
    
    print(f"\n测试报告已保存到: {report_file}")
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
