#!/usr/bin/env python3
"""
测试JavaScript文件加载
"""

import requests
import time

def test_js_files():
    """测试JavaScript文件是否可以正常访问"""
    
    base_url = 'http://localhost:5000'
    
    # 测试的JavaScript文件
    js_files = [
        '/static/js/common.js',
        '/static/js/projects.js', 
        '/static/js/tasks.js'
    ]
    
    print("🔍 测试JavaScript文件访问...")
    
    for js_file in js_files:
        try:
            response = requests.get(f'{base_url}{js_file}', timeout=5)
            if response.status_code == 200:
                print(f"✅ {js_file} - 状态码: {response.status_code}")
            else:
                print(f"❌ {js_file} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ {js_file} - 错误: {str(e)}")

def test_simple_pages():
    """测试简化页面是否可以正常访问"""
    
    base_url = 'http://localhost:5000'
    
    # 测试的页面
    pages = [
        '/projects_simple.html',
        '/project_tasks.html?project_id=test-project'
    ]
    
    print("\n🌐 测试简化页面访问...")
    
    for page in pages:
        try:
            response = requests.get(f'{base_url}{page}', timeout=5)
            if response.status_code == 200:
                print(f"✅ {page} - 状态码: {response.status_code}")
                
                # 检查页面是否包含必要的JavaScript引用
                content = response.text
                if 'static/js/common.js' in content:
                    print(f"  ✅ 包含common.js引用")
                else:
                    print(f"  ❌ 缺少common.js引用")
                    
                if 'projects_simple.html' in page and 'static/js/projects.js' in content:
                    print(f"  ✅ 包含projects.js引用")
                elif 'project_tasks.html' in page and 'static/js/tasks.js' in content:
                    print(f"  ✅ 包含tasks.js引用")
                else:
                    print(f"  ❌ 缺少必要的JS引用")
                    
            else:
                print(f"❌ {page} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ {page} - 错误: {str(e)}")

def test_api_endpoints():
    """测试API端点"""
    
    base_url = 'http://localhost:5000'
    
    # 测试的API端点
    endpoints = [
        '/api/projects'
    ]
    
    print("\n🔗 测试API端点...")
    
    for endpoint in endpoints:
        try:
            response = requests.get(f'{base_url}{endpoint}', timeout=5)
            if response.status_code == 200:
                print(f"✅ {endpoint} - 状态码: {response.status_code}")
                try:
                    data = response.json()
                    print(f"  📊 返回数据类型: {type(data)}")
                    if isinstance(data, list):
                        print(f"  📊 项目数量: {len(data)}")
                except:
                    print(f"  ⚠️  响应不是有效的JSON")
            else:
                print(f"❌ {endpoint} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint} - 错误: {str(e)}")

if __name__ == '__main__':
    print("🚀 开始测试JavaScript加载修复...")
    
    # 测试JavaScript文件
    test_js_files()
    
    # 测试简化页面
    test_simple_pages()
    
    # 测试API端点
    test_api_endpoints()
    
    print("\n✨ 测试完成！")
    print("\n📋 修复总结:")
    print("1. 修正了projects_simple.html的JavaScript引用")
    print("2. 修正了project_tasks.html的JavaScript引用") 
    print("3. 使用正确的初始化函数名称")
    print("4. 添加了必要的依赖库引用")
    
    print("\n🎯 现在应该可以正常使用项目管理和任务管理功能了！")
