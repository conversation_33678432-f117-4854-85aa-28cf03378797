#!/usr/bin/env python3
"""
测试集成界面功能的脚本
"""

import sys
import os
import requests
import json
import time

def test_main_interface():
    """测试主界面"""
    base_url = "http://localhost:5000"
    
    print("=== 测试主界面 ===")
    
    try:
        response = requests.get(f"{base_url}/")
        print(f"主界面访问: {response.status_code}")
        if response.status_code == 200:
            content = response.text
            # 检查关键元素
            checks = [
                ('项目选择器', 'currentProjectSelector' in content),
                ('项目菜单', 'currentProjectMenu' in content),
                ('需求管理菜单', '需求管理' in content),
                ('设计管理菜单', '设计管理' in content),
                ('规则管理菜单', '规则管理' in content),
                ('任务管理菜单', '任务管理' in content),
                ('Markdown编辑器样式', 'editor-container' in content),
                ('Marked.js库', 'marked.min.js' in content),
                ('动态内容区域', 'mainContentArea' in content)
            ]
            
            for check_name, result in checks:
                status = "✅" if result else "❌"
                print(f"  {check_name}: {status}")
                
            return all(result for _, result in checks)
        else:
            print(f"  错误: {response.text}")
            return False
    except Exception as e:
        print(f"  连接失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    base_url = "http://localhost:5000"
    
    print("\n=== 测试API端点 ===")
    
    # 获取项目列表
    try:
        response = requests.get(f"{base_url}/api/projects")
        print(f"项目列表API: {response.status_code}")
        if response.status_code == 200:
            projects = response.json()
            print(f"  找到 {len(projects)} 个项目")
            if projects:
                return projects[0]['project_id']
        else:
            print(f"  错误: {response.text}")
    except Exception as e:
        print(f"  连接失败: {e}")
    
    return None

def test_markdown_apis(project_id):
    """测试Markdown编辑器相关API"""
    if not project_id:
        print("\n=== 跳过Markdown编辑器API测试（无项目ID） ===")
        return
    
    base_url = "http://localhost:5000"
    
    print(f"\n=== 测试Markdown编辑器API (项目ID: {project_id}) ===")
    
    # 测试各个API端点
    endpoints = [
        ('需求管理', 'requirement', 'requirement'),
        ('设计管理', 'design', 'design'),
        ('规则管理', 'rules', 'rules_constraint')
    ]
    
    for name, endpoint, field in endpoints:
        try:
            # 测试获取
            response = requests.get(f"{base_url}/api/projects/{project_id}/{endpoint}")
            print(f"{name} - 获取: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                content_length = len(data.get(field, ''))
                print(f"  内容长度: {content_length}")
                
                # 测试更新
                test_content = f"# 测试{name}\n\n这是通过API更新的{name}内容\n\n## 测试时间\n\n{time.strftime('%Y-%m-%d %H:%M:%S')}"
                update_data = {field: test_content}
                
                response = requests.put(f"{base_url}/api/projects/{project_id}/{endpoint}", json=update_data)
                print(f"{name} - 更新: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        print(f"  更新成功 ✅")
                    else:
                        print(f"  更新失败: {result}")
                else:
                    print(f"  更新错误: {response.text}")
            else:
                print(f"  获取错误: {response.text}")
                
        except Exception as e:
            print(f"  {name} API测试失败: {e}")

def test_javascript_functions():
    """测试JavaScript函数（通过检查HTML内容）"""
    base_url = "http://localhost:5000"
    
    print("\n=== 测试JavaScript函数 ===")
    
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            content = response.text
            
            # 检查关键JavaScript函数
            functions = [
                'showDashboard',
                'showProjectManagement', 
                'showRequirementManager',
                'showDesignManager',
                'showRulesManager',
                'showTaskManager',
                'hideAllContent',
                'updateActiveMenu',
                'loadMarkdownEditor',
                'switchViewMode',
                'updatePreview',
                'loadEditorContent',
                'saveContent',
                'switchCurrentProject'
            ]
            
            for func in functions:
                if f'function {func}(' in content:
                    print(f"  {func}: ✅")
                else:
                    print(f"  {func}: ❌")
                    
        else:
            print(f"  无法获取页面内容: {response.status_code}")
            
    except Exception as e:
        print(f"  测试失败: {e}")

def main():
    """主函数"""
    print("开始测试集成界面功能...")
    print("请确保Web服务器正在运行 (python src/web_app.py)")
    print()
    
    # 等待用户确认
    input("按Enter键开始测试...")
    
    # 测试主界面
    interface_ok = test_main_interface()
    
    # 测试API端点
    project_id = test_api_endpoints()
    
    # 测试Markdown编辑器API
    test_markdown_apis(project_id)
    
    # 测试JavaScript函数
    test_javascript_functions()
    
    print("\n=== 测试完成 ===")
    print("请在浏览器中访问 http://localhost:5000 查看新界面")
    print()
    print("测试步骤:")
    print("1. 在左上角选择一个项目")
    print("2. 观察左侧菜单是否显示项目相关功能")
    print("3. 点击左侧菜单中的各个功能（需求管理、设计管理、规则管理、任务管理）")
    print("4. 验证右侧区域直接显示功能内容，而不是嵌套页面")
    print("5. 测试Markdown编辑器的三种视图模式")
    print("6. 测试保存功能")
    print()
    
    if interface_ok:
        print("✅ 界面集成测试通过")
    else:
        print("❌ 界面集成测试失败，请检查HTML结构")

if __name__ == "__main__":
    main()
