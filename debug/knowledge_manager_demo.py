#!/usr/bin/env python3
"""
知识库管理功能演示脚本
展示修复后的知识库管理功能
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5005"

def demo_knowledge_manager():
    """演示知识库管理功能"""
    print("🎯 知识库管理功能演示")
    print("=" * 50)
    
    # 1. 获取项目列表
    print("\n📋 1. 获取项目列表")
    response = requests.get(f"{BASE_URL}/api/projects")
    projects = response.json()
    
    if not projects:
        print("❌ 没有找到项目")
        return
    
    project = projects[0]
    project_id = project['project_id']
    print(f"✅ 使用项目: {project['name']} (ID: {project_id})")
    
    # 2. 创建知识库
    print("\n📚 2. 创建知识库")
    kb_data = {
        "name": "演示知识库",
        "description": "这是一个演示用的知识库，包含AI和机器学习相关内容"
    }
    
    response = requests.post(
        f"{BASE_URL}/api/projects/{project_id}/knowledge_bases",
        json=kb_data
    )
    result = response.json()
    
    if result.get('success'):
        kb_id = result['kb_id']
        print(f"✅ 知识库创建成功: {kb_id}")
    else:
        print(f"❌ 知识库创建失败: {result.get('message')}")
        return
    
    # 3. 添加多个文档
    print("\n📄 3. 添加示例文档")
    documents = [
        {
            "title": "人工智能简介",
            "content": "人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。AI包括机器学习、深度学习、自然语言处理等多个子领域。",
            "metadata": {"category": "AI基础", "author": "系统"}
        },
        {
            "title": "机器学习算法",
            "content": "机器学习是AI的核心技术之一，包括监督学习、无监督学习和强化学习。常见算法有线性回归、决策树、随机森林、支持向量机、神经网络等。",
            "metadata": {"category": "机器学习", "author": "系统"}
        },
        {
            "title": "深度学习框架",
            "content": "深度学习框架如TensorFlow、PyTorch、Keras等为开发者提供了构建和训练神经网络的工具。这些框架支持GPU加速，简化了复杂模型的开发过程。",
            "metadata": {"category": "深度学习", "author": "系统"}
        },
        {
            "title": "自然语言处理",
            "content": "自然语言处理（NLP）是AI的重要应用领域，包括文本分类、情感分析、机器翻译、问答系统等。现代NLP大量使用Transformer架构和预训练模型。",
            "metadata": {"category": "NLP", "author": "系统"}
        }
    ]
    
    doc_ids = []
    for i, doc in enumerate(documents, 1):
        response = requests.post(
            f"{BASE_URL}/api/knowledge_bases/{kb_id}/documents",
            json=doc
        )
        result = response.json()
        
        if result.get('success'):
            doc_ids.append(result['doc_id'])
            print(f"✅ 文档 {i} 添加成功: {doc['title']}")
        else:
            print(f"❌ 文档 {i} 添加失败: {result.get('message')}")
    
    # 4. 演示搜索功能
    print("\n🔍 4. 演示搜索功能")
    search_queries = [
        "机器学习算法",
        "深度学习",
        "自然语言处理",
        "AI应用"
    ]
    
    for query in search_queries:
        print(f"\n🔎 搜索: '{query}'")
        search_data = {"query": query, "limit": 3}
        
        response = requests.post(
            f"{BASE_URL}/api/knowledge_bases/{kb_id}/search",
            json=search_data
        )
        result = response.json()
        
        if result.get('success'):
            documents = result['documents']
            print(f"📊 找到 {len(documents)} 个相关文档:")
            
            for doc in documents:
                similarity = 1 - doc['score'] / 100  # 转换为相似度
                print(f"  📄 {doc['title']} (相似度: {similarity:.3f})")
                print(f"     {doc['content'][:100]}...")
        else:
            print(f"❌ 搜索失败: {result.get('message')}")
    
    # 5. 获取知识库统计信息
    print("\n📊 5. 知识库统计信息")
    response = requests.get(f"{BASE_URL}/api/knowledge_bases/{kb_id}")
    result = response.json()
    
    if result.get('success'):
        kb = result['knowledge_base']
        print(f"📚 知识库名称: {kb['name']}")
        print(f"📝 描述: {kb['description']}")
        print(f"📄 文档数量: {kb['document_count']}")
        print(f"📅 创建时间: {kb['created_at']}")
    
    # 6. 清理演示数据
    print("\n🧹 6. 清理演示数据")
    response = requests.delete(f"{BASE_URL}/api/knowledge_bases/{kb_id}")
    result = response.json()
    
    if result.get('success'):
        print("✅ 演示知识库已删除")
    else:
        print(f"❌ 删除失败: {result.get('message')}")
    
    print("\n🎉 演示完成！")
    print("\n💡 您现在可以通过浏览器访问 http://127.0.0.1:5005/knowledge_manager.html")
    print("   来使用图形界面管理知识库。")

if __name__ == "__main__":
    print("🚀 启动知识库管理功能演示")
    print("请确保服务器正在运行...")
    time.sleep(1)
    
    try:
        demo_knowledge_manager()
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保应用程序正在运行")
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {str(e)}")
