<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>容器显示测试</title>
    <style>
        .test-container {
            width: 300px;
            height: 200px;
            border: 2px solid #007bff;
            background-color: #f8f9fa;
            padding: 20px;
            margin: 20px;
        }
    </style>
</head>
<body>
    <h1>容器显示测试</h1>
    
    <button onclick="testDisplay()">测试显示/隐藏</button>
    <button onclick="checkStyles()">检查样式</button>
    
    <div id="testContainer" class="test-container" style="display: none;">
        <h3>测试容器</h3>
        <p>这是一个测试容器，用于验证display样式的设置。</p>
    </div>
    
    <div id="output"></div>
    
    <script>
        function testDisplay() {
            const container = document.getElementById('testContainer');
            const currentDisplay = window.getComputedStyle(container).display;
            
            console.log('当前显示状态:', currentDisplay);
            
            if (currentDisplay === 'none') {
                container.style.display = 'block';
                console.log('设置为显示');
            } else {
                container.style.display = 'none';
                console.log('设置为隐藏');
            }
            
            checkStyles();
        }
        
        function checkStyles() {
            const container = document.getElementById('testContainer');
            const output = document.getElementById('output');
            
            const inlineStyle = container.style.display;
            const computedStyle = window.getComputedStyle(container).display;
            
            output.innerHTML = `
                <h3>样式检查结果:</h3>
                <p><strong>内联样式:</strong> ${inlineStyle}</p>
                <p><strong>计算样式:</strong> ${computedStyle}</p>
                <p><strong>容器可见:</strong> ${computedStyle !== 'none' ? '是' : '否'}</p>
            `;
            
            console.log('内联样式:', inlineStyle);
            console.log('计算样式:', computedStyle);
        }
        
        // 页面加载时检查初始状态
        window.onload = function() {
            checkStyles();
        };
    </script>
</body>
</html>
