#!/usr/bin/env python3
"""
测试前后端分离重构是否成功
"""

import os
import sys
import requests
import time
import threading
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent.parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

def test_static_files():
    """测试静态文件是否存在"""
    print("=== 测试静态文件 ===")
    
    static_dir = current_dir / "static"
    required_files = [
        "index.html",
        "projects.html", 
        "project_tasks.html",
        "project_files.html",
        "task_llm_logs.html",
        "js/common.js",
        "js/projects.js",
        "js/tasks.js",
        "js/files.js",
        "js/llm_logs.js"
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = static_dir / file_path
        if not full_path.exists():
            missing_files.append(str(file_path))
        else:
            print(f"✓ {file_path}")
    
    if missing_files:
        print(f"✗ 缺少文件: {missing_files}")
        return False
    else:
        print("✓ 所有静态文件都存在")
        return True

def test_web_app_import():
    """测试web_app模块是否可以正常导入"""
    print("\n=== 测试web_app模块导入 ===")
    
    try:
        from web_app import app, init_app
        print("✓ web_app模块导入成功")
        
        # 检查是否还有render_template的使用
        import inspect
        source = inspect.getsource(app.__class__)
        if 'render_template' in source:
            print("✗ 发现render_template的使用")
            return False
        else:
            print("✓ 没有发现render_template的使用")
        
        return True
    except Exception as e:
        print(f"✗ web_app模块导入失败: {e}")
        return False

def test_api_routes():
    """测试API路由是否正确配置"""
    print("\n=== 测试API路由配置 ===")
    
    try:
        from web_app import app
        
        # 获取所有路由
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append((rule.rule, list(rule.methods)))
        
        # 检查必要的API路由
        required_api_routes = [
            '/api/projects',
            '/api/projects/<project_id>',
            '/api/projects/<project_id>/tasks',
            '/api/projects/<project_id>/files'
        ]
        
        # 检查静态HTML路由
        required_html_routes = [
            '/',
            '/index.html',
            '/projects.html',
            '/project_tasks.html',
            '/project_files.html',
            '/task_llm_logs.html'
        ]
        
        existing_routes = [route[0] for route in routes]
        
        missing_api = []
        for route in required_api_routes:
            if route not in existing_routes:
                missing_api.append(route)
        
        missing_html = []
        for route in required_html_routes:
            if route not in existing_routes:
                missing_html.append(route)
        
        if missing_api:
            print(f"✗ 缺少API路由: {missing_api}")
        else:
            print("✓ 所有API路由都存在")
        
        if missing_html:
            print(f"✗ 缺少HTML路由: {missing_html}")
        else:
            print("✓ 所有HTML路由都存在")
        
        print(f"总共发现 {len(routes)} 个路由")
        
        return len(missing_api) == 0 and len(missing_html) == 0
        
    except Exception as e:
        print(f"✗ 路由测试失败: {e}")
        return False

def test_templates_cleanup():
    """测试旧模板文件是否已清理"""
    print("\n=== 测试模板文件清理 ===")
    
    templates_dir = current_dir / "templates"
    
    if not templates_dir.exists():
        print("✓ templates目录不存在")
        return True
    
    # 检查templates目录是否为空
    template_files = list(templates_dir.glob("*.html"))
    
    if template_files:
        print(f"✗ 发现残留的模板文件: {[f.name for f in template_files]}")
        return False
    else:
        print("✓ templates目录已清理干净")
        return True

def main():
    """主测试函数"""
    print("开始测试前后端分离重构...")
    print("=" * 50)
    
    tests = [
        test_static_files,
        test_web_app_import,
        test_api_routes,
        test_templates_cleanup
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ 测试失败: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！前后端分离重构成功！")
        return True
    else:
        print("❌ 部分测试失败，需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
