#!/usr/bin/env python3
"""
知识库管理修复测试脚本
测试知识库管理页面的修复效果和功能
"""

import requests
import json
import time
import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

BASE_URL = "http://127.0.0.1:5005"

def test_api_endpoint(url, method='GET', data=None, description=""):
    """测试API端点"""
    print(f"\n🔍 测试: {description}")
    print(f"📡 {method} {url}")
    
    try:
        if method == 'GET':
            response = requests.get(url)
        elif method == 'POST':
            response = requests.post(url, json=data, headers={'Content-Type': 'application/json'})
        elif method == 'DELETE':
            response = requests.delete(url)
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ 响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                return result
            except:
                print(f"✅ 响应: {response.text}")
                return response.text
        else:
            print(f"❌ 错误: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 异常: {str(e)}")
        return None

def test_knowledge_manager_pages():
    """测试知识库管理页面"""
    print("=" * 60)
    print("🧪 测试知识库管理页面")
    print("=" * 60)
    
    # 测试页面是否可以访问
    pages = [
        ("/knowledge_manager.html", "知识库管理页面"),
        ("/knowledge_detail.html", "知识库详情页面"),
        ("/static/js/common.js", "通用JavaScript文件"),
        ("/static/js/knowledge.js", "知识库JavaScript文件")
    ]
    
    for path, description in pages:
        url = f"{BASE_URL}{path}"
        response = requests.get(url)
        if response.status_code == 200:
            print(f"✅ {description}: 可访问")
        else:
            print(f"❌ {description}: 无法访问 (状态码: {response.status_code})")

def test_knowledge_base_api():
    """测试知识库API功能"""
    print("\n" + "=" * 60)
    print("🧪 测试知识库API功能")
    print("=" * 60)
    
    # 1. 获取项目列表
    projects = test_api_endpoint(f"{BASE_URL}/api/projects", description="获取项目列表")
    if not projects or len(projects) == 0:
        print("❌ 没有找到项目，无法继续测试知识库功能")
        return
    
    project_id = projects[0]['project_id']
    print(f"📋 使用项目ID: {project_id}")
    
    # 2. 获取知识库列表
    kb_list = test_api_endpoint(
        f"{BASE_URL}/api/projects/{project_id}/knowledge_bases",
        description="获取知识库列表"
    )
    
    # 3. 创建知识库
    kb_data = {
        "name": "测试知识库",
        "description": "这是一个用于测试的知识库"
    }
    
    create_result = test_api_endpoint(
        f"{BASE_URL}/api/projects/{project_id}/knowledge_bases",
        method='POST',
        data=kb_data,
        description="创建知识库"
    )
    
    if not create_result or not create_result.get('success'):
        print("❌ 创建知识库失败，无法继续测试")
        return
    
    kb_id = create_result.get('kb_id')
    print(f"📚 创建的知识库ID: {kb_id}")
    
    # 4. 获取知识库详情
    kb_detail = test_api_endpoint(
        f"{BASE_URL}/api/knowledge_bases/{kb_id}",
        description="获取知识库详情"
    )
    
    # 5. 添加文档
    doc_data = {
        "title": "测试文档",
        "content": "这是一个测试文档的内容，用于验证知识库的文档添加功能。",
        "metadata": {
            "category": "测试分类"
        }
    }
    
    add_doc_result = test_api_endpoint(
        f"{BASE_URL}/api/knowledge_bases/{kb_id}/documents",
        method='POST',
        data=doc_data,
        description="添加文档到知识库"
    )
    
    # 6. 搜索文档
    search_data = {
        "query": "测试",
        "limit": 5
    }
    
    search_result = test_api_endpoint(
        f"{BASE_URL}/api/knowledge_bases/{kb_id}/search",
        method='POST',
        data=search_data,
        description="搜索知识库文档"
    )
    
    # 7. 删除知识库
    delete_result = test_api_endpoint(
        f"{BASE_URL}/api/knowledge_bases/{kb_id}",
        method='DELETE',
        description="删除知识库"
    )
    
    return {
        'project_id': project_id,
        'kb_created': create_result.get('success', False) if create_result else False,
        'doc_added': add_doc_result.get('success', False) if add_doc_result else False,
        'search_worked': search_result.get('success', False) if search_result else False,
        'kb_deleted': delete_result.get('success', False) if delete_result else False
    }

def generate_test_report(test_results):
    """生成测试报告"""
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    if test_results:
        print(f"✅ 项目ID: {test_results['project_id']}")
        print(f"{'✅' if test_results['kb_created'] else '❌'} 知识库创建: {'成功' if test_results['kb_created'] else '失败'}")
        print(f"{'✅' if test_results['doc_added'] else '❌'} 文档添加: {'成功' if test_results['doc_added'] else '失败'}")
        print(f"{'✅' if test_results['search_worked'] else '❌'} 文档搜索: {'成功' if test_results['search_worked'] else '失败'}")
        print(f"{'✅' if test_results['kb_deleted'] else '❌'} 知识库删除: {'成功' if test_results['kb_deleted'] else '失败'}")
        
        all_success = all([
            test_results['kb_created'],
            test_results['doc_added'], 
            test_results['search_worked'],
            test_results['kb_deleted']
        ])
        
        print(f"\n🎯 总体结果: {'全部功能正常' if all_success else '部分功能存在问题'}")
        return all_success
    else:
        print("❌ 测试未能完成")
        return False

def main():
    """主函数"""
    print("🚀 开始测试知识库管理修复效果")
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    # 测试页面访问
    test_knowledge_manager_pages()
    
    # 测试API功能
    test_results = test_knowledge_base_api()
    
    # 生成测试报告
    success = generate_test_report(test_results)
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
