#!/usr/bin/env python3
"""
测试新界面功能的脚本
"""

import sys
import os
import requests
import json
import time

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

def test_api_endpoints():
    """测试API端点"""
    base_url = "http://localhost:5000"
    
    print("=== 测试API端点 ===")
    
    # 测试项目列表
    try:
        response = requests.get(f"{base_url}/api/projects")
        print(f"项目列表API: {response.status_code}")
        if response.status_code == 200:
            projects = response.json()
            print(f"  找到 {len(projects)} 个项目")
            if projects:
                project_id = projects[0]['project_id']
                print(f"  测试项目ID: {project_id}")
                
                # 测试需求管理API
                response = requests.get(f"{base_url}/api/projects/{project_id}/requirement")
                print(f"需求管理API: {response.status_code}")
                
                # 测试设计管理API
                response = requests.get(f"{base_url}/api/projects/{project_id}/design")
                print(f"设计管理API: {response.status_code}")
                
                # 测试规则管理API
                response = requests.get(f"{base_url}/api/projects/{project_id}/rules")
                print(f"规则管理API: {response.status_code}")
        else:
            print(f"  错误: {response.text}")
    except Exception as e:
        print(f"  连接失败: {e}")

def test_static_files():
    """测试静态文件"""
    base_url = "http://localhost:5000"
    
    print("\n=== 测试静态文件 ===")
    
    files_to_test = [
        "/main.html",
        "/css/markdown-editor.css", 
        "/js/markdown-editor.js",
        "/projects.html",
        "/project_tasks.html"
    ]
    
    for file_path in files_to_test:
        try:
            response = requests.get(f"{base_url}{file_path}")
            print(f"{file_path}: {response.status_code}")
        except Exception as e:
            print(f"{file_path}: 连接失败 - {e}")

def create_test_project():
    """创建测试项目"""
    base_url = "http://localhost:5000"
    
    print("\n=== 创建测试项目 ===")
    
    test_project = {
        "name": "测试项目",
        "work_dir": "/tmp/test_project",
        "description": "这是一个用于测试新界面的项目",
        "requirement": "# 测试需求\n\n这是测试需求内容",
        "design": "# 测试设计\n\n这是测试设计内容",
        "rules_constraint": "# 测试规则\n\n这是测试规则内容",
        "provider": "local",
        "task_type": "新功能"
    }
    
    try:
        response = requests.post(f"{base_url}/api/projects", json=test_project)
        print(f"创建项目: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                project_id = result.get('project_id')
                print(f"  项目ID: {project_id}")
                return project_id
            else:
                print(f"  失败: {result}")
        else:
            print(f"  错误: {response.text}")
    except Exception as e:
        print(f"  连接失败: {e}")
    
    return None

def test_markdown_editor_apis(project_id):
    """测试Markdown编辑器相关API"""
    if not project_id:
        print("\n=== 跳过Markdown编辑器API测试（无项目ID） ===")
        return
    
    base_url = "http://localhost:5000"
    
    print(f"\n=== 测试Markdown编辑器API (项目ID: {project_id}) ===")
    
    # 测试更新需求
    requirement_data = {"requirement": "# 更新的需求\n\n这是通过API更新的需求内容"}
    try:
        response = requests.put(f"{base_url}/api/projects/{project_id}/requirement", json=requirement_data)
        print(f"更新需求: {response.status_code}")
        if response.status_code == 200:
            print("  需求更新成功")
        else:
            print(f"  错误: {response.text}")
    except Exception as e:
        print(f"  连接失败: {e}")
    
    # 测试更新设计
    design_data = {"design": "# 更新的设计\n\n这是通过API更新的设计内容"}
    try:
        response = requests.put(f"{base_url}/api/projects/{project_id}/design", json=design_data)
        print(f"更新设计: {response.status_code}")
        if response.status_code == 200:
            print("  设计更新成功")
        else:
            print(f"  错误: {response.text}")
    except Exception as e:
        print(f"  连接失败: {e}")
    
    # 测试更新规则
    rules_data = {"rules_constraint": "# 更新的规则\n\n这是通过API更新的规则内容"}
    try:
        response = requests.put(f"{base_url}/api/projects/{project_id}/rules", json=rules_data)
        print(f"更新规则: {response.status_code}")
        if response.status_code == 200:
            print("  规则更新成功")
        else:
            print(f"  错误: {response.text}")
    except Exception as e:
        print(f"  连接失败: {e}")

def main():
    """主函数"""
    print("开始测试新界面功能...")
    print("请确保Web服务器正在运行 (python src/web_app.py)")
    print()
    
    # 等待用户确认
    input("按Enter键开始测试...")
    
    # 测试API端点
    test_api_endpoints()
    
    # 测试静态文件
    test_static_files()
    
    # 创建测试项目
    project_id = create_test_project()
    
    # 测试Markdown编辑器API
    test_markdown_editor_apis(project_id)
    
    print("\n=== 测试完成 ===")
    print("请在浏览器中访问 http://localhost:5000 查看新界面")

if __name__ == "__main__":
    main()
