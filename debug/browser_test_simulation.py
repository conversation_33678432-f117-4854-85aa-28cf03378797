#!/usr/bin/env python3
"""
模拟浏览器操作测试
"""

import requests
import time

def simulate_user_workflow():
    """模拟用户工作流程"""
    base_url = "http://localhost:5000"
    
    print("=== 模拟用户工作流程 ===")
    
    # 1. 访问主页
    print("1. 访问主页...")
    response = requests.get(f"{base_url}/")
    if response.status_code == 200:
        print("   ✅ 主页加载成功")
    else:
        print(f"   ❌ 主页加载失败: {response.status_code}")
        return False
    
    # 2. 获取项目列表
    print("2. 获取项目列表...")
    response = requests.get(f"{base_url}/api/projects")
    if response.status_code == 200:
        projects = response.json()
        if projects:
            project_id = projects[0]['project_id']
            project_name = projects[0]['name']
            print(f"   ✅ 找到项目: {project_name} (ID: {project_id})")
        else:
            print("   ❌ 没有找到项目")
            return False
    else:
        print(f"   ❌ 获取项目列表失败: {response.status_code}")
        return False
    
    # 3. 测试需求管理API
    print("3. 测试需求管理...")
    response = requests.get(f"{base_url}/api/projects/{project_id}/requirement")
    if response.status_code == 200:
        data = response.json()
        requirement = data.get('requirement', '')
        print(f"   ✅ 需求内容: {requirement[:50]}...")
        
        # 验证是否包含俄罗斯方块游戏
        if "俄罗斯方块游戏" in requirement:
            print("   ✅ 需求内容正确")
        else:
            print("   ⚠️  需求内容可能不是最新的")
    else:
        print(f"   ❌ 获取需求失败: {response.status_code}")
        return False
    
    # 4. 测试设计管理API
    print("4. 测试设计管理...")
    response = requests.get(f"{base_url}/api/projects/{project_id}/design")
    if response.status_code == 200:
        data = response.json()
        design = data.get('design', '')
        print(f"   ✅ 设计内容长度: {len(design)}")
    else:
        print(f"   ❌ 获取设计失败: {response.status_code}")
        return False
    
    # 5. 测试规则管理API
    print("5. 测试规则管理...")
    response = requests.get(f"{base_url}/api/projects/{project_id}/rules")
    if response.status_code == 200:
        data = response.json()
        rules = data.get('rules_constraint', '')
        print(f"   ✅ 规则内容长度: {len(rules)}")
    else:
        print(f"   ❌ 获取规则失败: {response.status_code}")
        return False
    
    # 6. 测试任务管理页面
    print("6. 测试任务管理页面...")
    response = requests.get(f"{base_url}/project_tasks.html")
    if response.status_code == 200:
        print("   ✅ 任务管理页面可访问")
    else:
        print(f"   ❌ 任务管理页面访问失败: {response.status_code}")
        return False
    
    return True

def check_javascript_functions():
    """检查JavaScript函数是否正确定义"""
    base_url = "http://localhost:5000"
    
    print("\n=== 检查JavaScript函数 ===")
    
    response = requests.get(f"{base_url}/")
    if response.status_code == 200:
        content = response.text
        
        # 检查关键函数
        functions = [
            'showRequirementManager',
            'showDesignManager', 
            'showRulesManager',
            'showTaskManager',
            'loadMarkdownEditor',
            'loadEditorContent',
            'switchCurrentProject',
            'restoreLastProject'
        ]
        
        all_found = True
        for func in functions:
            if f'function {func}(' in content:
                print(f"   ✅ {func}")
            else:
                print(f"   ❌ {func}")
                all_found = False
        
        return all_found
    else:
        print(f"   ❌ 无法获取页面内容: {response.status_code}")
        return False

def check_html_elements():
    """检查HTML元素是否正确"""
    base_url = "http://localhost:5000"
    
    print("\n=== 检查HTML元素 ===")
    
    response = requests.get(f"{base_url}/")
    if response.status_code == 200:
        content = response.text
        
        # 检查关键元素
        elements = [
            ('项目选择器', 'id="currentProjectSelector"'),
            ('项目菜单', 'id="currentProjectMenu"'),
            ('需求内容区', 'id="requirementContent"'),
            ('设计内容区', 'id="designContent"'),
            ('规则内容区', 'id="rulesContent"'),
            ('任务内容区', 'id="taskContent"'),
            ('主内容区', 'id="mainContentArea"'),
            ('仪表板内容', 'id="dashboardContent"')
        ]
        
        all_found = True
        for name, element in elements:
            if element in content:
                print(f"   ✅ {name}")
            else:
                print(f"   ❌ {name}")
                all_found = False
        
        return all_found
    else:
        print(f"   ❌ 无法获取页面内容: {response.status_code}")
        return False

def main():
    """主函数"""
    print("开始浏览器功能模拟测试...")
    print("这将模拟用户在浏览器中的操作流程")
    print()
    
    # 模拟用户工作流程
    workflow_ok = simulate_user_workflow()
    
    # 检查JavaScript函数
    js_ok = check_javascript_functions()
    
    # 检查HTML元素
    html_ok = check_html_elements()
    
    print("\n=== 测试总结 ===")
    
    if workflow_ok:
        print("✅ 用户工作流程测试通过")
    else:
        print("❌ 用户工作流程测试失败")
    
    if js_ok:
        print("✅ JavaScript函数检查通过")
    else:
        print("❌ JavaScript函数检查失败")
    
    if html_ok:
        print("✅ HTML元素检查通过")
    else:
        print("❌ HTML元素检查失败")
    
    if all([workflow_ok, js_ok, html_ok]):
        print("\n🎉 所有测试通过！功能应该正常工作")
        print("\n请在浏览器中验证:")
        print("1. 访问 http://localhost:5000")
        print("2. 选择一个项目（应该自动恢复上次选择）")
        print("3. 点击需求管理，查看右侧是否显示编辑器")
        print("4. 测试三种视图模式：编辑、预览、混合")
        print("5. 测试保存功能")
        print("6. 测试其他管理功能")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
