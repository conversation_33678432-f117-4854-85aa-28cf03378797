# 浏览器控制台检查指南

## 操作步骤

1. **打开浏览器开发者工具**
   - 按 F12 或右键点击页面选择"检查"
   - 切换到 Console（控制台）标签

2. **访问页面**
   - 访问 http://localhost:5000
   - 观察控制台是否有错误信息

3. **选择项目**
   - 在左上角项目选择器中选择一个项目
   - 观察控制台输出

4. **点击需求管理**
   - 点击左侧菜单的"需求管理"
   - 观察控制台的详细输出

## 预期的控制台输出

```
showRequirementManager called, currentProjectId: [项目ID]
Hiding all content...
Container before showing: [HTMLDivElement]
Container display after setting block: block
Loading markdown editor...
loadMarkdownEditor called: requirementContent requirement 需求管理 (3) ['生成需求', '一键优化', '保存']
Container after setting display block: [HTMLDivElement]
Container display style: block
Container computed style: block
loadEditorContent called: requirementContent requirement currentProjectId: [项目ID]
Fetching: /api/projects/[项目ID]/requirement
Response status: 200
Response data: {requirement: '使用html生成一个俄罗斯方块游戏'}
Editor element: [HTMLTextAreaElement]
Setting content: 使用html生成一个俄罗斯方块游戏
Container display after timeout: block
Container computed style: block
Updating active menu...
showRequirementManager completed
```

## 检查要点

1. **容器元素检查**
   - 在控制台中输入: `document.getElementById('requirementContent')`
   - 检查返回的元素是否存在

2. **样式检查**
   - 在控制台中输入: `document.getElementById('requirementContent').style.display`
   - 应该返回 "block"

3. **计算样式检查**
   - 在控制台中输入: `window.getComputedStyle(document.getElementById('requirementContent')).display`
   - 应该返回 "block"

4. **内容检查**
   - 在控制台中输入: `document.getElementById('requirementContent').innerHTML`
   - 应该包含编辑器的HTML内容

5. **编辑器元素检查**
   - 在控制台中输入: `document.getElementById('editor_requirementContent')`
   - 应该返回textarea元素

## 可能的问题

1. **CSS冲突**
   - 检查是否有其他CSS规则覆盖了display属性
   - 在Elements标签中检查元素的样式

2. **父容器问题**
   - 检查mainContentArea是否正常显示
   - 检查main元素是否有问题

3. **JavaScript错误**
   - 查看控制台是否有JavaScript错误
   - 检查函数调用是否正常

## 手动测试命令

在浏览器控制台中执行以下命令进行手动测试：

```javascript
// 1. 检查容器
const container = document.getElementById('requirementContent');
console.log('Container:', container);

// 2. 强制显示
container.style.display = 'block';
console.log('Display set to:', container.style.display);

// 3. 检查计算样式
console.log('Computed display:', window.getComputedStyle(container).display);

// 4. 添加测试内容
container.innerHTML = '<div style="background: red; padding: 20px;">测试内容</div>';

// 5. 检查父容器
const mainArea = document.getElementById('mainContentArea');
console.log('Main area:', mainArea);
console.log('Main area display:', window.getComputedStyle(mainArea).display);
```

如果手动设置后能看到红色的测试内容，说明容器本身没问题，问题在于JavaScript逻辑。
如果手动设置后仍然看不到内容，说明是CSS或HTML结构问题。
