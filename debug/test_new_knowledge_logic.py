#!/usr/bin/env python3
"""
测试新的知识库逻辑
验证项目自动创建知识库和文档管理功能
"""

import requests
import json
import time
import sys
import os

BASE_URL = "http://127.0.0.1:5005"

def test_auto_knowledge_base_creation():
    """测试项目自动创建知识库功能"""
    print("🎯 测试项目自动创建知识库功能")
    print("=" * 50)
    
    # 1. 获取项目列表
    print("\n📋 1. 获取项目列表")
    response = requests.get(f"{BASE_URL}/api/projects")
    projects = response.json()
    
    if not projects:
        print("❌ 没有找到项目")
        return False
    
    project = projects[0]
    project_id = project['project_id']
    project_name = project['name']
    print(f"✅ 使用项目: {project_name} (ID: {project_id})")
    
    # 2. 测试获取项目知识库（应该自动创建）
    print(f"\n📚 2. 获取项目 '{project_name}' 的知识库")
    response = requests.get(f"{BASE_URL}/api/projects/{project_id}/knowledge_bases")
    result = response.json()
    
    if result.get('success') and result.get('knowledge_base'):
        kb = result['knowledge_base']
        kb_id = kb['kb_id']
        print(f"✅ 自动创建/获取知识库成功:")
        print(f"   📚 知识库名称: {kb['name']}")
        print(f"   📝 描述: {kb['description']}")
        print(f"   🆔 ID: {kb_id}")
        print(f"   📄 文档数量: {kb['document_count']}")
        print(f"   📅 创建时间: {kb['created_at']}")
    else:
        print(f"❌ 获取知识库失败: {result.get('message')}")
        return False
    
    # 3. 测试添加文档
    print(f"\n📄 3. 向知识库添加测试文档")
    test_documents = [
        {
            "title": f"{project_name} - 项目概述",
            "content": f"这是 {project_name} 项目的概述文档。项目旨在实现高质量的软件解决方案。",
            "metadata": {"category": "项目文档", "type": "概述"}
        },
        {
            "title": f"{project_name} - 技术规范",
            "content": f"{project_name} 项目采用现代化的技术栈，包括前端框架、后端API和数据库设计。",
            "metadata": {"category": "技术文档", "type": "规范"}
        },
        {
            "title": f"{project_name} - 用户指南",
            "content": f"本指南介绍如何使用 {project_name} 系统的各项功能，包括基本操作和高级特性。",
            "metadata": {"category": "用户文档", "type": "指南"}
        }
    ]
    
    doc_ids = []
    for i, doc in enumerate(test_documents, 1):
        response = requests.post(
            f"{BASE_URL}/api/knowledge_bases/{kb_id}/documents",
            json=doc
        )
        result = response.json()
        
        if result.get('success'):
            doc_ids.append(result['doc_id'])
            print(f"✅ 文档 {i} 添加成功: {doc['title']}")
        else:
            print(f"❌ 文档 {i} 添加失败: {result.get('message')}")
    
    # 4. 测试搜索功能
    print(f"\n🔍 4. 测试知识库搜索功能")
    search_queries = [
        f"{project_name}",
        "技术规范",
        "用户指南",
        "项目概述"
    ]
    
    for query in search_queries:
        print(f"\n🔎 搜索: '{query}'")
        search_data = {"query": query, "limit": 5}
        
        response = requests.post(
            f"{BASE_URL}/api/knowledge_bases/{kb_id}/search",
            json=search_data
        )
        result = response.json()
        
        if result.get('success'):
            documents = result['documents']
            print(f"📊 找到 {len(documents)} 个相关文档:")
            
            for doc in documents:
                similarity = 1 - doc['score'] / 100
                print(f"  📄 {doc['title']} (相似度: {similarity:.3f})")
                if doc.get('metadata'):
                    print(f"     🏷️ 分类: {doc['metadata'].get('category', '未分类')}")
        else:
            print(f"❌ 搜索失败: {result.get('message')}")
    
    # 5. 验证知识库信息更新
    print(f"\n📊 5. 验证知识库信息更新")
    response = requests.get(f"{BASE_URL}/api/projects/{project_id}/knowledge_bases")
    result = response.json()
    
    if result.get('success') and result.get('knowledge_base'):
        kb_updated = result['knowledge_base']
        print(f"✅ 知识库信息已更新:")
        print(f"   📄 文档数量: {kb_updated['document_count']}")
        print(f"   🔄 更新时间: {kb_updated['updated_at']}")
        
        if kb_updated['document_count'] == len(test_documents):
            print("✅ 文档数量统计正确")
        else:
            print(f"⚠️ 文档数量不匹配: 期望 {len(test_documents)}, 实际 {kb_updated['document_count']}")
    
    # 6. 测试其他项目的知识库隔离
    print(f"\n🔒 6. 测试项目知识库隔离")
    if len(projects) > 1:
        other_project = projects[1]
        other_project_id = other_project['project_id']
        other_project_name = other_project['name']
        
        print(f"📋 测试项目: {other_project_name} (ID: {other_project_id})")
        
        response = requests.get(f"{BASE_URL}/api/projects/{other_project_id}/knowledge_bases")
        result = response.json()
        
        if result.get('success') and result.get('knowledge_base'):
            other_kb = result['knowledge_base']
            print(f"✅ 其他项目也有独立的知识库:")
            print(f"   📚 知识库名称: {other_kb['name']}")
            print(f"   📄 文档数量: {other_kb['document_count']}")
            
            if other_kb['kb_id'] != kb_id:
                print("✅ 知识库隔离正确")
            else:
                print("❌ 知识库隔离失败")
        else:
            print(f"❌ 获取其他项目知识库失败: {result.get('message')}")
    else:
        print("ℹ️ 只有一个项目，跳过隔离测试")
    
    print(f"\n🎉 测试完成！")
    return True

def test_knowledge_manager_page():
    """测试知识库管理页面"""
    print("\n" + "=" * 50)
    print("🌐 测试知识库管理页面")
    print("=" * 50)
    
    # 测试页面访问
    response = requests.get(f"{BASE_URL}/knowledge_manager.html")
    if response.status_code == 200:
        print("✅ 知识库管理页面可以访问")
        
        # 检查页面内容
        content = response.text
        if "知识库管理" in content and "项目选择器" in content:
            print("✅ 页面包含必要的元素")
        else:
            print("⚠️ 页面可能缺少某些元素")
    else:
        print(f"❌ 知识库管理页面无法访问 (状态码: {response.status_code})")

def main():
    """主函数"""
    print("🚀 开始测试新的知识库逻辑")
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    try:
        # 测试自动创建知识库功能
        success = test_auto_knowledge_base_creation()
        
        # 测试页面访问
        test_knowledge_manager_page()
        
        print("\n" + "=" * 50)
        print("🏁 测试完成")
        print("=" * 50)
        
        if success:
            print("✅ 新的知识库逻辑工作正常")
            print("💡 每个项目现在都有自己的默认知识库")
            print("🌐 可以通过 http://127.0.0.1:5005/knowledge_manager.html 访问管理界面")
        else:
            print("❌ 测试过程中发现问题")
        
        return success
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保应用程序正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
