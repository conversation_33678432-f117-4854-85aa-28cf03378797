# 贡献指南

感谢您对俄罗斯方块游戏项目的兴趣！我们欢迎各种形式的贡献，无论是代码、文档、设计还是问题反馈。

## 🌟 贡献方式

### 1. 🐛 报告 Bug
报告Bug可以帮助我们改进产品质量。请在提交问题前考虑以下几个问题：

- 您使用的是哪个浏览器和版本？
- 重复问题的具体步骤
- 您期望的结果是什么？
- 实际发生了什么？

**提交一个Bug报告包含：**
- 详细的问题描述
- 复现步骤
- 环境信息（操作系统、浏览器版本等）
- 截图或视频（如有）

### 2. 💡 提出新功能建议
我们欢迎您提出新功能的建议。请考虑：

- 功能是否符合项目的整体设计理念
- 是否有更好的解决方案
- 功能的复杂度和维护成本

**提交一个功能建议包含：**
- 功能详细描述
- 使用场景和用户需求
- 可能的实现方案
- 相关的风险和挑战

### 3. 💻 代码贡献
如果您是一名开发者，我们非常欢迎您直接贡献代码。

### 4. 📚 文档改进
完善文档是项目成功的重要因素。您可以：
- 修正文档中的错误
- 添加缺失的信息
- 改进文档的格式和可读性
- 翻译文档到其他语言

### 5. 🎨 用户界面改进
如果您擅长设计，可以帮忙：
- 改进游戏界面的用户体验
- 设计新的皮肤或主题
- 优化移动端的显示效果

### 6. 🧪 测试和反馈
- 为新版本提供测试反馈
- 帮助发现边缘案例
- 测试不同设备和浏览器的兼容性

## 🚀 开始贡献

### 第一步：Fork 项目

1. 访问项目的GitHub页面
2. 点击右上角的"Fork"按钮
3. 将Fork后的仓库克隆到本地

```bash
git clone https://github.com/your-username/tetris-game.git
cd tetris-game
```

### 第二步：设置开发环境

```bash
# 安装依赖（如果有）
npm install

# 启动开发服务器
npm start

# 打开浏览器访问
http://localhost:8000
```

### 第三步：创建功能分支

```bash
# 创建特性分支
git checkout -b feature/amazing-feature

# 或者创建修复分支
git checkout -b bugfix/fix-annoying-bug
```

### 第四步：进行开发

1. 确保您阅读并理解了项目代码规范
2. 进行必要的编码和测试
3. 确保您的代码不会破坏现有功能

### 第五步：提交更改

```bash
# 添加更改
git add .

# 提交更改
git commit -m "feat: 添加新功能"

# 推送到您的fork
git push origin feature/amazing-feature
```

### 第六步：创建Pull Request

1. 访问GitHub页面
2. 点击"Compare & pull request"
3. 填写详细的PR描述
4. 确保CI测试通过
5. 等待维护者审核

## 📋 代码规范

### 命名规范

```javascript
// ✅ 正确的命名示例
const BLOCK_SIZE = 30;           // 常量：大写下划线
let currentPlayer = null;         // 变量：驼峰命名
class TetrisGame { }             // 类：帕斯卡命名
function calculateScore() { }     // 函数：驼峰命名

// ❌ 错误的命名示例
const block_size = 30;           // 常量不应使用下划线
let current_player = null;       // 变量命名不规范
class tetrisGame { }             // 类命名不符合规范
function CalculateScore() { }     // 函数命名不符合规范
```

### 代码格式

```javascript
// ✅ 代码格式良好
class TetrisGame {
    constructor() {
        this.board = [];
        this.currentPiece = null;
        this.score = 0;
    }

    // 方法间有空行
    startGame() {
        this.board = this.createBoard();
        this.generateNewPiece();
    }

    // 方法遵循统一的缩进和格式
    createBoard() {
        const board = [];
        for (let y = 0; y < BOARD_HEIGHT; y++) {
            board[y] = [];
            for (let x = 0; x < BOARD_WIDTH; x++) {
                board[y][x] = 0;
            }
        }
        return board;
    }
}

// ❌ 代码格式混乱
class TetrisGame{
constructor(){
this.board=[];this.currentPiece=null;this.score=0;
}
startGame(){this.board=this.createBoard();this.generateNewPiece();}
createBoard(){const board=[];for(let y=0;y<BOARD_HEIGHT;y++){board[y]=[];for(let x=0;x<BOARD_WIDTH;x++){board[y][x]=0;}}return board;}}
```

### 注释规范

```javascript
// ✅ 良好的注释
class ScoreManager {
    /**
     * 计算消除行数的得分
     * @param {number} lines - 消除的行数
     * @param {number} level - 当前等级
     * @returns {number} 得分数
     */
    calculateScore(lines, level) {
        // 根据不同消除行数给予不同分数
        const scores = [0, 100, 300, 500, 800];
        return scores[lines] * level;
    }

    /**
     * 检查是否可以升级
     * @private
     */
    checkLevelUp() {
        const newLevel = Math.floor(this.totalLines / 10) + 1;
        if (newLevel > this.currentLevel && newLevel <= MAX_LEVEL) {
            this.currentLevel = newLevel;
            return true;
        }
        return false;
    }
}
```

## 🧪 测试要求

### 单元测试

```javascript
// ✅ 良好的单元测试示例
describe('TetrisGame', () => {
    let game;

    beforeEach(() => {
        game = new TetrisGame();
    });

    describe('generateNewPiece', () => {
        it('应该生成一个有效的方块', () => {
            const piece = game.generateNewPiece();
            expect(piece).toBeDefined();
            expect(piece.shape).toBeDefined();
            expect(piece.shape.length).toBeGreaterThan(0);
        });

        it('生成的方块应该在合理的位置', () => {
            const piece = game.generateNewPiece();
            expect(piece.x).toBeGreaterThanOrEqual(0);
            expect(piece.x).toBeLessThan(BOARD_WIDTH);
            expect(piece.y).toBe(0);
        });
    });

    describe('checkCollision', () => {
        it('应该检测到边界碰撞', () => {
            const piece = { x: -1, y: 0, shape: [[1]] };
            expect(game.checkCollision(piece)).toBe(true);
        });

        it('应该检测到方块碰撞', () => {
            game.board[5][5] = 1;
            const piece = { x: 5, y: 5, shape: [[1]] };
            expect(game.checkCollision(piece)).toBe(true);
        });
    });
});
```

### E2E测试

```javascript
// ✅ E2E测试示例
describe('Game Integration', () => {
    beforeEach(() => {
        // 重置游戏状态
        document.location.reload();
    });

    it('应该能够开始游戏', () => {
        // 查找开始按钮并点击
        const startButton = document.getElementById('startBtn');
        startButton.click();

        // 验证游戏状态改变
        expect(game.isPlaying).toBe(true);
        expect(document.getElementById('gameStatus').textContent).toBe('游戏中');
    });

    it('应该能够暂停游戏', () => {
        // 开始游戏
        document.getElementById('startBtn').click();

        // 暂停游戏
        const pauseButton = document.getElementById('pauseBtn');
        pauseButton.click();

        // 验证暂停状态
        expect(game.isPaused).toBe(true);
        expect(document.getElementById('gameStatus').textContent).toBe('暂停');
    });
});
```

## 📝 提交信息规范

### 提交消息格式

我们采用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```bash
<type>[optional scope]: <description>

[optional body]

[optional footer]
```

### 类型说明

| 类型 | 说明 | 示例 |
|------|------|------|
| `feat` | 新功能 | `feat: 添加双人模式` |
| `fix` | Bug修复 | `fix: 修复旋转碰撞检测错误` |
| `docs` | 文档修改 | `docs: 更新使用说明` |
| `style` | 代码格式修改 | `style: 格式化代码` |
| `refactor` | 重构 | `refactor: 重构状态管理` |
| `perf` | 性能优化 | `perf: 优化渲染性能` |
| `test` | 测试相关 | `test: 添加单元测试` |
| `chore` | 构建或工具变动 | `chore: 更新依赖包` |

### 提交消息示例

```bash
# 功能添加
git commit -m "feat: 添加音效系统"

# Bug修复
git commit -m "fix: 修复移动设备触摸延迟问题"

# 文档更新
git commit -m "docs: 修复快速入门指南中的错误链接"

# 性能优化
git commit -m "perf: 优化Canvas渲染性能，减少内存占用"

# 测试添加
git commit -m "test: 添加方块旋转功能的单元测试"

# 多行提交消息
git commit -m "feat: 添加排行榜功能

- 实现本地排行榜存储
- 显示前10名玩家成绩
- 支持分数排序和时间排序
"
```

## 🔍 Pull Request 检查清单

在提交PR之前，请确保完成以下检查：

### 代码质量
- [ ] 代码遵循项目的编码规范
- [ ] 代码没有明显的性能问题
- [ ] 错误处理完善，考虑了边界情况
- [ ] 添加了必要的注释和文档

### 测试覆盖
- [ ] 新功能包含相应的单元测试
- [ ] 修复包含回归测试
- [ ] 所有测试都能通过
- [ ] 测试覆盖率没有显著下降

### 文档更新
- [ ] 更新了相关的API文档
- [ ] 更新了用户使用说明（如有变化）
- [ ] 在README中添加了新功能的说明
- [ ] 修复了文档中的错误

### 用户体验
- [ ] 新功能符合用户期望
- [ ] 界面元素在移动设备上正常显示
- [ ] 操作流程直观易懂
- [ ] 没有引入新的可用性问题

### 兼容性
- [ ] 在目标浏览器中正常工作
- [ ] 在不同设备上测试通过
- [ ] 考虑了不同分辨率的情况
- [ ] 没有破坏现有的功能

### 安全性
- [ ] 没有明显的安全漏洞
- [ ] 输入数据经过了验证和处理
- [ ] 遵循了项目的安全最佳实践
- [ ] 没有暴露敏感信息

## 🤝 社区指南

### 行为准则

我们致力于创造一个友好、包容的开发社区。所有贡献者都需要遵循以下准则：

- 尊重所有贡献者
- 接受建设性的批评
- 关注技术本身，不进行人身攻击
- 欢迎初学者和经验丰富的开发者
- 保持专业和礼貌的态度

### 沟通渠道

- **GitHub Issues**: 用于报告bug和提出功能建议
- **GitHub Discussions**: 用于一般讨论和问答
- **Pull Reviews**: 用于代码审查和反馈

### 如何有效地参与讨论

1. **明确问题**: 清晰地描述您遇到的问题或建议
2. **提供上下文**: 提供足够的信息帮助他人理解
3. **保持耐心**: 开发者可能需要时间来响应
4. **保持友好**: 即使存在分歧，也要保持礼貌
5. **关注解决方案**: 集中精力寻找解决问题最好的方案

## 🎯 贡献指南 FAQ

### Q: 我是前端开发的新手，可以贡献吗？

A: 当然可以！我们非常欢迎新手开发者。您可以从以下方面开始：
- 修复一些简单的bug
- 改进文档和注释
- 添加简单的测试
- 优化用户界面

### Q: 我不是开发者，可以怎样参与项目？

A: 非开发者有多种方式参与：
- 报告bug和提出建议
- 帮助完善文档
- 提供UI/UX设计建议
- 参与社区讨论
- 测试新版本

### Q: 我的PR被拒绝了怎么办？

A: 这是正常的过程。代码审查是为了保证项目质量：
- 仔细审查reviewer的评论
- 按照建议进行修改
- 如果有疑问，可以礼貌地讨论
- 在大多数情况下，修正确认后会被接受

### Q: 如何找到适合自己贡献的任务？

A: 您可以通过以下方式找到合适的任务：
- 查看"good first issue"标签
- 关注项目的待办事项列表
- 在社区讨论中寻找需要帮助的地方
- 选择自己感兴趣和擅长的问题

### Q: 我可以为中文以外的语言做贡献吗？

A: 是的！国际化很重要：

```javascript
// ✅ 支持多语言的代码示例
const translations = {
    'zh-CN': {
        'game_title': '俄罗斯方块'
    },
    'en-US': {
        'game_title': 'Tetris'
    },
    'ja-JP': {
        'game_title': 'テトリス'
    }
};

function getTranslation(lang, key) {
    return translations[lang]?.[key] || translations['zh-CN'][key];
}
```

## 📊 贡献者认可

我们珍视每一位贡献者，无论您的大小贡献都会被记住。

### 贡献者徽章

我们将为不同类型的贡献颁发徽章：
- 🏆 **代码贡献者**: 提供高质量代码的开发者
- 📚 **文档专家**: 完善项目文档的贡献者
- 🎨 **设计大师**: 改进用户界面的设计师
- 🧪 **测试专家**: 提供全面测试的贡献者
- 💡 **创新思维**: 提出优秀功能建议的贡献者
- 🌟 **社区英雄**: 活跃参与社区讨论的贡献者

### 贡献者墙

我们将在项目的README中设立贡献者墙，展示所有活跃贡献者的名字。长期贡献者还将获得特殊的荣誉标识。

## 🎉 结语

感谢您花费时间阅读贡献指南！您的参与对项目的成功至关重要。无论您是提交一个bug报告、一个功能建议，还是直接的代码贡献，我们都非常感谢您的支持。

让我们一起创造一个更棒的俄罗斯方块游戏！🚀

**如果您有任何问题，随时通过以下方式联系我们：**
- GitHub Issues
- 项目讨论区
- 邮件支持

---

## 📄 许可证

通过贡献本项目的代码，您同意将您的贡献在项目许可证下发布。

**许可证类型**: MIT License
**许可证文件**: [LICENSE](LICENSE)
**许可证链接**: https://github.com/your-username/tetris-game/blob/main/LICENSE

---

*最后更新: 2024年*
*维护者: 俄罗斯方块游戏开发团队*