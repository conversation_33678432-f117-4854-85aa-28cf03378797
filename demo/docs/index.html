<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俄罗斯方块游戏 - 文档中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            padding: 40px 0;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .docs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .doc-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .doc-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .doc-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .doc-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
        }

        .doc-title {
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .doc-description {
            color: #666;
            margin-bottom: 15px;
            min-height: 60px;
        }

        .doc-audience {
            display: inline-block;
            background: #f8f9fa;
            color: #495057;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            margin-bottom: 15px;
            border: 1px solid #dee2e6;
        }

        .doc-content {
            margin-bottom: 15px;
            color: #555;
        }

        .doc-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .doc-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .features {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .features h2 {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
            font-size: 2em;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .feature-item {
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .feature-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .feature-description {
            color: #666;
            font-size: 0.9em;
        }

        .footer {
            text-align: center;
            color: white;
            padding: 30px 0;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }

            .docs-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .doc-card {
                padding: 20px;
            }

            .features-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 俄罗斯方块游戏</h1>
            <p>完整的文档中心 - 用户指南、开发文档、贡献指南</p>
        </div>

        <div class="docs-grid">
            <div class="doc-card">
                <div class="doc-icon">📖</div>
                <h3 class="doc-title">用户使用手册</h3>
                <div class="doc-audience">👥 所有用户</div>
                <p class="doc-description">最完整的用户指南，包含游戏介绍、安装运行、操作说明和常见问题解答。</p>
                <div class="doc-content">
                    <strong>包含内容：</strong> 游戏介绍、界面指南、操作说明、游戏规则、功能特性、常见问题、移动设备优化
                </div>
                <a href="user_manual.md" class="doc-link">查看详细文档 →</a>
            </div>

            <div class="doc-card">
                <div class="doc-icon">🚀</div>
                <h3 class="doc-title">快速入门指南</h3>
                <div class="doc-audience">🆕 新用户</div>
                <p class="doc-description">5分钟快速上手指南，让您立即开始享受游戏乐趣。</p>
                <div class="doc-content">
                    <strong>包含内容：</strong> 快速开始、基础操作、游戏目标、新手技巧、问题排查、移动端优化
                </div>
                <a href="quick_start.md" class="doc-link">立即开始 →</a>
            </div>

            <div class="doc-card">
                <div class="doc-icon">🔧</div>
                <h3 class="doc-title">开发者指南</h3>
                <div class="doc-audience">👨‍💻 开发者</div>
                <p class="doc-description">全面的技术参考，帮助您理解架构、修改代码和扩展功能。</p>
                <div class="doc-content">
                    <strong>包含内容：</strong> 项目架构、核心类详解、游戏逻辑、事件处理、界面样式、性能优化、扩展开发
                </div>
                <a href="developer_guide.md" class="doc-link">查看技术文档 →</a>
            </div>

            <div class="doc-card">
                <div class="doc-icon">🤝</div>
                <h3 class="doc-title">贡献指南</h3>
                <div class="doc-audience">🤝 贡献者</div>
                <p class="doc-description">项目参与指南，包含代码规范、提交流程和社区协作规范。</p>
                <div class="doc-content">
                    <strong>包含内容：</strong> 贡献方式、代码规范、测试要求、提交信息、PR检查清单、社区指南
                </div>
                <a href="CONTRIBUTING.md" class="doc-link">参与贡献 →</a>
            </div>

            <div class="doc-card">
                <div class="doc-icon">📋</div>
                <h3 class="doc-title">更新日志</h3>
                <div class="doc-audience">🔄 所有用户</div>
                <p class="doc-description">版本变更记录，跟踪功能更新、问题修复和版本规划。</p>
                <div class="doc-content">
                    <strong>包含内容：</strong> 版本历史、功能更新、问题修复、版本规划、贡献记录、致谢
                </div>
                <a href="CHANGELOG.md" class="doc-link">查看更新 →</a>
            </div>

            <div class="doc-card">
                <div class="doc-icon">📚</div>
                <h3 class="doc-title">文档索引</h3>
                <div class="doc-audience">📖 阅读者</div>
                <p class="doc-description">文档中心索引，提供完整的文档导航和使用建议。</p>
                <div class="doc-content">
                    <strong>包含内容：</strong> 文档索引、使用建议、导航路径、获取帮助、兼容性说明
                </div>
                <a href="README.md" class="doc-link">文档导航 →</a>
            </div>
        </div>

        <div class="features">
            <h2>🎯 文档特色</h2>
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">📖</div>
                    <div class="feature-title">完整全面</div>
                    <div class="feature-description">从入门到精通的全套文档体系</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🎯</div>
                    <div class="feature-title">精准定位</div>
                    <div class="feature-description">针对不同用户的定制化内容</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔧</div>
                    <div class="feature-title">技术深入</div>
                    <div class="feature-description">详细的架构分析和实现原理</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🚀</div>
                    <div class="feature-title">快速上手</div>
                    <div class="feature-description">快速启动和操作的清晰指南</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🤝</div>
                    <div class="feature-title">协作友好</div>
                    <div class="feature-description">完善的贡献参与机制</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📱</div>
                    <div class="feature-title">移动优化</div>
                    <div class="feature-description">支持多设备和浏览器的响应式设计</div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>🎉 感谢使用俄罗斯方块游戏！</p>
            <p>如有问题或建议，欢迎通过项目Issue或讨论区与我们联系</p>
        </div>
    </div>
</body>
</html>