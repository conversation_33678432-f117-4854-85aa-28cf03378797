# 俄罗斯方块游戏开发指南

## 📋 项目概述

本项目是一个基于HTML5 Canvas的俄罗斯方块游戏，采用模块化设计，代码结构清晰，易于扩展和维护。

### 核心特性
- 🎮 完整的俄罗斯方块游戏逻辑
- ⚡ 60FPS流畅渲染
- 📱 响应式设计，多设备适配
- 🎯 精确的碰撞检测和物理模拟
- 💾 本地存储数据持久化

### 技术栈
- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **渲染**: Canvas 2D API
- **存储**: localStorage
- **兼容性**: 支持所有现代浏览器

## 📁 项目结构

```
tetris-game/
├── index.html              # 主页面入口
├── styles.css             # 游戏样式文件
├── game.js                # 核心游戏逻辑
├── CLAUDE.md              # 项目说明文档
├── docs/                  # 文档目录
│   ├── user_manual.md           # 用户使用手册
│   ├── quick_start.md          # 快速入门指南
│   └── developer_guide.md     # 开发者指南
└── assets/                # 资源文件目录
    └── images/             # 图片资源
```

## 🏗️ 核心架构

### 1. 游戏状态管理 (Game State Management)

```javascript
// 游戏状态对象结构
const gameState = {
    board: [],              // 10x20游戏棋盘数组
    currentPiece: null,     // 当前活动的方块
    nextPiece: null,        // 下一个方块
    score: 0,               // 当前分数
    level: 1,               // 当前等级
    lines: 0,               // 已消除行数
    isPlaying: false,       // 游戏是否进行中
    isPaused: false,        // 游戏是否暂停
    gameOver: false,        // 游戏是否结束
    dropTime: 0,            // 下落计时器
    dropInterval: 1000,     // 下落间隔(毫秒)
    highScore: 0,           // 历史最高分
    startTime: 0,           // 游戏开始时间
    elapsedTime: 0          // 游戏时长
};
```

### 2. 方块系统 (Piece System)

```javascript
// 方块形状定义 (7种经典俄罗斯方块)
const PIECES = [
    // I型 - 4个方块直线排列
    [
        [1, 1, 1, 1]
    ],
    // O型 - 2x2正方形
    [
        [1, 1],
        [1, 1]
    ],
    // T型 - T形结构
    [
        [0, 1, 0],
        [1, 1, 1]
    ],
    // S型 - S形螺旋
    [
        [0, 1, 1],
        [1, 1, 0]
    ],
    // Z型 - Z形螺旋
    [
        [1, 1, 0],
        [0, 1, 1]
    ],
    // J型 - J形结构
    [
        [1, 0, 0],
        [1, 1, 1]
    ],
    // L型 - L形结构
    [
        [0, 0, 1],
        [1, 1, 1]
    ]
];

// 方块颜色配置
const PIECE_COLORS = [
    '#00f0f0', // I型 - 青色
    '#f0f000', // O型 - 黄色
    '#a000f0', // T型 - 紫色
    '#00f000', // S型 - 绿色
    '#f00000', // Z型 - 红色
    '#0000f0', // J型 - 蓝色
    '#f0a000'  // L型 - 橙色
];
```

## 🔧 核心类详解

### 1. 主游戏类 (TetrisGame)

```javascript
class TetrisGame {
    constructor() {
        this.canvas = document.getElementById('gameBoard');
        this.ctx = this.canvas.getContext('2d');
        this.nextCanvas = document.getElementById('nextPieceCanvas');
        this.nextCtx = this.nextCanvas.getContext('2d');

        this.boardArray = this.createBoard();
        this.currentPiece = null;
        this.nextPiece = null;
        this.gameLoop = null;
        this.dropTimer = 0;

        this.initializeGame();
    }

    // 创建游戏棋盘
    createBoard() {
        const board = [];
        for (let y = 0; y < BOARD_HEIGHT; y++) {
            board[y] = [];
            for (let x = 0; x < BOARD_WIDTH; x++) {
                board[y][x] = 0;
            }
        }
        return board;
    }

    // 初始化游戏
    initializeGame() {
        this.generateNewPiece();
        this.generateNextPiece();
        this.bindEvents();
        this.updateUI();
    }

    // 开始游戏
    start() {
        this.isPlaying = true;
        this.gameLoop = requestAnimationFrame(this.update.bind(this));
    }

    // 暂停游戏
    pause() {
        this.isPaused = !this.isPaused;
        if (!this.isPaused) {
            this.gameLoop = requestAnimationFrame(this.update.bind(this));
        }
    }

    // 重置游戏
    reset() {
        if (this.gameLoop) {
            cancelAnimationFrame(this.gameLoop);
        }

        this.boardArray = this.createBoard();
        this.currentPiece = null;
        this.nextPiece = null;
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.isPlaying = false;
        this.isPaused = false;
        this.gameOver = false;
        this.dropTimer = 0;

        this.generateNewPiece();
        this.generateNextPiece();
        this.updateUI();
        this.render();
    }
}
```

### 2. 方块管理类 (PieceManager)

```javascript
class PieceManager {
    constructor() {
        this.currentPiece = null;
        this.nextPiece = null;
    }

    // 生成新方块
    generatePiece() {
        const typeId = Math.floor(Math.random() * PIECES.length);
        return {
            type: typeId,
            shape: PIECES[typeId],
            color: PIECE_COLORS[typeId],
            x: Math.floor(BOARD_WIDTH / 2) - Math.floor(PIECES[typeId][0].length / 2),
            y: 0
        };
    }

    // 旋转方块
    rotatePiece(piece, direction = 1) {
        const shape = piece.shape;
        const rows = shape.length;
        const cols = shape[0].length;
        const rotated = [];

        // 创建旋转后的数组
        for (let i = 0; i < cols; i++) {
            rotated[i] = [];
            for (let j = 0; j < rows; j++) {
                if (direction === 1) {
                    // 顺时针旋转
                    rotated[i][j] = shape[rows - 1 - j][i];
                } else {
                    // 逆时针旋转
                    rotated[i][j] = shape[j][cols - 1 - i];
                }
            }
        }

        return {
            ...piece,
            shape: rotated
        };
    }

    // 检查碰撞
    checkCollision(piece, board, x = piece.x, y = piece.y) {
        const shape = piece.shape;

        for (let row = 0; row < shape.length; row++) {
            for (let col = 0; col < shape[row].length; col++) {
                if (shape[row][col]) {
                    const boardX = x + col;
                    const boardY = y + row;

                    // 检查边界
                    if (boardX < 0 || boardX >= BOARD_WIDTH ||
                        boardY >= BOARD_HEIGHT) {
                        return true;
                    }

                    // 检查底部碰撞
                    if (boardY >= 0 && board[boardY][boardX]) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    // 固定方块到棋盘
    lockPiece(piece, board) {
        const shape = piece.shape;

        for (let row = 0; row < shape.length; row++) {
            for (let col = 0; col < shape[row].length; col++) {
                if (shape[row][col]) {
                    const boardY = piece.y + row;
                    const boardX = piece.x + col;

                    if (boardY >= 0) {
                        board[boardY][boardX] = piece.type + 1; // +1 用于区分0（空格）
                    }
                }
            }
        }
    }
}
```

### 3. 渲染引擎类 (Renderer)

```javascript
class Renderer {
    constructor(canvas, ctx) {
        this.canvas = canvas;
        this.ctx = ctx;
        this.blockSize = BLOCK_SIZE;
    }

    // 渲染游戏棋盘
    renderBoard(board) {
        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制网格背景
        this.drawGrid();

        // 绘制已固定的方块
        for (let y = 0; y < board.length; y++) {
            for (let x = 0; x < board[y].length; x++) {
                if (board[y][x]) {
                    this.drawBlock(x, y, board[y][x] - 1);
                }
            }
        }
    }

    // 绘制单个方块
    drawBlock(x, y, pieceType) {
        const color = PIECE_COLORS[pieceType];
        const pixelX = x * this.blockSize;
        const pixelY = y * this.blockSize;

        // 绘制方块主体
        this.ctx.fillStyle = color;
        this.ctx.fillRect(pixelX, pixelY, this.blockSize, this.blockSize);

        // 绘制方块边框
        this.ctx.strokeStyle = '#333';
        this.ctx.lineWidth = 1;
        this.ctx.strokeRect(pixelX, pixelY, this.blockSize, this.blockSize);

        // 绘制高光效果
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        this.ctx.fillRect(pixelX, pixelY, this.blockSize - 2, 2);
        this.ctx.fillRect(pixelX, pixelY, 2, this.blockSize - 2);

        // 绘制阴影效果
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        this.ctx.fillRect(pixelX, pixelY + this.blockSize - 2, this.blockSize, 2);
        this.ctx.fillRect(pixelX + this.blockSize - 2, pixelY, 2, this.blockSize);
    }

    // 绘制网格
    drawGrid() {
        this.ctx.strokeStyle = '#666';
        this.ctx.lineWidth = 0.5;

        // 绘制垂直线
        for (let x = 0; x <= BOARD_WIDTH; x++) {
            this.ctx.beginPath();
            this.ctx.moveTo(x * this.blockSize, 0);
            this.ctx.lineTo(x * this.blockSize, BOARD_HEIGHT * this.blockSize);
            this.ctx.stroke();
        }

        // 绘制水平线
        for (let y = 0; y <= BOARD_HEIGHT; y++) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y * this.blockSize);
            this.ctx.lineTo(BOARD_WIDTH * this.blockSize, y * this.blockSize);
            this.ctx.stroke();
        }
    }

    // 渲染当前方块
    renderPiece(piece) {
        const shape = piece.shape;

        for (let row = 0; row < shape.length; row++) {
            for (let col = 0; col < shape[row].length; col++) {
                if (shape[row][col]) {
                    this.drawBlock(
                        piece.x + col,
                        piece.y + row,
                        piece.type
                    );
                }
            }
        }
    }

    // 渲染下一个方块预览
    renderNextPiece(piece) {
        const ctx = this.nextCtx;
        const canvas = this.nextCanvas;

        ctx.clearRect(0, 0, canvas.width, canvas.height);

        const blockSize = NEXT_PIECE_SIZE;
        const shape = piece.shape;

        // 计算居中位置
        const offsetX = (canvas.width - shape[0].length * blockSize) / 2;
        const offsetY = (canvas.height - shape.length * blockSize) / 2;

        for (let row = 0; row < shape.length; row++) {
            for (let col = 0; col < shape[row].length; col++) {
                if (shape[row][col]) {
                    const x = offsetX + col * blockSize;
                    const y = offsetY + row * blockSize;

                    ctx.fillStyle = piece.color;
                    ctx.fillRect(x, y, blockSize, blockSize);

                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 1;
                    ctx.strokeRect(x, y, blockSize, blockSize);
                }
            }
        }
    }
}
```

### 4. 分数管理类 (ScoreManager)

```javascript
class ScoreManager {
    constructor() {
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.highScore = this.loadHighScore();
    }

    // 消除行并计算分数
    clearLines(lines, board) {
        const clearedLines = [];

        // 检查并消除完整行
        for (let y = board.length - 1; y >= 0; y--) {
            if (board[y].every(cell => cell !== 0)) {
                clearedLines.push(y);
                board.splice(y, 1);
                board.unshift(new Array(BOARD_WIDTH).fill(0));
                y++; // 重新检查当前位置
            }
        }

        // 计算分数
        if (clearedLines.length > 0) {
            this.lines += clearedLines.length;
            const baseScore = this.getBaseScore(clearedLines.length);
            const levelBonus = this.level;
            this.score += baseScore * levelBonus;

            // 检查升级
            this.checkLevelUp();

            // 更新最高分
            this.updateHighScore();
        }

        return clearedLines.length;
    }

    // 获取基础分数
    getBaseScore(lines) {
        const scores = [0, 100, 300, 500, 800];
        return scores[lines] || 0;
    }

    // 检查是否升级
    checkLevelUp() {
        const newLevel = Math.floor(this.lines / 10) + 1;
        if (newLevel !== this.level) {
            this.level = Math.min(newLevel, 10); // 最高10级
            return true;
        }
        return false;
    }

    // 更新最高分
    updateHighScore() {
        if (this.score > this.highScore) {
            this.highScore = this.score;
            this.saveHighScore();
        }
    }

    // 加载历史最高分
    loadHighScore() {
        try {
            return parseInt(localStorage.getItem('tetrisHighScore') || '0');
        } catch {
            return 0;
        }
    }

    // 保存最高分
    saveHighScore() {
        try {
            localStorage.setItem('tetrisHighScore', this.highScore.toString());
        } catch (e) {
            console.warn('Failed to save high score:', e);
        }
    }

    // 重置分数
    reset() {
        this.score = 0;
        this.level = 1;
        this.lines = 0;
    }
}
```

## 🎮 游戏逻辑实现

### 1. 主游戏循环

```javascript
class TetrisGame {
    constructor() {
        // ... 其他初始化代码 ...
        this.lastTime = 0;
        this.dropTimer = 0;
        this.dropInterval = 1000;
    }

    update(currentTime) {
        if (!this.isPlaying || this.isPaused) {
            this.gameLoop = requestAnimationFrame(this.update.bind(this));
            return;
        }

        const deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        this.dropTimer += deltaTime;

        // 方块下落逻辑
        if (this.dropTimer >= this.dropInterval) {
            this.movePieceDown();
            this.dropTimer = 0;
        }

        this.render();
        this.updateUI();

        this.gameLoop = requestAnimationFrame(this.update.bind(this));
    }
}
```

### 2. 方块移动逻辑

```javascript
class TetrisGame {
    constructor() {
        this.pieceManager = new PieceManager();
        // ...
    }

    movePieceLeft() {
        if (this.currentPiece && !this.pieceManager.checkCollision(
            this.currentPiece,
            this.boardArray,
            this.currentPiece.x - 1,
            this.currentPiece.y
        )) {
            this.currentPiece.x--;
            this.render();
            return true;
        }
        return false;
    }

    movePieceRight() {
        if (this.currentPiece && !this.pieceManager.checkCollision(
            this.currentPiece,
            this.boardArray,
            this.currentPiece.x + 1,
            this.currentPiece.y
        )) {
            this.currentPiece.x++;
            this.render();
            return true;
        }
        return false;
    }

    movePieceDown() {
        if (this.currentPiece && !this.pieceManager.checkCollision(
            this.currentPiece,
            this.boardArray,
            this.currentPiece.x,
            this.currentPiece.y + 1
        )) {
            this.currentPiece.y++;
            this.render();
        } else {
            // 方块无法继续下落，固定到棋盘
            this.lockPiece();
        }
    }

    rotatePiece() {
        if (!this.currentPiece) return;

        const rotatedPiece = this.pieceManager.rotatePiece(this.currentPiece);

        // 检查旋转后是否碰撞
        if (!this.pieceManager.checkCollision(rotatedPiece, this.boardArray)) {
            this.currentPiece = rotatedPiece;
            this.render();
        }
    }

    hardDrop() {
        if (!this.currentPiece) return;

        // 快速下落到底部
        while (this.movePieceDown()) {
            this.scoreManager.score += 2; // 硬降额外加分
        }
        this.render();
    }
}
```

### 3. 行消除逻辑

```javascript
class TetrisGame {
    constructor() {
        this.scoreManager = new ScoreManager();
        // ...
    }

    lockPiece() {
        // 固定方块到棋盘
        this.pieceManager.lockPiece(this.currentPiece, this.boardArray);

        // 检查并消除完整行
        const clearedLines = this.scoreManager.clearLines([], this.boardArray);

        if (clearedLines > 0) {
            // 行消除特效
            this.playClearEffect(clearedLines);
        }

        // 生成新方块
        this.generateNewPiece();

        // 检查游戏是否结束
        if (this.checkGameOver()) {
            this.endGame();
        }
    }

    checkGameOver() {
        // 检查是否新方块一出现就碰撞
        return this.currentPiece &&
               this.pieceManager.checkCollision(this.currentPiece, this.boardArray);
    }

    generateNewPiece() {
        this.currentPiece = this.nextPiece || this.pieceManager.generatePiece();
        this.nextPiece = this.pieceManager.generatePiece();

        this.nextRenderer.renderNextPiece(this.nextPiece);
    }
}
```

## 🎯 事件处理系统

### 1. 键盘事件处理

```javascript
class TetrisGame {
    constructor() {
        // ...
        this.bindEvents();
    }

    bindEvents() {
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (!this.isPlaying && e.key !== 's' && e.key !== 'S') return;

            switch (e.key) {
                case 'ArrowLeft':
                    e.preventDefault();
                    this.movePieceLeft();
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.movePieceRight();
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    this.movePieceDown();
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    this.rotatePiece();
                    break;
                case ' ':
                    e.preventDefault();
                    this.hardDrop();
                    break;
                case 'p':
                case 'P':
                    e.preventDefault();
                    this.pause();
                    break;
                case 'r':
                case 'R':
                    e.preventDefault();
                    this.reset();
                    break;
                case 's':
                case 'S':
                    e.preventDefault();
                    this.start();
                    break;
            }
        });
    }
}
```

### 2. 触屏事件处理

```javascript
class TouchController {
    constructor(game) {
        this.game = game;
        this.touchStartX = 0;
        this.touchStartY = 0;
        this.threshold = 50; // 滑动阈值

        this.bindTouchEvents();
    }

    bindTouchEvents() {
        const canvas = this.game.canvas;

        // 触摸开始
        canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.touchStartX = e.touches[0].clientX;
            this.touchStartY = e.touches[0].clientY;
        });

        // 触摸结束
        canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            if (!this.touchStartX || !this.touchStartY) return;

            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;

            const deltaX = touchEndX - this.touchStartX;
            const deltaY = touchEndY - this.touchStartY;

            // 判断滑动方向
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                // 水平滑动
                if (deltaX > this.threshold) {
                    this.game.movePieceRight();
                } else if (deltaX < -this.threshold) {
                    this.game.movePieceLeft();
                }
            } else {
                // 垂直滑动
                if (deltaY > this.threshold) {
                    this.game.movePieceDown();
                } else if (deltaY < -this.threshold) {
                    this.game.rotatePiece();
                }
            }

            this.touchStartX = 0;
            this.touchStartY = 0;
        });

        // 双击事件（直接落下）
        canvas.addEventListener('dblclick', (e) => {
            e.preventDefault();
            this.game.hardDrop();
        });
    }
}
```

## 🎨 界面与样式

### 1. 响应式CSS布局

```css
/* 基础布局 */
.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Arial', sans-serif;
}

.game-main {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.game-board-section {
    flex: 1;
    min-width: 300px;
}

.game-sidebar {
    flex: 0 0 280px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 适应小屏幕 */
@media (max-width: 768px) {
    .game-main {
        flex-direction: column;
    }

    .game-sidebar {
        flex: none;
        width: 100%;
    }

    .game-board-wrapper {
        transform: scale(0.8);
        transform-origin: top left;
    }
}

/* 游戏按钮样式 */
.control-btn {
    padding: 12px 24px;
    font-size: 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.control-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.primary-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.secondary-btn {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}
```

## 🚀 部署与优化

### 1. 性能优化

```javascript
// 对象池优化
class ObjectPool {
    constructor(createFn, resetFn) {
        this.pool = [];
        this.createFn = createFn;
        this.resetFn = resetFn;
    }

    acquire() {
        if (this.pool.length > 0) {
            return this.pool.pop();
        }
        return this.createFn();
    }

    release(obj) {
        this.resetFn(obj);
        this.pool.push(obj);
    }
}

// 使用示例
const particlePool = new ObjectPool(
    () => ({ x: 0, y: 0, vx: 0, vy: 0, life: 0 }),
    (particle) => {
        particle.x = particle.y = 0;
        particle.vx = particle.vy = 0;
        particle.life = 0;
    }
);

// 渲染优化
class OptimizedRenderer {
    constructor(canvas, ctx) {
        this.canvas = canvas;
        this.ctx = ctx;
        this.offscreenCanvas = document.createElement('canvas');
        this.offscreenCtx = this.offscreenCanvas.getContext('2d');

        // 设置离屏画布尺寸
        this.offscreenCanvas.width = canvas.width;
        this.offscreenCanvas.height = canvas.height;
    }

    render() {
        // 在离屏画布上绘制
        this.drawToOffscreen();

        // 一次性复制到主画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.ctx.drawImage(
            this.offscreenCanvas,
            0, 0,
            this.canvas.width,
            this.canvas.height
        );
    }
}
```

### 2. 性能监控

```javascript
class PerformanceMonitor {
    constructor() {
        this.fps = 0;
        this.fpsHistory = [];
        this.lastTime = performance.now();
        this.frameCount = 0;

        // 开始监控
        this.start();
    }

    start() {
        this.monitorLoop();
    }

    monitorLoop() {
        const currentTime = performance.now();
        const deltaTime = currentTime - this.lastTime;

        this.frameCount++;

        // 每秒计算一次FPS
        if (this.frameCount % 60 === 0) {
            this.fps = Math.round(1000 / deltaTime);
            this.fpsHistory.push(this.fps);

            // 保持最近60秒的FPS记录
            if (this.fpsHistory.length > 60) {
                this.fpsHistory.shift();
            }

            console.log(`Current FPS: ${this.fps}`);
        }

        this.lastTime = currentTime;

        requestAnimationFrame(() => this.monitorLoop());
    }

    getAverageFPS() {
        if (this.fpsHistory.length === 0) return 0;
        const sum = this.fpsHistory.reduce((a, b) => a + b, 0);
        return Math.round(sum / this.fpsHistory.length);
    }

    isPerformanceGood() {
        return this.fps >= 55; // 55 FPS 以上视为良好性能
    }
}
```

## 🔧 代码规范与最佳实践

### 1. 编码规范

```javascript
// 命名规范
const BLOCK_SIZE = 30;           // 常量使用大写和下划线
let currentPiece = null;         // 变量使用驼峰命名
class PieceManager { }           // 类名使用帕斯卡命名
function calculateScore() { }     // 函数使用驼峰命名

// 代码组织
class TetrisGame {
    // 私有属性和方法
    #boardArray;  // 私有属性使用#前缀

    // 公共属性和方法
    scoreManager;

    constructor() {
        // 初始化代码
    }

    // 生命周期方法
    start() { }
    update() { }
    render() { }

    // 事件处理
    handleKeyPress() { }

    // 游戏逻辑
    movePiece() { }
    checkCollision() { }

    // 工具方法
    static createBoard() { }  // 静态方法
}

// 错误处理
try {
    localStorage.data = JSON.stringify(gameState);
} catch (error) {
    console.warn('Failed to save game state:', error);
    // 降级处理
    this.saveToCookies();
}

// 日志记录
const Logger = {
    log: (message, data = null) => {
        console.log(`[LOG] ${new Date().toISOString()}: ${message}`, data);
    },

    warn: (message, data = null) => {
        console.warn(`[WARN] ${new Date().toISOString()}: ${message}`, data);
    },

    error: (message, error = null) => {
        console.error(`[ERROR] ${new Date().toISOString()}: ${message}`, error);
    }
};
```

### 2. 测试策略

```javascript
// 单元测试示例
class TetrisGameTest {
    constructor() {
        this.game = new TetrisGame();
    }

    testPieceGeneration() {
        // 测试方块生成
        this.game.generateNewPiece();
        assert(this.game.currentPiece !== null);
        assert(this.game.nextPiece !== null);
        assert(Array.isArray(this.game.currentPiece.shape));
    }

    testCollisionDetection() {
        // 测试碰撞检测
        const testPiece = {
            x: 0, y: 0,
            shape: [[1]]
        };

        const testBoard = [
            [0, 0, 0],
            [0, 1, 0], // 在(1,1)位置有方块
            [0, 0, 0]
        ];

        // 测试不碰撞
        assert(!this.game.pieceManager.checkCollision(testPiece, testBoard, 0, 0));

        // 测试碰撞
        assert(this.game.pieceManager.checkCollision(testPiece, testBoard, 1, 1));
    }

    testLineClearing() {
        // 测试消行逻辑
        const testBoard = [
            [0, 0, 0],
            [1, 1, 1], // 完整行
            [0, 0, 0]
        ];

        const clearedCount = this.game.scoreManager.clearLines([], testBoard);
        assert(clearedCount === 1);
        assert(testBoard[1].every(cell => cell === 0));
        assert(testBoard[0].every(cell => cell === 1)); // 上方行下移
    }
}

// 集成测试
class IntegrationTest {
    async testFullGameFlow() {
        const game = new TetrisGame();

        // 开始游戏
        game.start();
        assert(game.isPlaying === true);

        // 执行一些操作
        game.movePieceLeft();
        game.movePieceRight();
        game.rotatePiece();

        // 检查游戏状态
        assert(game.boardArray !== null);
        assert(game.score >= 0);
        assert(game.level >= 1);

        // 结束游戏
        game.reset();
        assert(game.isPlaying === false);
        assert(game.score === 0);
        assert(game.level === 1);
    }
}
```

## 🎨 模块化扩展开发

### 1. 添加自定义方块

```javascript
// 扩展方块类型
class CustomPieceManager extends PieceManager {
    constructor() {
        super();
        this.addCustomPieces();
    }

    addCustomPieces() {
        // 添加自定义方块的形状和颜色
        this.customPieces = [
            // 5型方块 (五边形)
            [
                [0, 1, 1, 1],
                [1, 1, 0, 0],
                [0, 0, 0, 0]
            ],
            // 其他自定义方块...
        ];

        this.customColors = [
            '#ff6b6b', // 5型 - 粉红色
            '#4ecdc4', // 其他自定义颜色...
        ];
    }

    generatePiece() {
        // 可以选择包含自定义方块的随机生成
        const useCustomPiece = Math.random() < 0.3; // 30%概率生成自定义方块

        if (useCustomPiece && this.customPieces.length > 0) {
            const typeId = Math.floor(Math.random() * this.customPieces.length);
            return {
                type: typeId + 7, // 避免与原始方块类型冲突
                shape: this.customPieces[typeId],
                color: this.customColors[typeId],
                x: Math.floor(BOARD_WIDTH / 2) - Math.floor(this.customPieces[typeId][0].length / 2),
                y: 0
            };
        }

        // 否则使用原始方块
        return super.generatePiece();
    }
}
```

### 2. 添加音效系统

```javascript
class SoundSystem {
    constructor() {
        this.enabled = true;
        this.volume = 0.7;
        this.sounds = {};
        this.initSounds();
    }

    initSounds() {
        // 音效加载
        this.sounds = {
            move: this.loadSound('move.mp3'),
            rotate: this.loadSound('rotate.mp3'),
            drop: this.loadSound('drop.mp3'),
            clear1: this.loadSound('clear1.mp3'),
            clear2: this.loadSound('clear2.mp3'),
            clear3: this.loadSound('clear3.mp3'),
            clear4: this.loadSound('clear4.mp3'),
            gameOver: this.loadSound('gameover.mp3'),
            levelUp: this.loadSound('levelup.mp3'),
            background: this.loadSound('background.mp3', true)
        };
    }

    loadSound(filename, loop = false) {
        try {
            const audio = new Audio();
            audio.src = `assets/sounds/${filename}`;
            audio.loop = loop;
            audio.volume = this.volume;
            return audio;
        } catch (error) {
            console.warn(`Failed to load sound: ${filename}`, error);
            return null;
        }
    }

    play(soundName) {
        if (!this.enabled || !this.sounds[soundName]) return;

        try {
            // 重置音频播放位置
            this.sounds[soundName].currentTime = 0;
            this.sounds[soundName].play().catch(e => {
                console.warn(`Failed to play sound ${soundName}:`, e);
            });
        } catch (error) {
            console.warn(`Error playing sound ${soundName}:`, error);
        }
    }

    stop(soundName) {
        if (this.sounds[soundName]) {
            this.sounds[soundName].pause();
            this.sounds[soundName].currentTime = 0;
        }
    }

    setEnabled(enabled) {
        this.enabled = enabled;
        if (!enabled) {
            this.stopAll();
        }
    }

    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        Object.values(this.sounds).forEach(sound => {
            if (sound) sound.volume = this.volume;
        });
    }

    stopAll() {
        Object.values(this.sounds).forEach(sound => {
            if (sound) sound.pause();
        });
    }

    // 背景音乐控制
    startBackgroundMusic() {
        this.play('background');
    }

    stopBackgroundMusic() {
        this.stop('background');
    }
}
```

### 3. 添加粒子特效系统

```javascript
class ParticleSystem {
    constructor(canvas, ctx) {
        this.canvas = canvas;
        this.ctx = ctx;
        this.particles = [];
        this.maxParticles = 100;
    }

    createParticles(x, y, color, count = 20) {
        for (let i = 0; i < count && this.particles.length < this.maxParticles; i++) {
            this.particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 8,
                vy: (Math.random() - 0.5) * 8,
                life: 1.0,
                decay: Math.random() * 0.02 + 0.01,
                color: color,
                size: Math.random() * 4 + 2
            });
        }
    }

    update() {
        // 更新粒子位置
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];

            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.vy += 0.2; // 重力
            particle.life -= particle.decay;

            // 移除死亡的粒子
            if (particle.life <= 0) {
                this.particles.splice(i, 1);
            }
        }
    }

    render() {
        this.particles.forEach(particle => {
            this.ctx.save();
            this.ctx.globalAlpha = particle.life;
            this.ctx.fillStyle = particle.color;
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();
        });
    }

    clear() {
        this.particles = [];
    }

    // 消行特效
    createLineClearEffect(x, y, width) {
        const color = '#ffff00';
        for (let i = 0; i < width; i++) {
            this.createParticles(
                x * BLOCK_SIZE + BLOCK_SIZE / 2 + i * BLOCK_SIZE,
                y * BLOCK_SIZE + BLOCK_SIZE / 2,
                color,
                5
            );
        }
    }

    // 下落特效
    createDropEffect(x, y) {
        const color = '#00ffff';
        this.createParticles(
            x * BLOCK_SIZE + BLOCK_SIZE / 2,
            y * BLOCK_SIZE + BLOCK_SIZE / 2,
            color,
            10
        );
    }
}
```

## 📚 项目开发工具链

### 1. 开发环境配置

```json
// package.json
{
  "name": "tetris-game",
  "version": "1.0.0",
  "description": "HTML5 Canvas Russian方块游戏",
  "main": "index.html",
  "scripts": {
    "start": "python -m http.server 8000",
    "build": "webpack --mode production",
    "build:dev": "webpack --mode development",
    "test": "jest",
    "test:watch": "jest --watch",
    "lint": "eslint src/",
    "lint:fix": "eslint src/ --fix"
  },
  "devDependencies": {
    "webpack": "^5.0.0",
    "webpack-cli": "^4.0.0",
    "jest": "^27.0.0",
    "eslint": "^7.32.0",
    "prettier": "^2.3.0"
  }
}
```

### 2. Webpack配置

```javascript
// webpack.config.js
const path = require('path');

module.exports = {
  entry: './src/index.js',
  output: {
    filename: 'bundle.js',
    path: path.resolve(__dirname, 'dist'),
    clean: true
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env']
          }
        }
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader']
      },
      {
        test: /\.(png|svg|jpg|gif)$/,
        type: 'asset/resource'
      }
    ]
  },
  devServer: {
    static: {
      directory: path.join(__dirname, 'dist')
    },
    compress: true,
    port: 9000,
    hot: true
  }
};
```

### 3. Babel配置

```json
// .babelrc
{
  "presets": [
    ["@babel/preset-env", {
      "targets": {
        "browsers": ["last 2 versions", "not dead"]
      }
    }]
  ]
}
```

## 🤝 贡献指南

### 1. 提交代码流程

```
1. Fork 项目仓库
2. 创建特性分支: git checkout -b feature/new-feature
3. 提交更改: git commit -m "Add new feature"
4. 推送分支: git push origin feature/new-feature
5. 创建 Pull Request
```

### 2. 提交信息规范

```bash
# 功能添加
git commit -m "feat: 添加音效系统"

# 问题修复
git commit -m "fix: 修复旋转碰撞检测错误"

# 文档更新
git commit -m "docs: 更新使用说明文档"

# 性能优化
git commit -m "perf: 优化渲染性能"

# 代码重构
git commit -m "refactor: 重构游戏状态管理"
```

### 3. 代码审查清单

- [ ] 代码符合项目规范
- [ ] 包含适当的注释
- [ ] 考虑了边界情况
- [ ] 添加了必要的测试
- [ ] 更新了相关文档
- [ ] 性能考虑充分
- [ ] 安全性问题已解决

---

## 🎉 结语

本开发指南为俄罗斯方块游戏项目提供了全面的技术文档。通过遵循这些最佳实践和设计模式，您可以轻松地维护、扩展和优化游戏代码。

**Happy Coding!** 🚀

- **文档版本**: 1.0.0
- **最后更新**: 2024年
- **维护者**: 开发团队