# 俄罗斯方块游戏快速入门指南

## 🎮 快速开始

### 1. 🚀 运行游戏
```bash
# 方法1：本地服务器 (推荐)
cd /path/to/project
python -m http.server 8000
# 访问 http://localhost:8000

# 方法2：直接打开
双击 index.html 文件
```

### 2. 🎯 开始游戏
点击"开始游戏"按钮即可开始！
- 🎲 方块从顶部随机生成
- 🎮 使用键盘或触屏控制
- 🏆 目标：消除行数，获得高分

## ⌨️ 基础操作

| 操作 | 键盘 | 触屏 | 说明 |
|------|------|------|------|
| 左移 | ← | 左滑 | 向移动方块 |
| 右移 | → | 右滑 | 向移动方块 |
| 加速 | ↓ | 下滑 | 加快下落速度 |
| 旋转 | ↑ | 上滑 | 旋转90度 |
| 直接落下 | 空格 | 双击 | 落到底部 |
| 开始游戏 | S | 开始按钮 | 开始/继续游戏 |
| 暂停 | P | 暂停按钮 | 暂停/继续 |
| 重置 | R | 重置按钮 | 重新开始 |

## 🎯 游戏目标
- ❌ 避免方块堆叠到顶部
- ✅ 消除完整行获得分数
- 📈 提升等级，获得更高分数
- 🏊 获得个人最高分记录

## 📊 计分规则
- 📇 单行消除：100 × 当前等级
- 📇 双行消除：300 × 当前等级
- 📇 三行消除：500 × 当前等级
- 📇 四行消除：800 × 当前等级

⚡ 等级每提升10行增加1级
⚡ 等级越高，下落速度越快

## 💡 新手技巧
1. **优先填充底部**：从下往上堆叠
2. **留出操作空间**：避免堆得太高
3. **灵活使用旋转**：找到最佳放置位置
4. **预览下一方块**：提前规划布局
5. **保持冷静**：不着急，仔细思考

## 📱 移动设备优化
- 🔄 横屏体验更佳
- 🧼 保持屏幕清洁
- 🔋 关闭省电模式
- 🎪 关闭后台应用

## ❓ 遇到问题？
1. 🔄 刷新页面 (Ctrl+F5)
2. 🔄 清除浏览器缓存
3. 🔄 使用现代浏览器
4. 🔄 检查网络连接

---

**享受游戏，创造高分！** 🎉