// 俄罗斯方块游戏核心功能测试

// 测试结果记录
const testResults = {
    passed: 0,
    failed: 0,
    tests: []
};

// 辅助函数：记录测试结果
function test(name, condition, message) {
    const result = {
        name: name,
        passed: condition,
        message: message || (condition ? '通过' : '失败')
    };

    testResults.tests.push(result);
    if (condition) {
        testResults.passed++;
        console.log(`✓ ${name}: ${result.message}`);
    } else {
        testResults.failed++;
        console.error(`✗ ${name}: ${result.message}`);
    }
}

// 开始测试
console.log('=== 俄罗斯方块游戏核心功能测试 ===\n');

// 1. 测试游戏配置常量
console.log('1. 测试游戏配置常量');
test('游戏板宽度', BOARD_WIDTH === 10, `游戏板宽度应为10，实际为${BOARD_WIDTH}`);
test('游戏板高度', BOARD_HEIGHT === 20, `游戏板高度应为20，实际为${BOARD_HEIGHT}`);
test('方块大小', BLOCK_SIZE === 30, `方块大小应为30，实际为${BLOCK_SIZE}`);

// 2. 测试方块形状定义
console.log('\n2. 测试方块形状定义');
test('方块类型数量', PIECES.length === 7, `应有7种方块类型，实际有${PIECES.length}种`);
test('方块颜色数量', PIECE_COLORS.length === 7, `应有7种方块颜色，实际有${PIECE_COLORS.length}种`);

// 验证每种方块形状
const pieceNames = ['I型', 'O型', 'T型', 'S型', 'Z型', 'J型', 'L型'];
pieceNames.forEach((name, index) => {
    const piece = PIECES[index];
    test(`${name}方块定义`,
        Array.isArray(piece) && piece.length > 0,
        `${name}方块${Array.isArray(piece) && piece.length > 0 ? '定义正确' : '定义错误'}`
    );
});

// 3. 测试游戏状态初始化
console.log('\n3. 测试游戏状态');
test('游戏状态对象存在', typeof gameState === 'object', '游戏状态对象应该存在');
test('游戏板数组', Array.isArray(gameState.board), '游戏板应该是数组');
test('分数初始值', gameState.score === 0, `分数初始值应为0，实际为${gameState.score}`);
test('等级初始值', gameState.level === 1, `等级初始值应为1，实际为${gameState.level}`);
test('消除行数初始值', gameState.lines === 0, `消除行数初始值应为0，实际为${gameState.lines}`);

// 4. 测试核心函数存在性
console.log('\n4. 测试核心函数');
const coreFunctions = [
    'createPiece',
    'rotatePiece',
    'checkCollision',
    'lockPiece',
    'clearLines',
    'updateScore',
    'renderBoard',
    'drawBlock',
    'moveDown',
    'moveLeft',
    'moveRight',
    'rotateCurrentPiece',
    'hardDrop',
    'startGame',
    'pauseGame',
    'resetGame'
];

coreFunctions.forEach(funcName => {
    test(`函数 ${funcName}`,
        typeof window[funcName] === 'function',
        `函数 ${funcName} ${typeof window[funcName] === 'function' ? '已定义' : '未定义'}`
    );
});

// 5. 测试方块创建功能
console.log('\n5. 测试方块创建');
try {
    const testPiece = createPiece();
    test('创建方块返回对象', typeof testPiece === 'object', '创建方块应返回对象');
    test('方块包含type属性', 'type' in testPiece, '方块对象应包含type属性');
    test('方块包含shape属性', 'shape' in testPiece, '方块对象应包含shape属性');
    test('方块包含x坐标', 'x' in testPiece, '方块对象应包含x坐标');
    test('方块包含y坐标', 'y' in testPiece, '方块对象应包含y坐标');
    test('方块包含color属性', 'color' in testPiece, '方块对象应包含color属性');
    test('方块type范围', testPiece.type >= 0 && testPiece.type < 7,
        `方块type应在0-6之间，实际为${testPiece.type}`);
} catch (error) {
    test('方块创建异常', false, `创建方块时出错: ${error.message}`);
}

// 6. 测试方块旋转功能
console.log('\n6. 测试方块旋转');
try {
    const originalPiece = createPiece();
    const rotatedPiece = rotatePiece(originalPiece);
    test('旋转方块返回对象', typeof rotatedPiece === 'object', '旋转方块应返回对象');
    test('旋转后shape改变',
        JSON.stringify(originalPiece.shape) !== JSON.stringify(rotatedPiece.shape),
        '旋转后方块形状应该改变'
    );
} catch (error) {
    test('方块旋转异常', false, `旋转方块时出错: ${error.message}`);
}

// 7. 测试碰撞检测
console.log('\n7. 测试碰撞检测');
try {
    const testBoard = Array(BOARD_HEIGHT).fill().map(() => Array(BOARD_WIDTH).fill(0));
    const testPiece = createPiece();

    // 测试正常位置不碰撞
    const normalCollision = checkCollision(testPiece, testBoard);
    test('正常位置碰撞检测', typeof normalCollision === 'boolean',
        `碰撞检测应返回布尔值，实际返回${typeof normalCollision}`);

    // 测试左边界碰撞
    testPiece.x = -1;
    const leftCollision = checkCollision(testPiece, testBoard);
    test('左边界碰撞检测', leftCollision === true,
        `左边界应检测到碰撞，实际返回${leftCollision}`);

    // 测试右边界碰撞
    testPiece.x = BOARD_WIDTH;
    const rightCollision = checkCollision(testPiece, testBoard);
    test('右边界碰撞检测', rightCollision === true,
        `右边界应检测到碰撞，实际返回${rightCollision}`);

    // 测试底部碰撞
    testPiece.x = 0;
    testPiece.y = BOARD_HEIGHT;
    const bottomCollision = checkCollision(testPiece, testBoard);
    test('底部碰撞检测', bottomCollision === true,
        `底部应检测到碰撞，实际返回${bottomCollision}`);
} catch (error) {
    test('碰撞检测异常', false, `碰撞检测时出错: ${error.message}`);
}

// 8. 测试方块锁定功能
console.log('\n8. 测试方块锁定');
try {
    const testBoard = Array(BOARD_HEIGHT).fill().map(() => Array(BOARD_WIDTH).fill(0));
    const testPiece = createPiece();
    testPiece.y = BOARD_HEIGHT - 2;

    lockPiece(testPiece, testBoard);

    // 检查是否有方块被锁定到游戏板
    let hasLockedBlock = false;
    for (let y = 0; y < BOARD_HEIGHT; y++) {
        for (let x = 0; x < BOARD_WIDTH; x++) {
            if (testBoard[y][x] !== 0) {
                hasLockedBlock = true;
                break;
            }
        }
        if (hasLockedBlock) break;
    }

    test('方块锁定到游戏板', hasLockedBlock,
        `方块应被锁定到游戏板，实际${hasLockedBlock ? '已锁定' : '未锁定'}`);
} catch (error) {
    test('方块锁定异常', false, `锁定方块时出错: ${error.message}`);
}

// 9. 测试行消除功能
console.log('\n9. 测试行消除');
try {
    const testBoard = Array(BOARD_HEIGHT).fill().map(() => Array(BOARD_WIDTH).fill(0));

    // 填满最底下一行
    testBoard[BOARD_HEIGHT - 1].fill(1);

    const linesCleared = clearLines(testBoard);

    test('行消除返回值', typeof linesCleared === 'number',
        `行消除应返回数字，实际返回${typeof linesCleared}`);
    test('消除一行', linesCleared === 1,
        `应消除1行，实际消除${linesCleared}行`);
    test('消除后顶部添加空行',
        testBoard[0].every(cell => cell === 0),
        '消除行后应在顶部添加空行'
    );
} catch (error) {
    test('行消除异常', false, `消除行时出错: ${error.message}`);
}

// 10. 测试分数更新
console.log('\n10. 测试分数更新');
try {
    const oldScore = gameState.score;
    updateScore(1);
    test('分数更新', gameState.score >= oldScore,
        `消除1行后分数应增加，原分数${oldScore}，现分数${gameState.score}`);
} catch (error) {
    test('分数更新异常', false, `更新分数时出错: ${error.message}`);
}

// 11. 测试Canvas元素
console.log('\n11. 测试Canvas元素');
const gameBoard = document.getElementById('gameBoard');
const nextPieceCanvas = document.getElementById('nextPieceCanvas');

test('游戏板Canvas存在', gameBoard !== null, '游戏板Canvas应该存在');
test('游戏板Canvas类型', gameBoard instanceof HTMLCanvasElement,
    '游戏板应该是Canvas元素');
test('下一个方块Canvas存在', nextPieceCanvas !== null, '下一个方块Canvas应该存在');
test('下一个方块Canvas类型', nextPieceCanvas instanceof HTMLCanvasElement,
    '下一个方块Canvas应该是Canvas元素');

if (gameBoard) {
    test('游戏板Canvas宽度', gameBoard.width === BOARD_WIDTH * BLOCK_SIZE,
        `游戏板宽度应为${BOARD_WIDTH * BLOCK_SIZE}，实际为${gameBoard.width}`);
    test('游戏板Canvas高度', gameBoard.height === BOARD_HEIGHT * BLOCK_SIZE,
        `游戏板高度应为${BOARD_HEIGHT * BLOCK_SIZE}，实际为${gameBoard.height}`);
}

// 12. 测试按钮元素
console.log('\n12. 测试控制按钮');
const startBtn = document.getElementById('startBtn');
const pauseBtn = document.getElementById('pauseBtn');
const resetBtn = document.getElementById('resetBtn');

test('开始按钮存在', startBtn !== null, '开始按钮应该存在');
test('暂停按钮存在', pauseBtn !== null, '暂停按钮应该存在');
test('重置按钮存在', resetBtn !== null, '重置按钮应该存在');

// 13. 测试信息显示元素
console.log('\n13. 测试信息显示元素');
const scoreEl = document.getElementById('score');
const levelEl = document.getElementById('level');
const linesEl = document.getElementById('lines');
const gameStatusEl = document.getElementById('gameStatus');
const highScoreEl = document.getElementById('highScore');
const gameTimeEl = document.getElementById('gameTime');

test('分数显示元素存在', scoreEl !== null, '分数显示元素应该存在');
test('等级显示元素存在', levelEl !== null, '等级显示元素应该存在');
test('行数显示元素存在', linesEl !== null, '行数显示元素应该存在');
test('游戏状态显示元素存在', gameStatusEl !== null, '游戏状态显示元素应该存在');
test('最高分显示元素存在', highScoreEl !== null, '最高分显示元素应该存在');
test('游戏时间显示元素存在', gameTimeEl !== null, '游戏时间显示元素应该存在');

// 14. 测试游戏API
console.log('\n14. 测试游戏API');
test('游戏API对象存在', typeof window.tetrisGame === 'object',
    '全局游戏API对象应该存在');

if (window.tetrisGame) {
    const apiMethods = ['start', 'pause', 'reset', 'moveLeft', 'moveRight', 'rotate', 'hardDrop'];
    apiMethods.forEach(method => {
        test(`游戏API方法 ${method}`,
            typeof window.tetrisGame[method] === 'function',
            `游戏API应包含${method}方法`
        );
    });
}

// 输出测试总结
console.log('\n' + '='.repeat(50));
console.log('测试总结:');
console.log(`总计: ${testResults.tests.length} 个测试`);
console.log(`通过: ${testResults.passed} 个 (${(testResults.passed / testResults.tests.length * 100).toFixed(2)}%)`);
console.log(`失败: ${testResults.failed} 个 (${(testResults.failed / testResults.tests.length * 100).toFixed(2)}%)`);
console.log('='.repeat(50));

// 如果有失败的测试，列出详情
if (testResults.failed > 0) {
    console.log('\n失败的测试详情:');
    testResults.tests.filter(t => !t.passed).forEach(t => {
        console.log(`  - ${t.name}: ${t.message}`);
    });
}

// 导出测试结果
window.testResults = testResults;