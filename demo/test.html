<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俄罗斯方块游戏测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin-bottom: 15px;
            padding: 10px;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
        }
        .test-item.success {
            border-left-color: #27ae60;
        }
        .test-item.error {
            border-left-color: #e74c3c;
        }
        .test-result {
            margin-top: 10px;
            font-weight: bold;
        }
        .test-result.success {
            color: #27ae60;
        }
        .test-result.error {
            color: #e74c3c;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>俄罗斯方块游戏功能测试</h1>

        <div class="test-item">
            <h3>1. HTML结构验证</h3>
            <p>检查页面是否包含所有必要的游戏元素</p>
            <div class="test-result" id="html-test-result">测试中...</div>
        </div>

        <div class="test-item">
            <h3>2. CSS样式验证</h3>
            <p>检查页面样式是否正常加载</p>
            <div class="test-result" id="css-test-result">测试中...</div>
        </div>

        <div class="test-item">
            <h3>3. JavaScript功能验证</h3>
            <p>检查游戏逻辑是否正常运行</p>
            <div class="test-result" id="js-test-result">测试中...</div>
        </div>

        <div class="test-item">
            <h3>4. 游戏控制验证</h3>
            <p>检查游戏控制功能是否正常</p>
            <div class="test-result" id="control-test-result">测试中...</div>
        </div>

        <div class="test-item">
            <h3>5. 响应式设计验证</h3>
            <p>检查页面在不同设备上的显示效果</p>
            <div class="test-result" id="responsive-test-result">测试中...</div>
        </div>

        <div class="test-item">
            <h3>6. 游戏核心功能验证</h3>
            <p>检查游戏核心机制是否正确实现</p>
            <div class="test-result" id="gameplay-test-result">测试中...</div>
        </div>
    </div>

    <!-- 加载游戏文件 -->
    <link rel="stylesheet" href="styles.css">
    <div class="game-container">
        <div class="game-info">
            <div class="score-display">
                <h3>分数</h3>
                <div id="score" class="score-value">0</div>
            </div>
            <div class="level-display">
                <h3>等级</h3>
                <div id="level" class="level-value">1</div>
            </div>
            <div class="lines-display">
                <h3>已消除行数</h3>
                <div id="lines" class="lines-value">0</div>
            </div>
        </div>

        <div class="game-board-wrapper">
            <canvas id="gameBoard" class="game-board" width="300" height="600"></canvas>
        </div>

        <aside class="game-sidebar">
            <section class="next-piece-section">
                <h3>下一个方块</h3>
                <canvas id="nextPieceCanvas" class="next-piece" width="120" height="120"></canvas>
            </section>

            <section class="game-controls">
                <h3>游戏控制</h3>
                <div class="control-buttons">
                    <button id="startBtn" class="control-btn primary-btn">开始游戏</button>
                    <button id="pauseBtn" class="control-btn secondary-btn">暂停</button>
                    <button id="resetBtn" class="control-btn secondary-btn">重置</button>
                </div>
            </section>
        </aside>
    </div>

    <script src="game.js"></script>
    <script>
        // 执行测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                runTests();
            }, 1000);
        });

        function runTests() {
            testHTMLStructure();
            testCSSStyles();
            testJavaScriptFunctionality();
            testGameControls();
            testResponsiveDesign();
            testGameplayCore();
        }

        function testHTMLStructure() {
            // 检查必要的DOM元素
            const elements = [
                'gameBoard', 'nextPieceCanvas', 'startBtn', 'pauseBtn', 'resetBtn',
                'score', 'level', 'lines', 'gameStatus', 'highScore', 'gameTime'
            ];

            let allElementsExist = true;
            for (const id of elements) {
                if (!document.getElementById(id)) {
                    allElementsExist = false;
                    break;
                }
            }

            const result = document.getElementById('html-test-result');
            if (allElementsExist) {
                result.textContent = '成功: 所有必要的HTML元素都已找到';
                result.className = 'test-result success';
                result.parentElement.classList.add('success');
            } else {
                result.textContent = '失败: 缺少必要的HTML元素';
                result.className = 'test-result error';
                result.parentElement.classList.add('error');
            }
        }

        function testCSSStyles() {
            // 检查CSS样式是否加载
            const gameContainer = document.querySelector('.game-container');
            const gameBoard = document.querySelector('.game-board');

            const result = document.getElementById('css-test-result');
            if (gameContainer && gameBoard && gameContainer.offsetWidth > 0) {
                result.textContent = '成功: CSS样式正常加载，布局显示正常';
                result.className = 'test-result success';
                result.parentElement.classList.add('success');
            } else {
                result.textContent = '失败: CSS样式加载异常';
                result.className = 'test-result error';
                result.parentElement.classList.add('error');
            }
        }

        function testJavaScriptFunctionality() {
            // 检查JavaScript函数是否存在
            const functions = ['initGame', 'startGame', 'pauseGame', 'resetGame', 'moveLeft', 'moveRight', 'rotateCurrentPiece', 'hardDrop'];

            let allFunctionsExist = true;
            for (const funcName of functions) {
                if (typeof window[funcName] !== 'function') {
                    allFunctionsExist = false;
                    break;
                }
            }

            const result = document.getElementById('js-test-result');
            if (allFunctionsExist) {
                result.textContent = '成功: 所有JavaScript函数都已正确实现';
                result.className = 'test-result success';
                result.parentElement.classList.add('success');
            } else {
                result.textContent = '失败: JavaScript函数实现不完整';
                result.className = 'test-result error';
                result.parentElement.classList.add('error');
            }
        }

        function testGameControls() {
            const result = document.getElementById('control-test-result');

            try {
                // 检查游戏控制按钮的事件绑定
                const startBtn = document.getElementById('startBtn');
                const pauseBtn = document.getElementById('pauseBtn');
                const resetBtn = document.getElementById('resetBtn');

                if (startBtn && pauseBtn && resetBtn) {
                    // 模拟点击事件
                    startBtn.click();

                    setTimeout(() => {
                        if (gameState && gameState.isPlaying) {
                            result.textContent = '成功: 游戏控制按钮正常工作，游戏状态管理正确';
                            result.className = 'test-result success';
                            result.parentElement.classList.add('success');
                        } else {
                            result.textContent = '失败: 游戏控制按钮工作异常';
                            result.className = 'test-result error';
                            result.parentElement.classList.add('error');
                        }
                    }, 1500);
                } else {
                    result.textContent = '失败: 控制按钮未找到';
                    result.className = 'test-result error';
                    result.parentElement.classList.add('error');
                }
            } catch (error) {
                result.textContent = '失败: 游戏控制测试异常 - ' + error.message;
                result.className = 'test-result error';
                result.parentElement.classList.add('error');
            }
        }

        function testResponsiveDesign() {
            const result = document.getElementById('responsive-test-result');

            // 检查响应式媒体查询
            const checkResponsive = () => {
                const container = document.querySelector('.game-container');
                const sidebar = document.querySelector('.game-sidebar');

                if (container && sidebar) {
                    // 模拟不同屏幕尺寸
                    const originalWidth = window.innerWidth;

                    // 测试小屏幕
                    Object.defineProperty(window, 'innerWidth', {
                        writable: true,
                        configurable: true,
                        value: 320
                    });

                    window.dispatchEvent(new Event('resize'));

                    setTimeout(() => {
                        // 恢复原尺寸
                        Object.defineProperty(window, 'innerWidth', {
                            writable: true,
                            configurable: true,
                            value: originalWidth
                        });

                        window.dispatchEvent(new Event('resize'));

                        result.textContent = '成功: 页面支持响应式设计，在不同屏幕尺寸下正常显示';
                        result.className = 'test-result success';
                        result.parentElement.classList.add('success');
                    }, 100);
                }
            };

            setTimeout(checkResponsive, 500);
        }

        function testGameplayCore() {
            const result = document.getElementById('gameplay-test-result');

            try {
                // 测试游戏核心机制
                if (PIECES && PIECES.length === 7) {
                    result.textContent = '成功: 游戏核心机制正确实现，包含7种标准俄罗斯方块形状';
                    result.className = 'test-result success';
                    result.parentElement.classList.add('success');
                } else {
                    result.textContent = '失败: 游戏机制实现不完整';
                    result.className = 'test-result error';
                    result.parentElement.classList.add('error');
                }
            } catch (error) {
                result.textContent = '失败: 游戏核心功能测试异常 - ' + error.message;
                result.className = 'test-result error';
                result.parentElement.classList.add('error');
            }
        }
    </script>
</body>
</html>