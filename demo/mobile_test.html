<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="俄罗斯方块移动端测试">
    <title>俄罗斯方块 - 移动端测试</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        /* 测试页面专用样式 */
        .test-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: white;
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .test-header h1 {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .test-section h2 {
            margin-bottom: 15px;
            color: #f39c12;
        }

        .test-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
        }

        .test-item h3 {
            margin-bottom: 10px;
            color: #3498db;
        }

        .test-item p {
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .test-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .test-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
        }

        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 8px;
            font-weight: bold;
        }

        .test-result.success {
            background: rgba(46, 204, 113, 0.2);
            color: #2ecc71;
        }

        .test-result.error {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
        }

        .test-result.info {
            background: rgba(52, 152, 219, 0.2);
            color: #3498db;
        }

        .gesture-area {
            background: rgba(255, 255, 255, 0.1);
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            font-size: 1.2em;
            text-align: center;
            cursor: pointer;
            user-select: none;
            -webkit-user-select: none;
        }

        .mobile-controls-test {
            margin: 20px 0;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .info-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .info-card h4 {
            margin-bottom: 8px;
            color: #f39c12;
        }

        .info-card .value {
            font-size: 1.5em;
            font-weight: bold;
            color: #3498db;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .test-header h1 {
                font-size: 1.5em;
            }

            .test-section {
                padding: 15px;
            }

            .test-controls {
                justify-content: center;
            }

            .test-btn {
                flex: 1;
                min-width: 120px;
            }
        }

        @media (max-width: 480px) {
            .test-container {
                padding: 10px;
            }

            .test-header h1 {
                font-size: 1.3em;
            }

            .test-section {
                padding: 12px;
                margin-bottom: 15px;
            }

            .info-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <header class="test-header">
            <h1>🎮 俄罗斯方块移动端测试</h1>
            <p>测试所有移动端功能和响应式设计</p>
        </header>

        <main>
            <!-- 设备信息检测 -->
            <section class="test-section">
                <h2>📱 设备信息检测</h2>
                <div class="info-grid" id="deviceInfo">
                    <div class="info-card">
                        <h4>屏幕宽度</h4>
                        <div class="value" id="screenWidth">-</div>
                    </div>
                    <div class="info-card">
                        <h4>屏幕高度</h4>
                        <div class="value" id="screenHeight">-</div>
                    </div>
                    <div class="info-card">
                        <h4>设备类型</h4>
                        <div class="value" id="deviceType">-</div>
                    </div>
                    <div class="info-card">
                        <h4>触屏支持</h4>
                        <div class="value" id="touchSupport">-</div>
                    </div>
                    <div class="info-card">
                        <h4>震动支持</h4>
                        <div class="value" id="vibrateSupport">-</div>
                    </div>
                    <div class="info-card">
                        <h4>方向</h4>
                        <div class="value" id="orientation">-</div>
                    </div>
                </div>
            </section>

            <!-- 手势测试区域 -->
            <section class="test-section">
                <h2>✋ 手势测试</h2>
                <div class="test-item">
                    <h3>触控手势测试</h3>
                    <p>在下方区域测试各种手势操作</p>
                    <div class="gesture-area" id="gestureArea">
                        <div>
                            <div>👆 单击：旋转</div>
                            <div>👆👆 双击：硬降</div>
                            <div>↔️ 滑动：移动</div>
                            <div>👆 长按：保留方块</div>
                        </div>
                    </div>
                    <div id="gestureResult" class="test-result info">等待手势输入...</div>
                </div>
            </section>

            <!-- 虚拟按键测试 -->
            <section class="test-section">
                <h2>🎮 虚拟按键测试</h2>
                <div class="test-item">
                    <h3>移动端控制按钮</h3>
                    <p>测试虚拟按键的响应性和触觉反馈</p>
                    <div class="mobile-controls-test">
                        <div class="mobile-control-row">
                            <button class="mobile-btn" id="testLeft" aria-label="向左移动" data-action="left">←</button>
                            <button class="mobile-btn" id="testRotate" aria-label="旋转方块" data-action="rotate">↻</button>
                            <button class="mobile-btn" id="testRight" aria-label="向右移动" data-action="right">→</button>
                            <button class="mobile-btn" id="testHold" aria-label="保留方块" data-action="hold">H</button>
                        </div>
                        <div class="mobile-control-row">
                            <button class="mobile-btn" id="testDown" aria-label="加速下落" data-action="down">↓</button>
                            <button class="mobile-btn" id="testDrop" aria-label="直接落下" data-action="drop">⬇</button>
                            <button class="mobile-btn" id="testPause" aria-label="暂停游戏" data-action="pause">⏸</button>
                        </div>
                    </div>
                    <div id="buttonResult" class="test-result info">点击按钮测试...</div>
                </div>
            </section>

            <!-- 功能测试 -->
            <section class="test-section">
                <h2>🔧 功能测试</h2>
                <div class="test-item">
                    <h3>响应式布局测试</h3>
                    <p>测试不同屏幕尺寸下的布局适配</p>
                    <div class="test-controls">
                        <button class="test-btn" onclick="testResponsiveLayout()">测试响应式布局</button>
                        <button class="test-btn" onclick="testOrientationChange()">测试屏幕旋转</button>
                        <button class="test-btn" onclick="testSafeArea()">测试安全区域</button>
                    </div>
                    <div id="responsiveResult" class="test-result info"></div>
                </div>

                <div class="test-item">
                    <h3>性能测试</h3>
                    <p>测试移动端性能和流畅度</p>
                    <div class="test-controls">
                        <button class="test-btn" onclick="testPerformance()">运行性能测试</button>
                        <button class="test-btn" onclick="testTouchLatency()">测试触控延迟</button>
                        <button class="test-btn" onclick="testHapticFeedback()">测试触觉反馈</button>
                    </div>
                    <div id="performanceResult" class="test-result info"></div>
                </div>

                <div class="test-item">
                    <h3>游戏测试</h3>
                    <p>启动实际游戏进行完整测试</p>
                    <div class="test-controls">
                        <button class="test-btn" onclick="startGameTest()">启动游戏测试</button>
                        <button class="test-btn" onclick="testSoundSystem()">测试音效系统</button>
                        <button class="test-btn" onclick="exportTestReport()">导出测试报告</button>
                    </div>
                    <div id="gameTestResult" class="test-result info"></div>
                </div>
            </section>
        </main>
    </div>

    <script>
        // 测试页面主控制器
        class MobileTestController {
            constructor() {
                this.testResults = [];
                this.init();
            }

            init() {
                this.detectDeviceInfo();
                this.initGestureTest();
                this.initButtonTest();
                this.initOrientationListener();
            }

            detectDeviceInfo() {
                const deviceInfo = {
                    screenWidth: window.screen.width,
                    screenHeight: window.screen.height,
                    deviceType: this.getDeviceType(),
                    touchSupport: 'ontouchstart' in window,
                    vibrateSupport: 'vibrate' in navigator,
                    orientation: this.getOrientation()
                };

                // 更新显示
                document.getElementById('screenWidth').textContent = `${deviceInfo.screenWidth}px`;
                document.getElementById('screenHeight').textContent = `${deviceInfo.screenHeight}px`;
                document.getElementById('deviceType').textContent = deviceInfo.deviceType;
                document.getElementById('touchSupport').textContent = deviceInfo.touchSupport ? '✅' : '❌';
                document.getElementById('vibrateSupport').textContent = deviceInfo.vibrateSupport ? '✅' : '❌';
                document.getElementById('orientation').textContent = deviceInfo.orientation;

                this.testResults.push({
                    test: '设备检测',
                    result: 'success',
                    details: deviceInfo
                });
            }

            getDeviceType() {
                const width = window.screen.width;
                const height = window.screen.height;

                if (width <= 360) return '超小屏';
                if (width <= 480) return '小屏手机';
                if (width <= 768) return '手机';
                if (width <= 1024) return '平板';
                return '桌面';
            }

            getOrientation() {
                if (window.matchMedia("(orientation: portrait)").matches) {
                    return '竖屏';
                } else if (window.matchMedia("(orientation: landscape)").matches) {
                    return '横屏';
                }
                return '未知';
            }

            initGestureTest() {
                const gestureArea = document.getElementById('gestureArea');
                let touchStartTime = 0;
                let touchStartX = 0;
                let touchStartY = 0;
                let lastTapTime = 0;
                let tapCount = 0;

                gestureArea.addEventListener('touchstart', (e) => {
                    e.preventDefault();
                    const touch = e.touches[0];
                    touchStartTime = Date.now();
                    touchStartX = touch.clientX;
                    touchStartY = touch.clientY;

                    // 双击检测
                    const currentTime = Date.now();
                    if (currentTime - lastTapTime < 300) {
                        tapCount++;
                        if (tapCount === 2) {
                            this.handleGesture('doubleTap', '双击');
                            tapCount = 0;
                        }
                    } else {
                        tapCount = 1;
                    }
                    lastTapTime = currentTime;

                    // 长按检测
                    setTimeout(() => {
                        if (Date.now() - touchStartTime > 500) {
                            this.handleGesture('longPress', '长按');
                        }
                    }, 500);
                });

                gestureArea.addEventListener('touchend', (e) => {
                    e.preventDefault();
                    const touch = e.changedTouches[0];
                    const touchEndTime = Date.now();
                    const touchDuration = touchEndTime - touchStartTime;

                    const deltaX = touch.clientX - touchStartX;
                    const deltaY = touch.clientY - touchStartY;
                    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

                    if (distance > 30 && touchDuration < 500) {
                        // 滑动手势
                        if (Math.abs(deltaX) > Math.abs(deltaY)) {
                            this.handleGesture(deltaX > 0 ? 'swipeRight' : 'swipeLeft', '滑动');
                        } else {
                            this.handleGesture(deltaY > 0 ? 'swipeDown' : 'swipeUp', '滑动');
                        }
                    } else if (touchDuration < 200 && distance < 10 && tapCount === 1) {
                        // 单击
                        setTimeout(() => {
                            if (tapCount === 1) {
                                this.handleGesture('tap', '单击');
                                tapCount = 0;
                            }
                        }, 300);
                    }
                });
            }

            handleGesture(type, description) {
                const gestureResult = document.getElementById('gestureResult');
                gestureResult.className = 'test-result success';
                gestureResult.textContent = `检测到${description}手势: ${type}`;

                // 触觉反馈
                if (navigator.vibrate) {
                    navigator.vibrate([20]);
                }

                this.testResults.push({
                    test: '手势测试',
                    result: 'success',
                    details: { type, description }
                });
            }

            initButtonTest() {
                const buttons = ['testLeft', 'testRight', 'testRotate', 'testDown', 'testDrop', 'testHold', 'testPause'];

                buttons.forEach(buttonId => {
                    const btn = document.getElementById(buttonId);
                    if (btn) {
                        btn.addEventListener('click', () => {
                            this.handleButtonClick(buttonId.replace('test', ''));
                        });

                        btn.addEventListener('touchstart', (e) => {
                            e.preventDefault();
                            btn.classList.add('pressed');
                        });

                        btn.addEventListener('touchend', (e) => {
                            e.preventDefault();
                            btn.classList.remove('pressed');
                        });
                    }
                });
            }

            handleButtonClick(action) {
                const buttonResult = document.getElementById('buttonResult');
                buttonResult.className = 'test-result success';
                buttonResult.textContent = `按钮 ${action} 响应正常`;

                // 触觉反馈
                if (navigator.vibrate) {
                    navigator.vibrate([10]);
                }

                this.testResults.push({
                    test: '按钮测试',
                    result: 'success',
                    details: { action }
                });
            }

            initOrientationListener() {
                window.addEventListener('orientationchange', () => {
                    setTimeout(() => {
                        document.getElementById('orientation').textContent = this.getOrientation();
                        this.testResults.push({
                            test: '屏幕旋转',
                            result: 'success',
                            details: { orientation: this.getOrientation() }
                        });
                    }, 100);
                });
            }
        }

        // 测试函数
        function testResponsiveLayout() {
            const result = document.getElementById('responsiveResult');
            result.className = 'test-result info';
            result.textContent = '测试响应式布局中...';

            setTimeout(() => {
                const width = window.innerWidth;
                const height = window.innerHeight;

                let layoutType = '';
                if (width <= 360) layoutType = '超小屏布局';
                else if (width <= 480) layoutType = '小屏布局';
                else if (width <= 768) layoutType = '移动端布局';
                else layoutType = '桌面端布局';

                result.className = 'test-result success';
                result.textContent = `响应式布局测试成功 - 当前使用: ${layoutType} (${width}×${height})`;
            }, 1000);
        }

        function testOrientationChange() {
            const result = document.getElementById('responsiveResult');
            result.className = 'test-result info';
            result.textContent = '请旋转设备测试方向变化...';
        }

        function testSafeArea() {
            const result = document.getElementById('responsiveResult');

            // 检测安全区域支持
            const hasSafeArea = CSS.supports('padding: max(0px)');

            if (hasSafeArea) {
                result.className = 'test-result success';
                result.textContent = '安全区域支持: ✅';
            } else {
                result.className = 'test-result error';
                result.textContent = '安全区域支持: ❌';
            }
        }

        function testPerformance() {
            const result = document.getElementById('performanceResult');
            result.className = 'test-result info';
            result.textContent = '运行性能测试...';

            // 简单的性能测试
            const startTime = performance.now();

            // 模拟游戏渲染循环
            let iterations = 0;
            const maxIterations = 1000;

            function runTest() {
                for (let i = 0; i < 100; i++) {
                    // 模拟游戏逻辑
                    Math.random();
                    iterations++;
                }

                if (iterations < maxIterations) {
                    requestAnimationFrame(runTest);
                } else {
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    const fps = Math.round(maxIterations / (duration / 1000));

                    result.className = fps > 30 ? 'test-result success' : 'test-result error';
                    result.textContent = `性能测试完成 - FPS: ${fps}, 耗时: ${duration.toFixed(2)}ms`;
                }
            }

            requestAnimationFrame(runTest);
        }

        function testTouchLatency() {
            const result = document.getElementById('performanceResult');
            result.className = 'test-result info';
            result.textContent = '请快速点击屏幕测试触控延迟...';

            let startTime = Date.now();
            let clicks = 0;

            const clickHandler = () => {
                clicks++;
                const latency = Date.now() - startTime;

                if (clicks >= 5) {
                    const avgLatency = latency / clicks;
                    result.className = avgLatency < 100 ? 'test-result success' : 'test-result error';
                    result.textContent = `触控延迟测试 - 平均延迟: ${avgLatency.toFixed(2)}ms`;
                    document.removeEventListener('click', clickHandler);
                }
            };

            document.addEventListener('click', clickHandler);
        }

        function testHapticFeedback() {
            const result = document.getElementById('performanceResult');

            if (navigator.vibrate) {
                // 测试不同的震动模式
                navigator.vibrate([20]);
                setTimeout(() => navigator.vibrate([10, 50, 10]), 300);
                setTimeout(() => navigator.vibrate([100, 50, 100]), 600);

                result.className = 'test-result success';
                result.textContent = '触觉反馈测试完成 - 支持多种震动模式';
            } else {
                result.className = 'test-result error';
                result.textContent = '触觉反馈测试失败 - 设备不支持震动';
            }
        }

        function startGameTest() {
            const result = document.getElementById('gameTestResult');
            result.className = 'test-result info';
            result.textContent = '准备启动游戏测试...';

            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
        }

        function testSoundSystem() {
            const result = document.getElementById('gameTestResult');

            // 检查音频上下文支持
            const audioContextSupported = window.AudioContext || window.webkitAudioContext;

            if (audioContextSupported) {
                result.className = 'test-result success';
                result.textContent = '音效系统测试完成 - 支持Web Audio API';

                // 尝试播放测试音
                try {
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();

                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);

                    oscillator.frequency.value = 440;
                    gainNode.gain.value = 0.1;

                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.1);
                } catch (error) {
                    console.log('音频播放测试失败:', error);
                }
            } else {
                result.className = 'test-result error';
                result.textContent = '音效系统测试失败 - 不支持Web Audio API';
            }
        }

        function exportTestReport() {
            const report = {
                timestamp: new Date().toISOString(),
                deviceInfo: {
                    userAgent: navigator.userAgent,
                    screen: `${window.screen.width}x${window.screen.height}`,
                    viewport: `${window.innerWidth}x${window.innerHeight}`,
                    devicePixelRatio: window.devicePixelRatio
                },
                testResults: window.testController ? window.testController.testResults : [],
                summary: {
                    total: window.testController ? window.testController.testResults.length : 0,
                    passed: window.testController ? window.testController.testResults.filter(r => r.result === 'success').length : 0
                }
            };

            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `tetris-mobile-test-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);

            const result = document.getElementById('gameTestResult');
            result.className = 'test-result success';
            result.textContent = '测试报告已导出';
        }

        // 初始化测试控制器
        window.testController = new MobileTestController();
    </script>
</body>
</html>