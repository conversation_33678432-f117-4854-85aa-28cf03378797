<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音效系统测试</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .test-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            padding: 30px;
            max-width: 600px;
            width: 100%;
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            border: 2px solid #e9ecef;
        }

        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
        }

        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }

        .test-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
        }

        .test-btn:active {
            transform: translateY(0);
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            background: #e8f5e8;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            color: #155724;
        }

        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }

        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }

        .volume-control {
            margin: 20px 0;
            padding: 15px;
            background: #fff;
            border-radius: 8px;
            border: 2px solid #dee2e6;
        }

        .volume-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #dee2e6;
            outline: none;
            -webkit-appearance: none;
            margin: 10px 0;
        }

        .volume-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(45deg, #3498db, #2980b9);
            cursor: pointer;
        }

        .compatibility-info {
            font-size: 14px;
            color: #6c757d;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎵 俄罗斯方块音效系统测试</h1>

        <div class="test-section">
            <h3>📊 音效系统状态</h3>
            <div id="status" class="status">正在初始化...</div>
            <div class="compatibility-info" id="compatibility"></div>
        </div>

        <div class="test-section">
            <h3>🔊 音量控制</h3>
            <div class="volume-control">
                <label>音量: <span id="volumeValue">50%</span></label>
                <input type="range" id="volumeSlider" class="volume-slider" min="0" max="100" value="50">
                <button id="muteToggle" class="test-btn" style="margin-top: 10px;">🔊 音效开启</button>
            </div>
        </div>

        <div class="test-section">
            <h3>🎮 基础音效测试</h3>
            <div class="test-buttons">
                <button class="test-btn" data-sound="move">⬅️ 移动音效</button>
                <button class="test-btn" data-sound="rotate">🔄 旋转音效</button>
                <button class="test-btn" data-sound="softDrop">⬇️ 软下落音效</button>
                <button class="test-btn" data-sound="hardDrop">⏬ 硬降音效</button>
                <button class="test-btn" data-sound="hold">🤚 保留音效</button>
                <button class="test-btn" data-sound="levelUp">⬆️ 升级音效</button>
            </div>
        </div>

        <div class="test-section">
            <h3>✨ 消除音效测试</h3>
            <div class="test-buttons">
                <button class="test-btn" data-sound="clear1">➖ 单行消除</button>
                <button class="test-btn" data-sound="clear2">↔️ 双行消除</button>
                <button class="test-btn" data-sound="clear3">↕️ 三行消除</button>
                <button class="test-btn" data-sound="tetris">💎 四行消除(Tetris)</button>
                <button class="test-btn" data-sound="tSpin">🌟 T-Spin音效</button>
                <button class="test-btn" data-sound="combo">🔥 连击音效</button>
            </div>
        </div>

        <div class="test-section">
            <h3>🎵 特殊音效测试</h3>
            <div class="test-buttons">
                <button class="test-btn" data-sound="gameOver">💀 游戏结束</button>
                <button class="test-btn" onclick="testSequence()">🎼 音序测试</button>
                <button class="test-btn" onclick="testPerformance()">⚡ 性能测试</button>
            </div>
        </div>

        <div class="test-section">
            <h3>📱 触觉反馈测试</h3>
            <div class="test-buttons">
                <button class="test-btn" onclick="testHaptic('move')">📳 移动震动</button>
                <button class="test-btn" onclick="testHaptic('rotate')">📳 旋转震动</button>
                <button class="test-btn" onclick="testHaptic('tetris')">📳 Tetris震动</button>
                <button class="test-btn" onclick="testHaptic('gameOver')">📳 游戏结束震动</button>
            </div>
            <div id="hapticStatus" class="status" style="margin-top: 10px;">检测触觉反馈支持...</div>
        </div>
    </div>

    <script>
        // 复制音效管理器代码进行测试
        class SoundManager {
            constructor() {
                this.audioContext = null;
                this.sounds = {};
                this.volume = 0.5;
                this.muted = false;
                this.initialized = false;
            }

            init() {
                if (this.initialized) return;

                try {
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    this.initialized = true;
                    this.createSounds();
                    return true;
                } catch (error) {
                    console.warn('音效系统初始化失败:', error);
                    return false;
                }
            }

            createSounds() {
                if (!this.audioContext) return;

                this.sounds.move = () => this.playTone(300, 0.05, 'square');
                this.sounds.rotate = () => this.playTone(500, 0.08, 'sine');
                this.sounds.softDrop = () => this.playTone(200, 0.1, 'sawtooth');
                this.sounds.hardDrop = () => this.playSequence([
                    {freq: 800, duration: 0.05},
                    {freq: 600, duration: 0.05},
                    {freq: 400, duration: 0.1}
                ]);
                this.sounds.clear1 = () => this.playTone(600, 0.2, 'triangle');
                this.sounds.clear2 = () => this.playSequence([
                    {freq: 600, duration: 0.1},
                    {freq: 800, duration: 0.2}
                ]);
                this.sounds.clear3 = () => this.playSequence([
                    {freq: 600, duration: 0.1},
                    {freq: 800, duration: 0.1},
                    {freq: 1000, duration: 0.2}
                ]);
                this.sounds.tetris = () => this.playSequence([
                    {freq: 400, duration: 0.1},
                    {freq: 600, duration: 0.1},
                    {freq: 800, duration: 0.1},
                    {freq: 1000, duration: 0.1},
                    {freq: 1200, duration: 0.3}
                ]);
                this.sounds.gameOver = () => this.playSequence([
                    {freq: 400, duration: 0.2},
                    {freq: 300, duration: 0.2},
                    {freq: 200, duration: 0.2},
                    {freq: 100, duration: 0.5}
                ]);
                this.sounds.combo = () => this.playSequence([
                    {freq: 800, duration: 0.05},
                    {freq: 1000, duration: 0.05},
                    {freq: 1200, duration: 0.1}
                ]);
                this.sounds.tSpin = () => this.playSequence([
                    {freq: 500, duration: 0.1},
                    {freq: 700, duration: 0.1},
                    {freq: 900, duration: 0.2}
                ]);
                this.sounds.hold = () => this.playTone(400, 0.15, 'sine');
                this.sounds.levelUp = () => this.playSequence([
                    {freq: 400, duration: 0.1},
                    {freq: 600, duration: 0.1},
                    {freq: 800, duration: 0.1},
                    {freq: 1000, duration: 0.2}
                ]);
            }

            playTone(frequency, duration, type = 'sine') {
                if (!this.audioContext || this.muted) return;

                try {
                    const oscillator = this.audioContext.createOscillator();
                    const gainNode = this.audioContext.createGain();

                    oscillator.connect(gainNode);
                    gainNode.connect(this.audioContext.destination);

                    oscillator.type = type;
                    oscillator.frequency.value = frequency;

                    gainNode.gain.setValueAtTime(this.volume * 0.3, this.audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

                    oscillator.start(this.audioContext.currentTime);
                    oscillator.stop(this.audioContext.currentTime + duration);
                    return true;
                } catch (error) {
                    console.warn('音效播放失败:', error);
                    return false;
                }
            }

            playSequence(notes) {
                if (!this.audioContext || this.muted) return;

                notes.forEach((note, index) => {
                    setTimeout(() => {
                        this.playTone(note.freq, note.duration, 'sine');
                    }, index * (note.duration * 1000));
                });
            }

            play(soundName) {
                if (!this.initialized) {
                    this.init();
                }

                if (this.sounds[soundName]) {
                    return this.sounds[soundName]();
                }
                return false;
            }

            setVolume(volume) {
                this.volume = Math.max(0, Math.min(1, volume));
            }

            toggleMute() {
                this.muted = !this.muted;
                return this.muted;
            }

            getVolume() {
                return this.volume;
            }

            isMuted() {
                return this.muted;
            }
        }

        // 初始化测试
        const soundManager = new SoundManager();
        let isSupported = true;

        function checkCompatibility() {
            const compatibility = document.getElementById('compatibility');
            const status = document.getElementById('status');

            if (!window.AudioContext && !window.webkitAudioContext) {
                compatibility.innerHTML = '⚠️ 您的浏览器不支持 Web Audio API，音效功能将无法使用。';
                status.className = 'status error';
                status.innerHTML = '❌ 音效系统不可用';
                isSupported = false;
                return false;
            }

            if (!/Mobile|Android|iPhone|iPad|iPod/.test(navigator.userAgent)) {
                compatibility.innerHTML = '💻 桌面浏览器检测 - 音效功能完全支持';
            } else {
                compatibility.innerHTML = '📱 移动设备检测 - 支持音效和触觉反馈';
            }

            if (soundManager.init()) {
                status.innerHTML = '✅ 音效系统初始化成功';
                status.className = 'status';
                return true;
            } else {
                status.innerHTML = '❌ 音效系统初始化失败';
                status.className = 'status error';
                isSupported = false;
                return false;
            }
        }

        function updateStatus(message, type = '') {
            const status = document.getElementById('status');
            status.innerHTML = message;
            if (type) {
                status.className = `status ${type}`;
            }
        }

        // 测试触觉反馈
        function testHaptic(type) {
            const hapticStatus = document.getElementById('hapticStatus');

            if (!('vibrate' in navigator)) {
                hapticStatus.innerHTML = '❌ 您的设备不支持触觉反馈';
                hapticStatus.className = 'status error';
                return;
            }

            const patterns = {
                move: [20],
                rotate: [15, 50, 15],
                tetris: [50, 100, 50, 100, 50],
                gameOver: [200]
            };

            const pattern = patterns[type] || [50];
            navigator.vibrate(pattern);

            hapticStatus.innerHTML = `✅ ${type} 触觉反馈已触发`;
            hapticStatus.className = 'status';
        }

        // 测试音序
        function testSequence() {
            updateStatus('🎵 正在播放音序测试...');
            soundManager.playTone(440, 0.2); // A
            setTimeout(() => soundManager.playTone(494, 0.2), 200); // B
            setTimeout(() => soundManager.playTone(523, 0.2), 400); // C
            setTimeout(() => soundManager.playTone(587, 0.2), 600); // D
            setTimeout(() => soundManager.playTone(659, 0.2), 800); // E
            setTimeout(() => {
                updateStatus('✅ 音序测试完成');
            }, 1000);
        }

        // 性能测试
        function testPerformance() {
            updateStatus('⚡ 正在进行性能测试...');
            const startTime = performance.now();
            let successCount = 0;

            for (let i = 0; i < 10; i++) {
                setTimeout(() => {
                    if (soundManager.play('move')) {
                        successCount++;
                    }

                    if (i === 9) {
                        const endTime = performance.now();
                        const duration = endTime - startTime;
                        updateStatus(`⚡ 性能测试完成 - 10个音效耗时${duration.toFixed(2)}ms，成功率${successCount}/10`);
                    }
                }, i * 50);
            }
        }

        // 事件监听器
        document.addEventListener('DOMContentLoaded', () => {
            const compatible = checkCompatibility();

            // 音效按钮事件
            document.querySelectorAll('[data-sound]').forEach(btn => {
                btn.addEventListener('click', () => {
                    if (!isSupported) {
                        updateStatus('❌ 音效系统不可用，无法播放音效', 'error');
                        return;
                    }

                    const soundName = btn.dataset.sound;
                    if (soundManager.play(soundName)) {
                        updateStatus(`🎵 播放音效: ${btn.textContent}`);
                    } else {
                        updateStatus(`❌ 音效播放失败: ${soundName}`, 'error');
                    }
                });
            });

            // 音量控制
            const volumeSlider = document.getElementById('volumeSlider');
            const volumeValue = document.getElementById('volumeValue');

            volumeSlider.addEventListener('input', (e) => {
                const volume = e.target.value / 100;
                soundManager.setVolume(volume);
                volumeValue.textContent = `${e.target.value}%`;
            });

            // 静音控制
            const muteToggle = document.getElementById('muteToggle');
            muteToggle.addEventListener('click', () => {
                const isMuted = soundManager.toggleMute();
                muteToggle.innerHTML = isMuted ? '🔇 音效关闭' : '🔊 音效开启';
                muteToggle.className = isMuted ? 'test-btn error' : 'test-btn';
                updateStatus(isMuted ? '🔇 音效已静音' : '🔊 音效已开启');
            });

            // 检测触觉反馈
            const hapticStatus = document.getElementById('hapticStatus');
            if ('vibrate' in navigator) {
                hapticStatus.innerHTML = '✅ 您的设备支持触觉反馈功能';
                hapticStatus.className = 'status';
            } else {
                hapticStatus.innerHTML = '⚠️ 您的设备不支持触觉反馈';
                hapticStatus.className = 'status warning';
            }
        });
    </script>
</body>
</html>