<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俄罗斯方块游戏 - 核心功能测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .test-header h1 {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .test-header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .test-controls {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .run-test-btn {
            padding: 12px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .run-test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .run-test-btn:active {
            transform: translateY(0);
        }

        .test-summary {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .summary-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .summary-label {
            font-size: 0.9em;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .summary-value {
            font-size: 1.5em;
            font-weight: bold;
        }

        .summary-value.total {
            color: #495057;
        }

        .summary-value.passed {
            color: #28a745;
        }

        .summary-value.failed {
            color: #dc3545;
        }

        .test-results {
            padding: 30px;
        }

        .test-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.3em;
            color: #495057;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }

        .test-item {
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #6c757d;
            background: #f8f9fa;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .test-item.passed {
            border-left-color: #28a745;
            background: #d4edda;
        }

        .test-item.failed {
            border-left-color: #dc3545;
            background: #f8d7da;
        }

        .test-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }

        .test-name {
            font-weight: bold;
            color: #212529;
        }

        .test-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }

        .test-status.passed {
            background: #28a745;
            color: white;
        }

        .test-status.failed {
            background: #dc3545;
            color: white;
        }

        .test-message {
            color: #6c757d;
            font-size: 0.95em;
            margin-top: 5px;
        }

        .console-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .console-output .success {
            color: #4ec9b0;
        }

        .console-output .error {
            color: #f48771;
        }

        .console-output .info {
            color: #569cd6;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .loading.active {
            display: block;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .game-preview {
            display: none;
            position: fixed;
            bottom: 0;
            right: 0;
            width: 0;
            height: 0;
            opacity: 0;
        }

        @media (max-width: 768px) {
            .test-controls {
                flex-direction: column;
            }

            .test-summary {
                width: 100%;
                justify-content: space-around;
            }

            .test-header h1 {
                font-size: 1.5em;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🎮 俄罗斯方块游戏</h1>
            <p>核心功能自动化测试套件</p>
        </div>

        <div class="test-controls">
            <button class="run-test-btn" onclick="runAllTests()">▶ 运行所有测试</button>
            <div class="test-summary">
                <div class="summary-item">
                    <span class="summary-label">总计</span>
                    <span class="summary-value total" id="totalTests">0</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">通过</span>
                    <span class="summary-value passed" id="passedTests">0</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">失败</span>
                    <span class="summary-value failed" id="failedTests">0</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">通过率</span>
                    <span class="summary-value total" id="passRate">0%</span>
                </div>
            </div>
        </div>

        <div class="test-results" id="testResults">
            <div class="loading active">
                <div class="spinner"></div>
                <p>准备运行测试...</p>
            </div>
        </div>
    </div>

    <!-- 游戏预览（用于测试，不显示） -->
    <div class="game-preview">
        <canvas id="gameBoard" width="300" height="600"></canvas>
        <canvas id="nextPieceCanvas" width="120" height="120"></canvas>
        <div id="score">0</div>
        <div id="level">1</div>
        <div id="lines">0</div>
        <div id="gameStatus">准备开始</div>
        <div id="highScore">0</div>
        <div id="gameTime">00:00</div>
        <button id="startBtn">开始</button>
        <button id="pauseBtn">暂停</button>
        <button id="resetBtn">重置</button>
    </div>

    <!-- 加载游戏脚本 -->
    <script src="game.js"></script>
    <script src="test_game_core.js"></script>

    <script>
        // 页面加载完成后自动运行测试
        window.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                runAllTests();
            }, 1000);
        });

        function runAllTests() {
            // 清空之前的测试结果
            const resultsContainer = document.getElementById('testResults');
            resultsContainer.innerHTML = '<div class="loading active"><div class="spinner"></div><p>正在运行测试...</p></div>';

            // 等待测试完成
            setTimeout(() => {
                displayTestResults();
            }, 1500);
        }

        function displayTestResults() {
            const resultsContainer = document.getElementById('testResults');

            // 更新汇总信息
            document.getElementById('totalTests').textContent = testResults.tests.length;
            document.getElementById('passedTests').textContent = testResults.passed;
            document.getElementById('failedTests').textContent = testResults.failed;

            const passRate = testResults.tests.length > 0
                ? (testResults.passed / testResults.tests.length * 100).toFixed(1)
                : 0;
            document.getElementById('passRate').textContent = passRate + '%';

            // 按类别组织测试结果
            const categories = {};
            testResults.tests.forEach(test => {
                const category = test.name.split(' ')[0];
                if (!categories[category]) {
                    categories[category] = [];
                }
                categories[category].push(test);
            });

            // 生成测试结果HTML
            let html = '';

            // 添加控制台输出区域
            html += '<div class="test-section">';
            html += '<div class="section-title">📊 测试概览</div>';
            html += `<div style="padding: 15px; background: ${passRate === 100 ? '#d4edda' : '#fff3cd'}; border-radius: 8px; margin-bottom: 20px;">`;
            html += `<h3 style="margin-bottom: 10px;">测试执行完成</h3>`;
            html += `<p>共执行 <strong>${testResults.tests.length}</strong> 个测试，`;
            html += `<strong style="color: #28a745;">${testResults.passed}</strong> 个通过，`;
            html += `<strong style="color: #dc3545;">${testResults.failed}</strong> 个失败。`;
            html += `通过率: <strong>${passRate}%</strong></p>`;
            html += '</div>';
            html += '</div>';

            // 按类别显示测试结果
            Object.keys(categories).forEach(category => {
                html += '<div class="test-section">';
                html += `<div class="section-title">${getCategoryIcon(category)} ${getCategoryName(category)}</div>`;

                categories[category].forEach(test => {
                    html += `<div class="test-item ${test.passed ? 'passed' : 'failed'}">`;
                    html += '<div class="test-item-header">';
                    html += `<span class="test-name">${test.name}</span>`;
                    html += `<span class="test-status ${test.passed ? 'passed' : 'failed'}">${test.passed ? '✓ 通过' : '✗ 失败'}</span>`;
                    html += '</div>';
                    html += `<div class="test-message">${test.message}</div>`;
                    html += '</div>';
                });

                html += '</div>';
            });

            resultsContainer.innerHTML = html;
        }

        function getCategoryIcon(category) {
            const icons = {
                '游戏板': '🎯',
                '方块': '🧱',
                '函数': '⚙️',
                '创建': '🏗️',
                '旋转': '🔄',
                '正常': '✅',
                '左边界': '⬅️',
                '右边界': '➡️',
                '底部': '⬇️',
                '消除': '💥',
                '分数': '🏆',
                '游戏': '🎮',
                '下一个': '👀',
                '开始': '▶️',
                '暂停': '⏸️',
                '重置': '🔄',
                '分数显示': '📊',
                '等级显示': '📈',
                '行数显示': '📏',
                '最高分': '🥇',
                '游戏时间': '⏱️',
                'Canvas': '🖼️'
            };
            return icons[category] || '📝';
        }

        function getCategoryName(category) {
            const names = {
                '游戏板': '游戏板配置',
                '方块': '方块定义',
                '函数': '核心函数',
                '创建': '方块创建',
                '旋转': '方块旋转',
                '正常': '碰撞检测',
                '左边界': '边界检测',
                '右边界': '边界检测',
                '底部': '底部检测',
                '消除': '行消除功能',
                '分数': '分数系统',
                '游戏': 'UI元素',
                '下一个': 'Canvas元素',
                '开始': '控制按钮',
                '暂停': '控制按钮',
                '重置': '控制按钮',
                '分数显示': '信息显示',
                '等级显示': '信息显示',
                '行数显示': '信息显示',
                '最高分': '统计信息',
                '游戏时间': '统计信息',
                'Canvas': 'Canvas配置'
            };
            return names[category] || category;
        }
    </script>
</body>
</html>