<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俄罗斯方块核心功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: white;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .test-pass {
            background: #4CAF50;
            color: white;
        }
        .test-fail {
            background: #f44336;
            color: white;
        }
        .test-info {
            background: #2196F3;
            color: white;
        }
        .game-display {
            display: inline-block;
            margin: 20px;
            vertical-align: top;
        }
        .canvas-container {
            border: 2px solid #444;
            border-radius: 5px;
            display: inline-block;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
        .stats {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px;
            margin: 5px 0;
            background: #444;
            border-radius: 5px;
        }
        .feature-list li.implemented {
            background: #4CAF50;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>俄罗斯方块核心功能测试</h1>

        <div class="test-section">
            <h2>功能特性清单</h2>
            <ul class="feature-list" id="featureList">
                <li data-feature="basic-movement">基础移动控制</li>
                <li data-feature="rotation">方块旋转系统</li>
                <li data-feature="wall-kick">墙踢机制</li>
                <li data-feature="ghost-piece">Ghost Piece预览</li>
                <li data-feature="hold-system">Hold保留系统</li>
                <li data-feature="t-spin">T-Spin检测</li>
                <li data-feature="combo">连击系统</li>
                <li data-feature="back-to-back">Back-to-Back奖励</li>
                <li data-feature="scoring">增强计分系统</li>
                <li data-feature="mobile">移动端支持</li>
                <li data-feature="accessibility">无障碍功能</li>
                <li data-feature="sound">音效系统</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>游戏测试区域</h2>
            <div class="game-display">
                <div class="canvas-container">
                    <canvas id="testCanvas" width="300" height="600"></canvas>
                </div>
                <div class="stats">
                    <div>分数: <span id="testScore">0</span></div>
                    <div>等级: <span id="testLevel">1</span></div>
                    <div>行数: <span id="testLines">0</span></div>
                    <div>连击: <span id="testCombo">0</span></div>
                </div>
            </div>
            <div class="controls">
                <h3>控制测试</h3>
                <button onclick="testMovement()">测试移动</button>
                <button onclick="testRotation()">测试旋转</button>
                <button onclick="testHold()">测试Hold</button>
                <button onclick="testTSpin()">测试T-Spin</button>
                <button onclick="testHardDrop()">测试快速下落</button>
                <button onclick="runAllTests()">运行所有测试</button>
            </div>
        </div>

        <div class="test-section">
            <h2>测试结果</h2>
            <div id="testResults"></div>
        </div>
    </div>

    <script src="game.js"></script>
    <script>
        // 测试框架
        class TestRunner {
            constructor() {
                this.results = [];
                this.currentTest = null;
            }

            runTest(name, testFunction) {
                this.currentTest = name;
                try {
                    const result = testFunction();
                    this.addResult(name, result, true);
                    return true;
                } catch (error) {
                    this.addResult(name, error.message, false);
                    return false;
                }
            }

            addResult(name, message, passed) {
                this.results.push({ name, message, passed });
                this.displayResult(name, message, passed);
            }

            displayResult(name, message, passed) {
                const resultsDiv = document.getElementById('testResults');
                const resultDiv = document.createElement('div');
                resultDiv.className = `test-result ${passed ? 'test-pass' : 'test-fail'}`;
                resultDiv.innerHTML = `<strong>${name}:</strong> ${message}`;
                resultsDiv.appendChild(resultDiv);
            }

            runAllTests() {
                this.results = [];
                document.getElementById('testResults').innerHTML = '';

                // 基础功能测试
                this.runTest('方块创建', () => {
                    const piece = createPiece();
                    return piece && piece.type && piece.shape && piece.color ? '成功创建方块' : '方块创建失败';
                });

                this.runTest('旋转系统', () => {
                    const piece = createPiece();
                    if (piece.type === 'O') return 'O方块不旋转，测试通过';
                    const rotated = rotatePiece(piece);
                    return rotated !== piece ? '旋转功能正常' : '旋转功能异常';
                });

                this.runTest('碰撞检测', () => {
                    const piece = createPiece();
                    piece.x = -5;
                    return checkCollision(piece, gameState.board) ? '碰撞检测正常' : '碰撞检测异常';
                });

                this.runTest('Ghost Piece', () => {
                    const piece = createPiece();
                    const ghostY = calculateGhostPiece(piece, gameState.board);
                    return typeof ghostY === 'number' && ghostY >= 0 ? 'Ghost Piece计算正常' : 'Ghost Piece计算异常';
                });

                this.runTest('T-Spin检测', () => {
                    const piece = { type: 'T', lastMove: 'rotate' };
                    const result = detectTSpin(piece, gameState.board, 'rotate');
                    return typeof result === 'boolean' ? 'T-Spin检测功能正常' : 'T-Spin检测功能异常';
                });

                this.runTest('Hold系统', () => {
                    return typeof holdCurrentPiece === 'function' ? 'Hold功能已实现' : 'Hold功能未实现';
                });

                this.runTest('计分系统', () => {
                    const oldScore = gameState.score;
                    updateScore(1, false);
                    const newScore = gameState.score;
                    return newScore > oldScore ? '计分系统正常' : '计分系统异常';
                });

                // 统计测试结果
                const passed = this.results.filter(r => r.passed).length;
                const total = this.results.length;

                this.displayResult('测试总结', `通过 ${passed}/${total} 项测试`, passed === total);

                // 更新功能清单
                this.updateFeatureList();
            }

            updateFeatureList() {
                const features = document.querySelectorAll('[data-feature]');
                features.forEach(feature => {
                    const featureName = feature.dataset.feature;
                    let implemented = false;

                    switch (featureName) {
                        case 'basic-movement':
                            implemented = typeof moveLeft === 'function' && typeof moveRight === 'function';
                            break;
                        case 'rotation':
                            implemented = typeof rotatePiece === 'function';
                            break;
                        case 'wall-kick':
                            implemented = typeof rotatePiece === 'function' && PIECES.I && PIECES.I.kickTable;
                            break;
                        case 'ghost-piece':
                            implemented = typeof calculateGhostPiece === 'function';
                            break;
                        case 'hold-system':
                            implemented = typeof holdCurrentPiece === 'function';
                            break;
                        case 't-spin':
                            implemented = typeof detectTSpin === 'function';
                            break;
                        case 'combo':
                            implemented = gameState.hasOwnProperty('combo');
                            break;
                        case 'back-to-back':
                            implemented = gameState.hasOwnProperty('backToBack');
                            break;
                        case 'scoring':
                            implemented = typeof updateScore === 'function';
                            break;
                        case 'mobile':
                            implemented = document.querySelector('.mobile-controls') !== null;
                            break;
                        case 'accessibility':
                            implemented = typeof GameUIManager === 'function';
                            break;
                        case 'sound':
                            implemented = typeof gameUI !== 'undefined' && gameUI.sounds;
                            break;
                    }

                    if (implemented) {
                        feature.classList.add('implemented');
                    }
                });
            }
        }

        // 测试函数
        function testMovement() {
            if (!gameState.isPlaying) {
                startGame();
            }

            // 模拟移动测试
            const originalX = gameState.currentPiece.x;
            moveLeft();
            const movedLeft = gameState.currentPiece.x < originalX;

            moveRight();
            const movedRight = gameState.currentPiece.x > gameState.currentPiece.x;

            alert(`移动测试: ${movedLeft && movedRight ? '通过' : '失败'}`);
        }

        function testRotation() {
            if (!gameState.isPlaying) {
                startGame();
            }

            const originalRotation = gameState.currentPiece.rotation;
            rotateCurrentPiece();
            const rotated = gameState.currentPiece.rotation !== originalRotation;

            alert(`旋转测试: ${rotated ? '通过' : '失败'}`);
        }

        function testHold() {
            if (!gameState.isPlaying) {
                startGame();
            }

            const originalPiece = gameState.currentPiece.type;
            holdCurrentPiece();
            const held = gameState.holdPiece === originalPiece;

            alert(`Hold测试: ${held ? '通过' : '失败'}`);
        }

        function testTSpin() {
            if (!gameState.isPlaying) {
                startGame();
            }

            // 简单的T-Spin测试
            const tSpinResult = detectTSpin(gameState.currentPiece, gameState.board, 'rotate');
            alert(`T-Spin测试: ${typeof tSpinResult === 'boolean' ? '功能正常' : '功能异常'}`);
        }

        function testHardDrop() {
            if (!gameState.isPlaying) {
                startGame();
            }

            const originalY = gameState.currentPiece.y;
            hardDrop();
            const dropped = gameState.currentPiece.y > originalY;

            alert(`快速下落测试: ${dropped ? '通过' : '失败'}`);
        }

        function runAllTests() {
            const testRunner = new TestRunner();
            testRunner.runAllTests();
        }

        // 页面加载完成后运行测试
        window.addEventListener('load', () => {
            const testRunner = new TestRunner();
            testRunner.updateFeatureList();

            // 显示游戏统计
            const updateTestStats = () => {
                document.getElementById('testScore').textContent = gameState.score;
                document.getElementById('testLevel').textContent = gameState.level;
                document.getElementById('testLines').textContent = gameState.lines;
                document.getElementById('testCombo').textContent = gameState.combo;
            };

            setInterval(updateTestStats, 100);
        });
    </script>
</body>
</html>