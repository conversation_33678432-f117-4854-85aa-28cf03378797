// 游戏配置
const BOARD_WIDTH = 10;
const BOARD_HEIGHT = 20;
const BLOCK_SIZE = 30;
const NEXT_PIECE_SIZE = 30;

// 音效系统
class SoundManager {
    constructor() {
        this.audioContext = null;
        this.sounds = {};
        this.volume = 0.5;
        this.muted = false;
        this.initialized = false;
    }

    // 初始化音频上下文
    init() {
        if (this.initialized) return;

        try {
            // 创建音频上下文（需要用户交互来启动）
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.initialized = true;
            this.createSounds();
            console.log('音效系统初始化成功');
        } catch (error) {
            console.warn('音效系统初始化失败:', error);
        }
    }

    // 创建各种音效
    createSounds() {
        if (!this.audioContext) return;

        // 移动音效 - 短促的点击声
        this.sounds.move = () => this.playTone(300, 0.05, 'square');

        // 旋转音效 - 稍高的音调
        this.sounds.rotate = () => this.playTone(500, 0.08, 'sine');

        // 软下落音效 - 低沉的咚声
        this.sounds.softDrop = () => this.playTone(200, 0.1, 'sawtooth');

        // 硬降音效 - 快速下降音效
        this.sounds.hardDrop = () => this.playSequence([
            {freq: 800, duration: 0.05},
            {freq: 600, duration: 0.05},
            {freq: 400, duration: 0.1}
        ]);

        // 单行消除音效
        this.sounds.clear1 = () => this.playTone(600, 0.2, 'triangle');

        // 双行消除音效
        this.sounds.clear2 = () => this.playSequence([
            {freq: 600, duration: 0.1},
            {freq: 800, duration: 0.2}
        ]);

        // 三行消除音效
        this.sounds.clear3 = () => this.playSequence([
            {freq: 600, duration: 0.1},
            {freq: 800, duration: 0.1},
            {freq: 1000, duration: 0.2}
        ]);

        // 四行消除(Tetris)音效 - 特效音
        this.sounds.tetris = () => this.playSequence([
            {freq: 400, duration: 0.1},
            {freq: 600, duration: 0.1},
            {freq: 800, duration: 0.1},
            {freq: 1000, duration: 0.1},
            {freq: 1200, duration: 0.3}
        ]);

        // 游戏结束音效
        this.sounds.gameOver = () => this.playSequence([
            {freq: 400, duration: 0.2},
            {freq: 300, duration: 0.2},
            {freq: 200, duration: 0.2},
            {freq: 100, duration: 0.5}
        ]);

        // 连击音效
        this.sounds.combo = () => this.playSequence([
            {freq: 800, duration: 0.05},
            {freq: 1000, duration: 0.05},
            {freq: 1200, duration: 0.1}
        ]);

        // T-Spin音效
        this.sounds.tSpin = () => this.playSequence([
            {freq: 500, duration: 0.1},
            {freq: 700, duration: 0.1},
            {freq: 900, duration: 0.2}
        ]);

        // 保留方块音效
        this.sounds.hold = () => this.playTone(400, 0.15, 'sine');

        // 等级提升音效
        this.sounds.levelUp = () => this.playSequence([
            {freq: 400, duration: 0.1},
            {freq: 600, duration: 0.1},
            {freq: 800, duration: 0.1},
            {freq: 1000, duration: 0.2}
        ]);
    }

    // 播放单个音调
    playTone(frequency, duration, type = 'sine') {
        if (!this.audioContext || this.muted) return;

        try {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            oscillator.type = type;
            oscillator.frequency.value = frequency;

            gainNode.gain.setValueAtTime(this.volume * 0.3, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + duration);
        } catch (error) {
            console.warn('音效播放失败:', error);
        }
    }

    // 播放音序
    playSequence(notes) {
        if (!this.audioContext || this.muted) return;

        notes.forEach((note, index) => {
            setTimeout(() => {
                this.playTone(note.freq, note.duration, 'sine');
            }, index * (note.duration * 1000));
        });
    }

    // 播放音效
    play(soundName) {
        if (!this.initialized) {
            this.init();
        }

        if (this.sounds[soundName]) {
            this.sounds[soundName]();
        }
    }

    // 设置音量
    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
    }

    // 静音切换
    toggleMute() {
        this.muted = !this.muted;
        return this.muted;
    }

    // 获取音量
    getVolume() {
        return this.volume;
    }

    // 获取静音状态
    isMuted() {
        return this.muted;
    }
}

// 创建全局音效管理器实例
const soundManager = new SoundManager();

// 游戏状态
let gameState = {
    board: [],
    currentPiece: null,
    nextPiece: null,
    holdPiece: null,
    canHold: true,
    score: 0,
    level: 1,
    lines: 0,
    isPlaying: false,
    isPaused: false,
    gameOver: false,
    dropTime: 0,
    dropInterval: 1000,
    combo: 0,
    backToBack: false,
    lastClearType: null,
    tSpinDetected: false,
    ghostPieceY: 0
};

// 方块形状定义（增强版）
const PIECES = {
    I: {
        shape: [
            [0, 0, 0, 0],
            [1, 1, 1, 1],
            [0, 0, 0, 0],
            [0, 0, 0, 0]
        ],
        color: '#00f0f0',
        kickTable: [
            {x: 0, y: 0}, {x: -1, y: 0}, {x: 2, y: 0}, {x: -1, y: 0}, {x: 2, y: 0}
        ]
    },
    O: {
        shape: [
            [1, 1],
            [1, 1]
        ],
        color: '#f0f000',
        kickTable: [] // O方块不需要旋转
    },
    T: {
        shape: [
            [0, 1, 0],
            [1, 1, 1],
            [0, 0, 0]
        ],
        color: '#a000f0',
        kickTable: [
            {x: 0, y: 0}, {x: -1, y: 0}, {x: 1, y: 0}, {x: 0, y: 1}, {x: -1, y: 1}, {x: 1, y: 1}
        ]
    },
    S: {
        shape: [
            [0, 1, 1],
            [1, 1, 0],
            [0, 0, 0]
        ],
        color: '#00f000',
        kickTable: [
            {x: 0, y: 0}, {x: -1, y: 0}, {x: 1, y: 0}, {x: 0, y: 1}, {x: -1, y: 1}, {x: 1, y: 1}
        ]
    },
    Z: {
        shape: [
            [1, 1, 0],
            [0, 1, 1],
            [0, 0, 0]
        ],
        color: '#f00000',
        kickTable: [
            {x: 0, y: 0}, {x: -1, y: 0}, {x: 1, y: 0}, {x: 0, y: 1}, {x: -1, y: 1}, {x: 1, y: 1}
        ]
    },
    J: {
        shape: [
            [1, 0, 0],
            [1, 1, 1],
            [0, 0, 0]
        ],
        color: '#0000f0',
        kickTable: [
            {x: 0, y: 0}, {x: -1, y: 0}, {x: 1, y: 0}, {x: 0, y: 1}, {x: -1, y: 1}, {x: 1, y: 1}
        ]
    },
    L: {
        shape: [
            [0, 0, 1],
            [1, 1, 1],
            [0, 0, 0]
        ],
        color: '#f0a000',
        kickTable: [
            {x: 0, y: 0}, {x: -1, y: 0}, {x: 1, y: 0}, {x: 0, y: 1}, {x: -1, y: 1}, {x: 1, y: 1}
        ]
    }
};

// 方块类型映射
const PIECE_TYPES = Object.keys(PIECES);
const PIECE_COLORS = PIECE_TYPES.map(type => PIECES[type].color);


// 初始化游戏
function initGame() {
    const gameBoard = document.getElementById('gameBoard');
    const nextPieceCanvas = document.getElementById('nextPieceCanvas');

    // 设置画布尺寸
    gameBoard.width = BOARD_WIDTH * BLOCK_SIZE;
    gameBoard.height = BOARD_HEIGHT * BLOCK_SIZE;
    nextPieceCanvas.width = 4 * NEXT_PIECE_SIZE;
    nextPieceCanvas.height = 4 * NEXT_PIECE_SIZE;

    // 初始化游戏板
    gameState.board = Array(BOARD_HEIGHT).fill().map(() => Array(BOARD_WIDTH).fill(0));

    // 绑定事件
    bindEvents();

    // 开始游戏循环
    gameLoop();
}

// 调整颜色亮度
function adjustBrightness(color, factor) {
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    const newR = Math.max(0, Math.min(255, r + r * factor));
    const newG = Math.max(0, Math.min(255, g + g * factor));
    const newB = Math.max(0, Math.min(255, b + b * factor));

    return `rgb(${Math.floor(newR)}, ${Math.floor(newG)}, ${Math.floor(newB)})`;
}

// 创建新方块（增强版）
function createPiece() {
    const type = PIECE_TYPES[Math.floor(Math.random() * PIECE_TYPES.length)];
    const pieceData = PIECES[type];
    return {
        type: type,
        shape: pieceData.shape,
        x: Math.floor(BOARD_WIDTH / 2) - Math.floor(pieceData.shape[0].length / 2),
        y: 0,
        color: pieceData.color,
        rotation: 0,
        lastMove: 'spawn'
    };
}

// 计算Ghost Piece位置
function calculateGhostPiece(piece, board) {
    let ghostY = piece.y;
    while (!checkCollision(piece, board, 0, ghostY - piece.y + 1)) {
        ghostY++;
    }
    return ghostY;
}

// 检测T-Spin
function detectTSpin(piece, board, lastMove) {
    if (piece.type !== 'T') return false;

    if (lastMove === 'rotate') {
        // 检查T-Spin条件：T方块的四个角是否有方块
        const corners = [
            {x: piece.x, y: piece.y},
            {x: piece.x + 2, y: piece.y},
            {x: piece.x, y: piece.y + 2},
            {x: piece.x + 2, y: piece.y + 2}
        ];

        let filledCorners = 0;
        corners.forEach(corner => {
            if (corner.x < 0 || corner.x >= BOARD_WIDTH ||
                corner.y >= BOARD_HEIGHT ||
                (corner.y >= 0 && board[corner.y][corner.x])) {
                filledCorners++;
            }
        });

        return filledCorners >= 3;
    }
    return false;
}

// 旋转方块（增强版，支持墙踢）
function rotatePiece(piece, clockwise = true) {
    if (piece.type === 'O') return piece; // O方块不旋转

    // 执行旋转
    const rotated = piece.shape[0].map((_, index) =>
        clockwise
            ? piece.shape.map(row => row[index]).reverse()
            : piece.shape.map(row => row[row.length - 1 - index])
    );

    // 尝试墙踢
    const kickTable = PIECES[piece.type].kickTable;
    for (let kick of kickTable) {
        const testPiece = {
            ...piece,
            shape: rotated,
            x: piece.x + kick.x,
            y: piece.y + kick.y,
            rotation: (piece.rotation + (clockwise ? 1 : 3)) % 4,
            lastMove: 'rotate'
        };

        if (!checkCollision(testPiece, gameState.board)) {
            return testPiece;
        }
    }

    return piece; // 无法旋转，返回原方块
}

// Hold功能
function holdCurrentPiece() {
    if (!gameState.currentPiece || !gameState.canHold) return;

    if (gameState.holdPiece) {
        // 交换当前方块和hold方块
        const temp = gameState.currentPiece;
        gameState.currentPiece = createHeldPiece(gameState.holdPiece);
        gameState.holdPiece = temp.type;
    } else {
        // 第一次hold
        gameState.holdPiece = gameState.currentPiece.type;
        gameState.currentPiece = gameState.nextPiece;
        gameState.nextPiece = createPiece();
    }

    gameState.canHold = false;
    renderHoldPiece();
    soundManager.play('hold');
}

// 创建Hold方块
function createHeldPiece(type) {
    const pieceData = PIECES[type];
    return {
        type: type,
        shape: pieceData.shape,
        x: Math.floor(BOARD_WIDTH / 2) - Math.floor(pieceData.shape[0].length / 2),
        y: 0,
        color: pieceData.color,
        rotation: 0,
        lastMove: 'spawn'
    };
}

// 检查碰撞
function checkCollision(piece, board, offsetX = 0, offsetY = 0) {
    for (let y = 0; y < piece.shape.length; y++) {
        for (let x = 0; x < piece.shape[y].length; x++) {
            if (piece.shape[y][x]) {
                const newX = piece.x + x + offsetX;
                const newY = piece.y + y + offsetY;

                if (newX < 0 || newX >= BOARD_WIDTH || newY >= BOARD_HEIGHT) {
                    return true;
                }

                if (newY >= 0 && board[newY][newX]) {
                    return true;
                }
            }
        }
    }
    return false;
}

// 固定方块到游戏板（增强版）
function lockPiece(piece, board) {
    for (let y = 0; y < piece.shape.length; y++) {
        for (let x = 0; x < piece.shape[y].length; x++) {
            if (piece.shape[y][x]) {
                const boardY = piece.y + y;
                const boardX = piece.x + x;
                if (boardY >= 0) {
                    const typeIndex = PIECE_TYPES.indexOf(piece.type);
                    board[boardY][boardX] = typeIndex + 1;
                }
            }
        }
    }
}

// 清除完整的行
function clearLines(board) {
    let linesCleared = 0;

    for (let y = BOARD_HEIGHT - 1; y >= 0; y--) {
        if (board[y].every(cell => cell !== 0)) {
            board.splice(y, 1);
            board.unshift(Array(BOARD_WIDTH).fill(0));
            linesCleared++;
            y++; // 重新检查当前行
        }
    }

    return linesCleared;
}

// 更新分数（增强版）
function updateScore(lines, isTSpin = false) {
    let baseScore = 0;
    let clearType = null;

    // 计算基础分数
    if (isTSpin) {
        // T-Spin奖励
        switch (lines) {
            case 0:
                baseScore = 400 * gameState.level; // T-Spin Mini
                clearType = 'T-Spin Mini';
                soundManager.play('tSpin');
                break;
            case 1:
                baseScore = 800 * gameState.level; // T-Spin Single
                clearType = 'T-Spin Single';
                soundManager.play('tSpin');
                break;
            case 2:
                baseScore = 1200 * gameState.level; // T-Spin Double
                clearType = 'T-Spin Double';
                soundManager.play('tSpin');
                break;
            case 3:
                baseScore = 1600 * gameState.level; // T-Spin Triple
                clearType = 'T-Spin Triple';
                soundManager.play('tSpin');
                break;
        }
    } else {
        // 普通消除
        const linePoints = [0, 100, 300, 500, 800];
        baseScore = linePoints[lines] * gameState.level;
        clearType = lines === 4 ? 'Tetris' : `${lines} Lines`;

        // 播放消除音效
        if (lines === 4) {
            soundManager.play('tetris');
            if (gameUI && gameUI.isHapticEnabled()) {
                gameUI.provideHapticFeedback('tetris');
            }
        } else if (lines === 3) {
            soundManager.play('clear3');
            if (gameUI && gameUI.isHapticEnabled()) {
                gameUI.provideHapticFeedback('clear3');
            }
        } else if (lines === 2) {
            soundManager.play('clear2');
            if (gameUI && gameUI.isHapticEnabled()) {
                gameUI.provideHapticFeedback('clear2');
            }
        } else if (lines === 1) {
            soundManager.play('clear1');
            if (gameUI && gameUI.isHapticEnabled()) {
                gameUI.provideHapticFeedback('clear1');
            }
        }
    }

    // 连击奖励
    let comboBonus = 0;
    if (lines > 0) {
        if (gameState.lastClearType !== null) {
            gameState.combo++;
            comboBonus = 50 * gameState.combo * gameState.level;
            // 连击音效
            if (gameState.combo > 1) {
                soundManager.play('combo');
                if (gameUI && gameUI.isHapticEnabled()) {
                    gameUI.provideHapticFeedback('combo');
                }
            }
        } else {
            gameState.combo = 0;
        }
    } else {
        gameState.combo = 0;
    }

    // Back-to-Back奖励
    let backToBackBonus = 0;
    if (gameState.backToBack && (lines === 4 || isTSpin)) {
        backToBackBonus = baseScore * 0.5;
    }

    // 更新游戏状态
    gameState.score += baseScore + comboBonus + backToBackBonus;
    gameState.lines += lines;
    gameState.lastClearType = clearType;

    // 更新Back-to-Back状态
    gameState.backToBack = (lines === 4 || isTSpin);

    // 更新等级
    const oldLevel = gameState.level;
    gameState.level = Math.floor(gameState.lines / 10) + 1;
    gameState.dropInterval = Math.max(100, 1000 - (gameState.level - 1) * 100);

    // 等级提升音效
    if (gameState.level > oldLevel) {
        soundManager.play('levelUp');
    }

    // 更新UI
    document.getElementById('score').textContent = gameState.score;
    document.getElementById('level').textContent = gameState.level;
    document.getElementById('lines').textContent = gameState.lines;
    document.getElementById('comboCount').textContent = gameState.combo;

    // 显示特殊消除效果
    if (lines > 0) {
        showClearEffect(clearType, baseScore, comboBonus, backToBackBonus);
    }

    // 更新最高分
    const highScore = localStorage.getItem('tetrisHighScore') || 0;
    if (gameState.score > highScore) {
        localStorage.setItem('tetrisHighScore', gameState.score);
        document.getElementById('highScore').textContent = gameState.score;
    }
}

// 显示消除效果
function showClearEffect(clearType, baseScore, comboBonus, backToBackBonus) {
    const effectText = [];
    effectText.push(`${clearType} +${baseScore}`);

    if (comboBonus > 0) {
        effectText.push(`Combo x${gameState.combo} +${comboBonus}`);
    }

    if (backToBackBonus > 0) {
        effectText.push(`Back-to-Back +${backToBackBonus}`);
    }

    // 创建临时显示元素
    const effectEl = document.createElement('div');
    effectEl.className = 'clear-effect';
    effectEl.innerHTML = effectText.join('<br>');
    effectEl.style.cssText = `
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        font-size: 18px;
        font-weight: bold;
        z-index: 1000;
        animation: fadeOut 2s ease-out forwards;
    `;

    document.querySelector('.game-board-wrapper').appendChild(effectEl);
    setTimeout(() => effectEl.remove(), 2000);
}

// 渲染游戏板
function renderBoard(board, ctx) {
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

    // 绘制网格
    ctx.strokeStyle = '#2c3e50';
    ctx.lineWidth = 1;

    for (let x = 0; x <= BOARD_WIDTH; x++) {
        ctx.beginPath();
        ctx.moveTo(x * BLOCK_SIZE, 0);
        ctx.lineTo(x * BLOCK_SIZE, BOARD_HEIGHT * BLOCK_SIZE);
        ctx.stroke();
    }

    for (let y = 0; y <= BOARD_HEIGHT; y++) {
        ctx.beginPath();
        ctx.moveTo(0, y * BLOCK_SIZE);
        ctx.lineTo(BOARD_WIDTH * BLOCK_SIZE, y * BLOCK_SIZE);
        ctx.stroke();
    }

    // 绘制已固定的方块
    for (let y = 0; y < BOARD_HEIGHT; y++) {
        for (let x = 0; x < BOARD_WIDTH; x++) {
            if (board[y][x]) {
                const colorIndex = board[y][x] - 1;
                drawBlock(ctx, x, y, PIECE_COLORS[colorIndex]);
            }
        }
    }
}

// 绘制方块（增强版）
function drawBlock(ctx, x, y, color, isGhost = false) {
    const gradient = ctx.createLinearGradient(
        x * BLOCK_SIZE, y * BLOCK_SIZE,
        (x + 1) * BLOCK_SIZE, (y + 1) * BLOCK_SIZE
    );

    if (isGhost) {
        // Ghost Piece样式
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        ctx.setLineDash([5, 5]);
        ctx.strokeRect(x * BLOCK_SIZE + 2, y * BLOCK_SIZE + 2, BLOCK_SIZE - 4, BLOCK_SIZE - 4);
        ctx.setLineDash([]);
    } else {
        // 普通方块样式
        gradient.addColorStop(0, color);
        gradient.addColorStop(1, adjustBrightness(color, -0.3));

        ctx.fillStyle = gradient;
        ctx.fillRect(x * BLOCK_SIZE + 1, y * BLOCK_SIZE + 1, BLOCK_SIZE - 2, BLOCK_SIZE - 2);

        // 添加高光效果
        ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.fillRect(x * BLOCK_SIZE + 1, y * BLOCK_SIZE + 1, BLOCK_SIZE - 2, 4);
    }
}

// 渲染Hold Piece
function renderHoldPiece(ctx, piece) {
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

    if (piece) {
        const pieceData = PIECES[piece];
        const offsetX = Math.floor((4 - pieceData.shape[0].length) / 2);
        const offsetY = Math.floor((4 - pieceData.shape.length) / 2);

        for (let y = 0; y < pieceData.shape.length; y++) {
            for (let x = 0; x < pieceData.shape[y].length; x++) {
                if (pieceData.shape[y][x]) {
                    const drawX = x + offsetX;
                    const drawY = y + offsetY;
                    drawBlock(ctx, drawX, drawY, pieceData.color);
                }
            }
        }
    }
}

// 渲染下一个方块（增强版）
function renderNextPiece(ctx, piece) {
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

    if (piece) {
        const offsetX = Math.floor((4 - piece.shape[0].length) / 2);
        const offsetY = Math.floor((4 - piece.shape.length) / 2);

        for (let y = 0; y < piece.shape.length; y++) {
            for (let x = 0; x < piece.shape[y].length; x++) {
                if (piece.shape[y][x]) {
                    const drawX = x + offsetX;
                    const drawY = y + offsetY;
                    drawBlock(ctx, drawX, drawY, piece.color);
                }
            }
        }
    }
}

// 游戏主循环
function gameLoop(currentTime) {
    if (gameState.isPlaying && !gameState.isPaused) {
        if (currentTime - gameState.dropTime > gameState.dropInterval) {
            moveDown();
            gameState.dropTime = currentTime;
        }

        render();
    }

    requestAnimationFrame(gameLoop);
}

// 移动方块（增强版）
function moveDown() {
    if (gameState.currentPiece && !checkCollision(gameState.currentPiece, gameState.board, 0, 1)) {
        gameState.currentPiece.y++;
        gameState.currentPiece.lastMove = 'down';
        soundManager.play('softDrop');
        if (gameUI && gameUI.isHapticEnabled()) {
            gameUI.provideHapticFeedback('softDrop');
        }
    } else if (gameState.currentPiece) {
        lockPiece(gameState.currentPiece, gameState.board);

        // 检测T-Spin
        const isTSpin = detectTSpin(gameState.currentPiece, gameState.board, gameState.currentPiece.lastMove);

        const lines = clearLines(gameState.board);
        updateScore(lines, isTSpin);

        gameState.currentPiece = gameState.nextPiece;
        gameState.nextPiece = createPiece();
        gameState.canHold = true; // 重置Hold权限

        if (checkCollision(gameState.currentPiece, gameState.board)) {
            gameOver();
        }
    }
}

// 左移
function moveLeft() {
    if (gameState.currentPiece && !checkCollision(gameState.currentPiece, gameState.board, -1, 0)) {
        gameState.currentPiece.x--;
        gameState.currentPiece.lastMove = 'left';
        soundManager.play('move');
        if (gameUI && gameUI.isHapticEnabled()) {
            gameUI.provideHapticFeedback('move');
        }
        render();
    }
}

// 右移
function moveRight() {
    if (gameState.currentPiece && !checkCollision(gameState.currentPiece, gameState.board, 1, 0)) {
        gameState.currentPiece.x++;
        gameState.currentPiece.lastMove = 'right';
        soundManager.play('move');
        if (gameUI && gameUI.isHapticEnabled()) {
            gameUI.provideHapticFeedback('move');
        }
        render();
    }
}

// 旋转（增强版）
function rotateCurrentPiece(clockwise = true) {
    if (gameState.currentPiece) {
        const rotated = rotatePiece(gameState.currentPiece, clockwise);
        if (rotated !== gameState.currentPiece) {
            gameState.currentPiece = rotated;
            soundManager.play('rotate');
            if (gameUI && gameUI.isHapticEnabled()) {
                gameUI.provideHapticFeedback('rotate');
            }
            render();
        }
    }
}

// 快速下落（增强版）
function hardDrop() {
    if (gameState.currentPiece) {
        while (!checkCollision(gameState.currentPiece, gameState.board, 0, 1)) {
            gameState.currentPiece.y++;
        }

        soundManager.play('hardDrop');
        if (gameUI && gameUI.isHapticEnabled()) {
            gameUI.provideHapticFeedback('hardDrop');
        }
        gameState.currentPiece.lastMove = 'hard_drop';
        lockPiece(gameState.currentPiece, gameState.board);

        // 检测T-Spin
        const isTSpin = detectTSpin(gameState.currentPiece, gameState.board, gameState.currentPiece.lastMove);

        const lines = clearLines(gameState.board);
        updateScore(lines, isTSpin);

        gameState.currentPiece = gameState.nextPiece;
        gameState.nextPiece = createPiece();
        gameState.canHold = true; // 重置Hold权限

        if (checkCollision(gameState.currentPiece, gameState.board)) {
            gameOver();
        }

        render();
    }
}

// 开始游戏（增强版）
function startGame() {
    gameState.board = Array(BOARD_HEIGHT).fill().map(() => Array(BOARD_WIDTH).fill(0));
    gameState.currentPiece = createPiece();
    gameState.nextPiece = createPiece();
    gameState.holdPiece = null;
    gameState.canHold = true;
    gameState.score = 0;
    gameState.level = 1;
    gameState.lines = 0;
    gameState.isPlaying = true;
    gameState.isPaused = false;
    gameState.gameOver = false;
    gameState.dropTime = 0;
    gameState.dropInterval = 1000;
    gameState.combo = 0;
    gameState.backToBack = false;
    gameState.lastClearType = null;
    gameState.tSpinDetected = false;

    // 更新UI
    document.getElementById('score').textContent = '0';
    document.getElementById('level').textContent = '1';
    document.getElementById('lines').textContent = '0';
    document.getElementById('comboCount').textContent = '0';
    document.getElementById('gameStatus').textContent = '游戏进行中';
    document.getElementById('gameStatus').className = 'game-status playing';

    // 清除Hold Piece显示
    const holdCtx = document.getElementById('holdPieceCanvas');
    if (holdCtx) {
        holdCtx.getContext('2d').clearRect(0, 0, holdCtx.width, holdCtx.height);
    }

    render();
}

// 暂停游戏
function pauseGame() {
    if (gameState.isPlaying && !gameState.gameOver) {
        gameState.isPaused = !gameState.isPaused;
        const statusEl = document.getElementById('gameStatus');
        if (gameState.isPaused) {
            statusEl.textContent = '游戏暂停';
            statusEl.className = 'game-status paused';
        } else {
            statusEl.textContent = '游戏进行中';
            statusEl.className = 'game-status playing';
        }
    }
}

// 重置游戏
function resetGame() {
    gameState.isPlaying = false;
    gameState.isPaused = false;
    gameState.gameOver = false;

    const statusEl = document.getElementById('gameStatus');
    statusEl.textContent = '准备开始';
    statusEl.className = 'game-status ready';

    const ctx = document.getElementById('gameBoard').getContext('2d');
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

    const nextCtx = document.getElementById('nextPieceCanvas').getContext('2d');
    nextCtx.clearRect(0, 0, nextCtx.canvas.width, nextCtx.canvas.height);
}

// 游戏结束
function gameOver() {
    gameState.gameOver = true;
    gameState.isPlaying = false;

    soundManager.play('gameOver');
    if (gameUI && gameUI.isHapticEnabled()) {
        gameUI.provideHapticFeedback('gameOver');
    }

    const statusEl = document.getElementById('gameStatus');
    statusEl.textContent = `游戏结束 - 最终分数: ${gameState.score}`;
    statusEl.className = 'game-status game-over';
}

// 渲染游戏（增强版）
function render() {
    const ctx = document.getElementById('gameBoard').getContext('2d');
    const nextCtx = document.getElementById('nextPieceCanvas').getContext('2d');

    // 复制当前游戏板状态
    const displayBoard = gameState.board.map(row => [...row]);

    // 计算并绘制Ghost Piece
    if (gameState.currentPiece) {
        gameState.ghostPieceY = calculateGhostPiece(gameState.currentPiece, gameState.board);

        // 绘制Ghost Piece
        for (let y = 0; y < gameState.currentPiece.shape.length; y++) {
            for (let x = 0; x < gameState.currentPiece.shape[y].length; x++) {
                if (gameState.currentPiece.shape[y][x]) {
                    const ghostY = gameState.ghostPieceY + y;
                    const ghostX = gameState.currentPiece.x + x;
                    if (ghostY >= 0 && ghostY < BOARD_HEIGHT && ghostX >= 0 && ghostX < BOARD_WIDTH) {
                        drawBlock(ctx, ghostX, ghostY, gameState.currentPiece.color, true);
                    }
                }
            }
        }

        // 绘制当前方块
        for (let y = 0; y < gameState.currentPiece.shape.length; y++) {
            for (let x = 0; x < gameState.currentPiece.shape[y].length; x++) {
                if (gameState.currentPiece.shape[y][x]) {
                    const boardY = gameState.currentPiece.y + y;
                    const boardX = gameState.currentPiece.x + x;
                    if (boardY >= 0) {
                        const typeIndex = PIECE_TYPES.indexOf(gameState.currentPiece.type);
                        displayBoard[boardY][boardX] = typeIndex + 1;
                    }
                }
            }
        }
    }

    renderBoard(displayBoard, ctx);
    renderNextPiece(nextCtx, gameState.nextPiece);
}

// 绑定事件监听器
function bindEvents() {
    // 键盘事件（增强版）
    document.addEventListener('keydown', (e) => {
        if (!gameState.isPlaying || gameState.gameOver) return;

        switch (e.key) {
            case 'ArrowLeft':
                e.preventDefault();
                moveLeft();
                break;
            case 'ArrowRight':
                e.preventDefault();
                moveRight();
                break;
            case 'ArrowDown':
                e.preventDefault();
                moveDown();
                break;
            case 'ArrowUp':
                e.preventDefault();
                rotateCurrentPiece(true); // 顺时针旋转
                break;
            case 'Control':
            case 'z':
            case 'Z':
                e.preventDefault();
                rotateCurrentPiece(false); // 逆时针旋转
                break;
            case ' ':
                e.preventDefault();
                hardDrop();
                break;
            case 'c':
            case 'C':
            case 'Shift':
                e.preventDefault();
                holdCurrentPiece();
                break;
            case 'p':
            case 'P':
                e.preventDefault();
                pauseGame();
                break;
        }
    });

    // 按钮事件
    document.getElementById('startBtn').addEventListener('click', startGame);
    document.getElementById('pauseBtn').addEventListener('click', pauseGame);
    document.getElementById('resetBtn').addEventListener('click', resetGame);

    // 高级触摸和手势识别系统
    class GestureManager {
        constructor() {
            this.touchStartX = 0;
            this.touchStartY = 0;
            this.touchStartTime = 0;
            this.longPressTimer = null;
            this.swipeThreshold = 30;
            this.longPressThreshold = 500;
            this.doubleTapThreshold = 300;
            this.lastTapTime = 0;
            this.tapCount = 0;
            this.isLongPress = false;
            this.gestureStartPoint = null;
            this.gestureCurrentPoint = null;

            this.init();
        }

        init() {
            const gameBoard = document.getElementById('gameBoard');

            // 基础触摸事件
            gameBoard.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
            gameBoard.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
            gameBoard.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });
            gameBoard.addEventListener('touchcancel', this.handleTouchCancel.bind(this), { passive: false });

            // 防止默认的触摸行为
            gameBoard.addEventListener('contextmenu', (e) => e.preventDefault());
        }

        handleTouchStart(e) {
            if (!gameState.isPlaying || gameState.gameOver) return;

            e.preventDefault();
            const touch = e.touches[0];

            this.touchStartX = touch.clientX;
            this.touchStartY = touch.clientY;
            this.touchStartTime = Date.now();
            this.isLongPress = false;
            this.gestureStartPoint = { x: touch.clientX, y: touch.clientY };

            // 设置长按检测
            this.longPressTimer = setTimeout(() => {
                this.isLongPress = true;
                this.handleLongPress();
            }, this.longPressThreshold);

            // 双击检测
            const currentTime = Date.now();
            if (currentTime - this.lastTapTime < this.doubleTapThreshold) {
                this.tapCount++;
                if (this.tapCount === 2) {
                    this.handleDoubleTap();
                    this.tapCount = 0;
                }
            } else {
                this.tapCount = 1;
            }
            this.lastTapTime = currentTime;
        }

        handleTouchMove(e) {
            if (!gameState.isPlaying || gameState.gameOver) return;

            e.preventDefault();
            const touch = e.touches[0];
            this.gestureCurrentPoint = { x: touch.clientX, y: touch.clientY };

            // 如果移动距离过大，取消长按
            const moveDistance = Math.sqrt(
                Math.pow(touch.clientX - this.touchStartX, 2) +
                Math.pow(touch.clientY - this.touchStartY, 2)
            );

            if (moveDistance > 10) {
                clearTimeout(this.longPressTimer);
            }
        }

        handleTouchEnd(e) {
            if (!gameState.isPlaying || gameState.gameOver) return;

            e.preventDefault();
            clearTimeout(this.longPressTimer);

            if (this.isLongPress) {
                this.isLongPress = false;
                return;
            }

            const touchEndTime = Date.now();
            const touchDuration = touchEndTime - this.touchStartTime;

            // 计算滑动距离和方向
            const deltaX = this.gestureCurrentPoint ?
                this.gestureCurrentPoint.x - this.touchStartX : 0;
            const deltaY = this.gestureCurrentPoint ?
                this.gestureCurrentPoint.y - this.touchStartY : 0;

            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

            // 根据滑动距离和时长判断手势类型
            if (distance > this.swipeThreshold && touchDuration < 500) {
                this.handleSwipe(deltaX, deltaY);
            } else if (touchDuration < 200 && distance < 10) {
                this.handleTap();
            }

            this.gestureCurrentPoint = null;
        }

        handleTouchCancel(e) {
            clearTimeout(this.longPressTimer);
            this.isLongPress = false;
            this.gestureCurrentPoint = null;
        }

        handleSwipe(deltaX, deltaY) {
            const absX = Math.abs(deltaX);
            const absY = Math.abs(deltaY);

            if (absX > absY) {
                // 水平滑动
                if (deltaX > this.swipeThreshold) {
                    // 右滑 - 快速向右移动
                    this.executeWithFeedback('moveRight');
                    // 如果滑动距离很大，连续移动
                    if (deltaX > 100) {
                        setTimeout(() => this.executeWithFeedback('moveRight'), 100);
                    }
                } else if (deltaX < -this.swipeThreshold) {
                    // 左滑 - 快速向左移动
                    this.executeWithFeedback('moveLeft');
                    // 如果滑动距离很大，连续移动
                    if (deltaX < -100) {
                        setTimeout(() => this.executeWithFeedback('moveLeft'), 100);
                    }
                }
            } else {
                // 垂直滑动
                if (deltaY > this.swipeThreshold) {
                    // 下滑 - 根据距离决定是软下落还是硬降
                    if (deltaY > 150) {
                        this.executeWithFeedback('hardDrop');
                    } else {
                        this.executeWithFeedback('softDrop');
                    }
                } else if (deltaY < -this.swipeThreshold) {
                    // 上滑 - 旋转
                    this.executeWithFeedback('rotate');
                }
            }
        }

        handleTap() {
            // 单击 - 旋转
            this.executeWithFeedback('rotate');
        }

        handleDoubleTap() {
            // 双击 - 硬降
            this.executeWithFeedback('hardDrop');
        }

        handleLongPress() {
            // 长按 - 保留方块
            this.executeWithFeedback('holdPiece');

            // 触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate([100, 50, 100]);
            }

            // 视觉反馈
            this.showGestureIndicator('保留方块');
        }

        executeWithFeedback(action) {
            const actionMap = {
                'moveLeft': moveLeft,
                'moveRight': moveRight,
                'rotate': rotateCurrentPiece,
                'softDrop': moveDown,
                'hardDrop': hardDrop,
                'holdPiece': holdPiece
            };

            if (actionMap[action]) {
                actionMap[action]();
                this.provideHapticFeedback('light');
            }
        }

        provideHapticFeedback(type = 'light') {
            if (!('vibrate' in navigator)) return;

            const vibrationPatterns = {
                'light': [10],
                'medium': [20],
                'heavy': [50],
                'success': [10, 50, 10],
                'error': [100, 50, 100],
                'longPress': [100, 50, 100]
            };

            navigator.vibrate(vibrationPatterns[type] || vibrationPatterns.light);
        }

        showGestureIndicator(text) {
            // 创建手势指示器
            const indicator = document.createElement('div');
            indicator.className = 'gesture-indicator';
            indicator.textContent = text;
            indicator.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 15px 25px;
                border-radius: 25px;
                font-size: 1.2em;
                z-index: 10000;
                animation: fadeInOut 1.5s ease;
            `;

            document.body.appendChild(indicator);

            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.parentNode.removeChild(indicator);
                }
            }, 1500);
        }
    }

    // 创建手势管理器实例
    const gestureManager = new GestureManager();
}

// 游戏时间计时器
function updateGameTime() {
    if (gameState.isPlaying && !gameState.isPaused && !gameState.gameOver) {
        const startTime = localStorage.getItem('gameStartTime');
        if (!startTime) {
            localStorage.setItem('gameStartTime', Date.now().toString());
        } else {
            const elapsed = Math.floor((Date.now() - parseInt(startTime)) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            document.getElementById('gameTime').textContent =
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }
}

// 页面加载完成后初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    // 加载最高分
    const highScore = localStorage.getItem('tetrisHighScore') || 0;
    document.getElementById('highScore').textContent = highScore;

    // 初始化游戏
    initGame();

    // 启动游戏时间计时器
    setInterval(updateGameTime, 1000);
});

// 游戏扩展功能和无障碍支持
class GameUIManager {
    constructor() {
        this.pieceCount = 0;
        this.comboCount = 0;
        this.currentDifficulty = 2;
        this.gameTimeTimer = null;
        this.announcer = null;
        this.initAccessibility();
        this.initMobileControls();
        this.initDifficultySelector();
        this.initSoundEffects();
        this.initGameOverlay();
    }

    // 初始化无障碍功能
    initAccessibility() {
        // 创建屏幕阅读器通知器
        this.announcer = document.createElement('div');
        this.announcer.setAttribute('aria-live', 'polite');
        this.announcer.setAttribute('aria-atomic', 'true');
        this.announcer.className = 'sr-only';
        this.announcer.style.cssText = `
            position: absolute;
            left: -10000px;
            top: -10000px;
            width: 1px;
            height: 1px;
            overflow: hidden;
        `;
        document.body.appendChild(this.announcer);

        // 增强键盘导航
        this.setupKeyboardNavigation();
        this.setupFocusManagement();
    }

    // 设置键盘导航
    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            // Tab键导航增强
            if (e.key === 'Tab') {
                const focusableElements = document.querySelectorAll(
                    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
                );
                const firstElement = focusableElements[0];
                const lastElement = focusableElements[focusableElements.length - 1];

                if (e.shiftKey && document.activeElement === firstElement) {
                    e.preventDefault();
                    lastElement.focus();
                } else if (!e.shiftKey && document.activeElement === lastElement) {
                    e.preventDefault();
                    firstElement.focus();
                }
            }

            // WASD控制支持
            if (!gameState.isPlaying || gameState.gameOver) return;

            switch (e.key.toLowerCase()) {
                case 'a':
                    e.preventDefault();
                    moveLeft();
                    break;
                case 'd':
                    e.preventDefault();
                    moveRight();
                    break;
                case 's':
                    e.preventDefault();
                    moveDown();
                    break;
                case 'w':
                    e.preventDefault();
                    rotateCurrentPiece();
                    break;
                case 'enter':
                    e.preventDefault();
                    hardDrop();
                    break;
            }
        });
    }

    // 设置焦点管理
    setupFocusManagement() {
        // 为按钮添加焦点指示器
        const buttons = document.querySelectorAll('.control-btn, .mobile-btn, .difficulty-btn');
        buttons.forEach(btn => {
            btn.addEventListener('focus', () => {
                btn.style.transform = 'scale(1.05)';
            });
            btn.addEventListener('blur', () => {
                btn.style.transform = 'scale(1)';
            });
        });
    }

    // 初始化移动端控制
    initMobileControls() {
        // 检测移动设备
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        if (isMobile) {
            document.querySelector('.mobile-controls').style.display = 'block';
        }

        // 绑定移动端控制按钮
        const mobileControls = {
            'mobileLeft': moveLeft,
            'mobileRight': moveRight,
            'mobileRotate': rotateCurrentPiece,
            'mobileDown': moveDown,
            'mobileDrop': hardDrop,
            'mobileHold': holdPiece,
            'mobilePause': pauseGame
        };

        Object.entries(mobileControls).forEach(([id, action]) => {
            const btn = document.getElementById(id);
            if (btn) {
                let touchStartTime = 0;
                let longPressTimer = null;
                let isLongPress = false;

                // 按钮点击事件
                btn.addEventListener('click', () => {
                    if (!isLongPress && gameState.isPlaying && !gameState.gameOver) {
                        action();
                        gameController.provideHapticFeedback('light');
                    }
                });

                // 触摸开始
                btn.addEventListener('touchstart', (e) => {
                    e.preventDefault();
                    touchStartTime = Date.now();
                    isLongPress = false;

                    btn.classList.add('pressed');

                    // 长按检测
                    longPressTimer = setTimeout(() => {
                        isLongPress = true;
                        btn.classList.add('long-press');
                        gameController.provideHapticFeedback('medium');

                        // 某些按钮的长按特殊功能
                        if (id === 'mobileDrop') {
                            gameController.showConfirmationDialog('确认重置游戏？', () => {
                                resetGame();
                            });
                        }
                    }, 800);
                });

                // 触摸结束
                btn.addEventListener('touchend', (e) => {
                    e.preventDefault();
                    clearTimeout(longPressTimer);

                    btn.classList.remove('pressed', 'long-press');

                    const touchDuration = Date.now() - touchStartTime;

                    // 防止意外操作：如果触摸时间太短或太长，不执行动作
                    if (touchDuration < 50 || touchDuration > 1000 || isLongPress) {
                        return;
                    }

                    // 执行按钮动作
                    if (gameState.isPlaying && !gameState.gameOver) {
                        action();
                        gameController.provideHapticFeedback('light');
                    }
                });

                // 触摸取消
                btn.addEventListener('touchcancel', () => {
                    clearTimeout(longPressTimer);
                    btn.classList.remove('pressed', 'long-press');
                });
            }
        });
    }

    // 确认对话框函数
    showConfirmationDialog(message, onConfirm) {
        // 创建确认对话框
        const dialog = document.createElement('div');
        dialog.className = 'confirmation-dialog';
        dialog.innerHTML = `
            <div class="confirmation-content">
                <h3>确认操作</h3>
                <p>${message}</p>
                <div class="confirmation-buttons">
                    <button class="confirmation-btn cancel">取消</button>
                    <button class="confirmation-btn confirm">确认</button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        // 添加事件监听器
        const cancelBtn = dialog.querySelector('.cancel');
        const confirmBtn = dialog.querySelector('.confirm');

        cancelBtn.addEventListener('click', () => {
            document.body.removeChild(dialog);
            gameController.provideHapticFeedback('light');
            document.body.style.overflow = '';
        });

        confirmBtn.addEventListener('click', () => {
            document.body.removeChild(dialog);
            if (onConfirm) onConfirm();
            gameController.provideHapticFeedback('success');
            document.body.style.overflow = '';
        });

        // 触觉反馈
        gameController.provideHapticFeedback('medium');

        // 防止背景滚动
        document.body.style.overflow = 'hidden';
    }

    // 触觉反馈（如果设备支持）
    provideHapticFeedback(type = 'normal') {
        if (!('vibrate' in navigator)) return;

        const patterns = {
            // 移动 - 短促震动
            move: [20],
            // 旋转 - 双短震动
            rotate: [15, 50, 15],
            // 软下落 - 中等震动
            softDrop: [30],
            // 硬降 - 长震动
            hardDrop: [100],
            // 消除行 - 多重震动
            clear1: [30, 100, 30],
            clear2: [30, 50, 30, 50, 30],
            clear3: [30, 50, 30, 50, 30, 50, 30],
            tetris: [50, 100, 50, 100, 50],
            // 游戏结束 - 长震动
            gameOver: [200],
            // 连击 - 快速震动
            combo: [10, 20, 10, 20, 10],
            // 默认
            normal: [50]
        };

        const pattern = patterns[type] || patterns.normal;
        navigator.vibrate(pattern);
    }

    // 触觉反馈控制
    setHapticFeedback(enabled) {
        this.hapticEnabled = enabled;
        localStorage.setItem('tetrisHaptic', enabled.toString());
    }

    // 检查触觉反馈状态
    isHapticEnabled() {
        if (this.hapticEnabled === undefined) {
            const saved = localStorage.getItem('tetrisHaptic');
            this.hapticEnabled = saved !== 'false' && 'vibrate' in navigator;
        }
        return this.hapticEnabled;
    }

    // 初始化难度选择器
    initDifficultySelector() {
        const difficultyBtns = document.querySelectorAll('.difficulty-btn');
        difficultyBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const level = parseInt(btn.dataset.level);
                this.setDifficulty(level);

                // 更新UI
                difficultyBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                this.announce(`难度设置为${btn.textContent}`);
            });
        });
    }

    // 设置游戏难度
    setDifficulty(level) {
        this.currentDifficulty = level;
        const baseSpeed = 1000;
        const speedMultiplier = level - 1;
        gameState.dropInterval = Math.max(100, baseSpeed - (speedMultiplier * 200));
    }

    // 初始化音效
    initSoundEffects() {
        // 初始化音效控制器
        this.setupSoundControls();

        // 初始化音量设置
        this.loadSoundSettings();
    }

    // 设置音效控制器
    setupSoundControls() {
        const soundToggle = document.getElementById('soundToggle');
        const volumeSlider = document.getElementById('volumeSlider');
        const volumeValue = document.getElementById('volumeValue');

        // 音效开关按钮
        if (soundToggle) {
            soundToggle.addEventListener('click', () => {
                const isMuted = soundManager.toggleMute();
                this.updateSoundUI(isMuted);
                this.saveSoundSettings();

                // 播放测试音效
                if (!isMuted) {
                    soundManager.play('move');
                }
            });
        }

        // 音量滑块
        if (volumeSlider) {
            volumeSlider.addEventListener('input', (e) => {
                const volume = e.target.value / 100;
                soundManager.setVolume(volume);
                volumeValue.textContent = `${e.target.value}%`;
                this.saveSoundSettings();
            });

            // 滑块释放时播放测试音效
            volumeSlider.addEventListener('change', () => {
                if (!soundManager.isMuted()) {
                    soundManager.play('move');
                }
            });
        }
    }

    // 更新音效UI状态
    updateSoundUI(isMuted) {
        const soundIcon = document.getElementById('soundIcon');
        const soundText = document.getElementById('soundText');
        const soundToggle = document.getElementById('soundToggle');

        if (isMuted) {
            soundIcon.textContent = '🔇';
            soundText.textContent = '音效关闭';
            soundToggle.classList.add('muted');
        } else {
            soundIcon.textContent = '🔊';
            soundText.textContent = '音效开启';
            soundToggle.classList.remove('muted');
        }
    }

    // 加载音效设置
    loadSoundSettings() {
        const savedVolume = localStorage.getItem('tetrisVolume');
        const savedMuted = localStorage.getItem('tetrisMuted') === 'true';

        if (savedVolume !== null) {
            const volume = parseFloat(savedVolume);
            soundManager.setVolume(volume);

            const volumeSlider = document.getElementById('volumeSlider');
            const volumeValue = document.getElementById('volumeValue');
            if (volumeSlider && volumeValue) {
                volumeSlider.value = volume * 100;
                volumeValue.textContent = `${Math.round(volume * 100)}%`;
            }
        }

        if (savedMuted) {
            soundManager.muted = savedMuted;
            this.updateSoundUI(savedMuted);
        }
    }

    // 保存音效设置
    saveSoundSettings() {
        localStorage.setItem('tetrisVolume', soundManager.getVolume().toString());
        localStorage.setItem('tetrisMuted', soundManager.isMuted().toString());
    }

    // 播放简单音调
    playTone(frequency, duration) {
        if (!this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.value = frequency;
        oscillator.type = 'square';

        gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + duration);
    }

    // 初始化游戏覆盖层
    initGameOverlay() {
        this.overlay = document.getElementById('gameOverlay');
        this.overlayTitle = document.getElementById('overlayTitle');
        this.overlayMessage = document.getElementById('overlayMessage');
    }

    // 更新游戏统计
    updateStats() {
        // 更新方块数
        this.pieceCount++;
        document.getElementById('pieceCount').textContent = this.pieceCount;

        // 添加数值更新动画
        this.animateValue('score', gameState.score);
        this.animateValue('level', gameState.level);
        this.animateValue('lines', gameState.lines);
    }

    // 数值变化动画
    animateValue(elementId, newValue) {
        const element = document.getElementById(elementId);
        element.classList.add('updated');
        element.textContent = newValue;

        setTimeout(() => {
            element.classList.remove('updated');
        }, 500);
    }

    // 显示覆盖层消息
    showOverlay(title, message, duration = 0) {
        this.overlayTitle.textContent = title;
        this.overlayMessage.textContent = message;
        this.overlay.style.display = 'flex';

        if (duration > 0) {
            setTimeout(() => {
                this.hideOverlay();
            }, duration);
        }
    }

    // 隐藏覆盖层
    hideOverlay() {
        this.overlay.style.display = 'none';
    }

    // 屏幕阅读器通知
    announce(message) {
        if (this.announcer) {
            this.announcer.textContent = message;
        }
    }

    // 播放音效
    playSound(soundName) {
        if (this.sounds[soundName]) {
            this.sounds[soundName]();
        }
    }

    // 更新游戏状态
    updateGameState(newState) {
        const statusEl = document.getElementById('gameStatus');
        statusEl.className = `game-status ${newState}`;

        switch (newState) {
            case 'playing':
                statusEl.textContent = '游戏进行中';
                this.announce('游戏开始');
                this.hideOverlay();
                break;
            case 'paused':
                statusEl.textContent = '游戏暂停';
                this.announce('游戏暂停');
                this.showOverlay('游戏暂停', '按P键或点击继续按钮恢复游戏');
                break;
            case 'gameover':
                statusEl.textContent = `游戏结束 - 最终分数: ${gameState.score}`;
                this.announce(`游戏结束，最终分数${gameState.score}分`);
                this.showOverlay('游戏结束', `最终分数: ${gameState.score}分`);
                this.playSound('gameOver');
                break;
            case 'ready':
                statusEl.textContent = '准备开始';
                this.announce('准备开始游戏');
                this.showOverlay('准备开始', '点击开始按钮或按空格键开始游戏');
                break;
        }
    }
}

// 创建UI管理器实例
const gameUI = new GameUIManager();

// 增强现有游戏函数
const originalUpdateScore = updateScore;
const originalStartGame = startGame;
const originalPauseGame = pauseGame;
const originalGameOver = gameOver;
const originalResetGame = resetGame;
const originalMoveLeft = moveLeft;
const originalMoveRight = moveRight;
const originalRotateCurrentPiece = rotateCurrentPiece;
const originalHardDrop = hardDrop;

// 更新分数（增强版）
updateScore = function(lines) {
    originalUpdateScore(lines);
    gameUI.updateStats();

    if (lines > 0) {
        gameUI.comboCount = lines;
        document.getElementById('comboCount').textContent = gameUI.comboCount;
        gameUI.playSound('clear');
        gameUI.announce(`消除了${lines}行`);
    }
};

// 开始游戏（增强版）
startGame = function() {
    originalStartGame();
    gameUI.updateGameState('playing');
    gameUI.pieceCount = 0;
    gameUI.comboCount = 0;
    document.getElementById('pieceCount').textContent = '0';
    document.getElementById('comboCount').textContent = '0';
};

// 暂停游戏（增强版）
pauseGame = function() {
    originalPauseGame();
    gameUI.updateGameState(gameState.isPaused ? 'paused' : 'playing');
};

// 游戏结束（增强版）
gameOver = function() {
    originalGameOver();
    gameUI.updateGameState('gameover');
};

// 重置游戏（增强版）
resetGame = function() {
    originalResetGame();
    gameUI.updateGameState('ready');
};

// 左移（增强版）
moveLeft = function() {
    originalMoveLeft();
    gameUI.playSound('move');
};

// 右移（增强版）
moveRight = function() {
    originalMoveRight();
    gameUI.playSound('move');
};

// 旋转（增强版）
rotateCurrentPiece = function() {
    originalRotateCurrentPiece();
    gameUI.playSound('rotate');
};

// 快速下落（增强版）
hardDrop = function() {
    originalHardDrop();
    gameUI.playSound('drop');
};

// 导出增强的游戏对象
window.tetrisGame = {
    start: startGame,
    pause: pauseGame,
    reset: resetGame,
    moveLeft: moveLeft,
    moveRight: moveRight,
    rotate: rotateCurrentPiece,
    hardDrop: hardDrop,
    ui: gameUI
};