# 俄罗斯方块游戏使用说明

## 简介
这是一个基于HTML5 Canvas开发的俄罗斯方块游戏，具有完整的游戏功能和现代化的用户界面。游戏支持键盘和鼠标操作，适配桌面和移动设备。

## 安装与运行

### 系统要求
- 现代浏览器（Chrome 60+、Firefox 55+、Safari 12+、Edge 79+）
- 支持HTML5 Canvas功能
- 设备推荐：PC、平板电脑、智能手机

### 运行方法
1. 将项目文件部署到本地Web服务器
2. 或者直接在浏览器中打开HTML文件（某些功能可能受限）

**推荐使用本地服务器运行：**
```bash
# 使用Python内置服务器
python -m http.server 8000

# 访问 http://localhost:8000
```

## 游戏界面

### 主要组件
- **游戏画布**：主要游戏区域，方块下落和消除的地方
- **信息面板**：
  - 分数显示：当前游戏得分
  - 等级显示：当前游戏等级
  - 已消除行数：已成功消除的总行数
- **下一个方块预览**：显示即将出现的方块类型
- **控制按钮**：开始游戏、暂停、重置
- **操作说明**：键盘操作指南
- **游戏统计**：最高分、游戏时间

### 游戏状态
- **准备开始**：点击"开始游戏"按钮开始
- **游戏中**：方块正在下落
- **暂停**：游戏被暂停
- **游戏结束**：堆叠方块到达顶部

## 操作方式

### 键盘控制
| 按键 | 功能 | 说明 |
|------|------|------|
| ← | 左移 | 向左移动当前方块 |
| → | 右移 | 向右移动当前方块 |
| ↓ | 加速下落 | 加快方块的下降速度 |
| ↑ | 旋转 | 顺时针旋转当前方块 |
| 空格 | 直接落下 | 方块直接落到底部 |

### 鼠标/触摸控制
- 点击按钮执行相应操作
- 支持触摸屏滑动操作（移动设备）
- 点击"开始游戏"、"暂停"、"重置"按钮控制游戏

## 游戏规则

### 基础规则
1. 方块从顶部随机生成并下落
2. 玩家可以移动、旋转方块
3. 当方块触底或与其他方块碰撞时固定
4. 当一行被完全填满时，该行消除并获得分数
5. 方块堆叠高度到达游戏区域顶部时，游戏结束

### 方块类型
游戏包含7种经典方块类型：
- **I型**：长条形，4个方块组成
- **O型**：正方形，2×2方块组成
- **T型**：T形，3个方块在一行，1个方块在中间
- **S型**：S形，两个2×2方块错位排列
- **Z型**：Z形，与S型反向
- **J型**：J形，类似L型反向
- **L型**：L形，3个方块垂直，1个方块水平

### 计分规则
- **单行消除**：100分 × 当前等级
- **双行消除**：300分 × 当前等级
- **三行消除**：500分 × 当前等级
- **四行消除（Tetris）**：800分 × 当前等级

### 等级系统
- 初始等级：1级
- 每消除10行升一级
- 等级越高，方块下落速度越快

## 功能特性

### 游戏控制
- **开始游戏**：开始新游戏或继续暂停的游戏
- **暂停**：暂停当前游戏，可以随时继续
- **重置**：重新开始游戏，清空所有数据

### 数据持久化
- **最高分记录**：自动保存并显示历史最高分
- **游戏时间**：实时显示已游戏时间

### 响应式设计
- 自适应不同屏幕尺寸
- 支持横屏和竖屏模式
- 移动设备优化布局

## 技术架构

### 前端技术栈
- **HTML5 Canvas**：游戏渲染引擎
- **CSS3**：样式和动画
- **JavaScript**：游戏逻辑和交互

### 游戏引擎特性
- 60FPS流畅渲染
- 精确的碰撞检测
- 平滑的方块旋转
- 响应式用户界面

## 常见问题

### Q: 游戏无法开始？
A: 请确保使用现代浏览器，并检查是否有其他应用阻止了JavaScript执行。

### Q: 分数计算有误？
A: 分数计算基于消除的行数和当前等级，等级越高，单次消除得分越高。

### Q: 方块旋转不流畅？
A: 这可能是设备性能问题，建议关闭其他占用资源的程序。

### Q: 移动设备操作不灵敏？
A: 尝试清理浏览器缓存，或使用更稳定的网络环境。

## 性能优化

### 浏览器优化建议
- 使用最新的浏览器版本
- 启用硬件加速（Chrome、Firefox等）
- 关闭不必要的浏览器扩展

### 设备适配
- PC端：建议使用全屏模式获得最佳体验
- 移动端：保持屏幕清洁，确保触摸灵敏
- 平板端：横屏游戏体验更佳

## 开发说明

### 项目结构
```
├── index.html          # 主页面
├── styles.css          # 样式文件
├── game.js            # 游戏逻辑
└── CLAUDE.md          # 项目说明
```

### 核心模块
- **游戏状态管理**：控制游戏流程
- **方块系统**：生成、移动、旋转、碰撞
- **渲染引擎**：Canvas绘制优化
- **用户界面**：交互和反馈

## 后续开发计划

### 功能扩展
- 音效系统（背景音乐、音效）
- 粒子特效系统
- 难度递增机制
- 多人排行榜
- 存档系统

### 用户体验优化
- 暂停功能完善
- 快捷键配置
- 设置面板
- 更多的动画效果

### 性能优化
- 帧率优化
- 内存管理
- 移动设备性能适配

## 版本信息

- **当前版本**：1.0.0
- **开发状态**：基础功能已完成
- **兼容性**：支持主流现代浏览器
- **更新日志**：详见项目提交记录

---

**技术支持**：如有问题或建议，请通过以下方式联系：
- 项目Issue系统
- 邮件支持
- 社区讨论区

感谢使用俄罗斯方块游戏！