<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="description" content="经典俄罗斯方块游戏 - 支持键盘控制和触屏操作，包含多个难度等级、分数系统和保留方块功能">
    <meta name="keywords" content="俄罗斯方块, Tetris, 经典游戏, 益智游戏, 网页游戏, HTML5游戏, 方块游戏">
    <meta name="author" content="Tetris Game">
    <meta name="robots" content="index, follow">
    <meta name="theme-color" content="#3498db">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="俄罗斯方块">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-TileColor" content="#3498db">
    <meta name="msapplication-config" content="browserconfig.xml">
    <title>俄罗斯方块游戏 - 经典益智游戏</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='0.9em' font-size='90'>🎮</text></svg>">
</head>
<body>
    <div class="game-container">
        <header class="game-header">
            <h1>俄罗斯方块</h1>
            <div class="game-subtitle">经典益智游戏</div>
        </header>

        <main class="game-main">
            <section class="game-board-section">
                <div class="game-info">
                    <div class="score-display">
                        <h3>分数</h3>
                        <div id="score" class="score-value" aria-label="当前分数">0</div>
                    </div>
                    <div class="level-display">
                        <h3>等级</h3>
                        <div id="level" class="level-value" aria-label="当前等级">1</div>
                    </div>
                    <div class="lines-display">
                        <h3>已消除行数</h3>
                        <div id="lines" class="lines-value" aria-label="已消除行数">0</div>
                    </div>
                </div>

                <div class="game-board-wrapper">
                    <canvas id="gameBoard" class="game-board" width="300" height="600"
                            aria-label="游戏主界面" role="application" tabindex="0"></canvas>
                    <div class="game-overlay" id="gameOverlay">
                        <div class="overlay-content">
                            <h2 id="overlayTitle">准备开始</h2>
                            <p id="overlayMessage">点击开始按钮开始游戏</p>
                            <div class="overlay-tips">💡 提示：使用键盘方向键或屏幕下方按钮控制</div>
                        </div>
                    </div>
                </div>

                <!-- 移动端虚拟按键 -->
                <div class="mobile-controls">
                    <div class="mobile-control-row">
                        <button class="mobile-btn" id="mobileLeft" aria-label="向左移动"
                                data-action="left" touch-action="none">←</button>
                        <button class="mobile-btn" id="mobileRotate" aria-label="旋转方块"
                                data-action="rotate" touch-action="none">↻</button>
                        <button class="mobile-btn" id="mobileRight" aria-label="向右移动"
                                data-action="right" touch-action="none">→</button>
                        <button class="mobile-btn" id="mobileHold" aria-label="保留方块"
                                data-action="hold" touch-action="none">H</button>
                    </div>
                    <div class="mobile-control-row">
                        <button class="mobile-btn" id="mobileDown" aria-label="加速下落"
                                data-action="down" touch-action="none">↓</button>
                        <button class="mobile-btn" id="mobileDrop" aria-label="直接落下"
                                data-action="drop" touch-action="none">⬇</button>
                        <button class="mobile-btn" id="mobilePause" aria-label="暂停游戏"
                                data-action="pause" touch-action="none">⏸</button>
                    </div>
                </div>
            </section>

            <aside class="game-sidebar">
                <section class="hold-piece-section">
                    <h3>保留方块</h3>
                    <canvas id="holdPieceCanvas" class="hold-piece" width="120" height="120"
                            aria-label="保留方块预览"></canvas>
                    <div class="hold-instruction">按C或Shift键保留</div>
                </section>

                <section class="next-piece-section">
                    <h3>下一个方块</h3>
                    <canvas id="nextPieceCanvas" class="next-piece" width="120" height="120"
                            aria-label="下一个方块预览"></canvas>
                </section>

                <section class="game-controls">
                    <h3>游戏控制</h3>
                    <div class="control-buttons">
                        <button id="startBtn" class="control-btn primary-btn" aria-label="开始游戏"
                                data-action="start" autocomplete="off">
                            <span class="btn-icon">▶</span> 开始游戏
                        </button>
                        <button id="pauseBtn" class="control-btn secondary-btn" aria-label="暂停游戏"
                                data-action="pause" disabled autocomplete="off">
                            <span class="btn-icon">⏸</span> 暂停
                        </button>
                        <button id="resetBtn" class="control-btn secondary-btn" aria-label="重置游戏"
                                data-action="reset" autocomplete="off">
                            <span class="btn-icon">⟲</span> 重置
                        </button>
                    </div>
                </section>

                <section class="sound-controls">
                    <h3>音效设置</h3>
                    <div class="sound-controls-container">
                        <div class="sound-toggle">
                            <button id="soundToggle" class="control-btn sound-btn" aria-label="切换音效开关">
                                <span id="soundIcon" class="btn-icon">🔊</span>
                                <span id="soundText">音效开启</span>
                            </button>
                        </div>
                        <div class="volume-control">
                            <label for="volumeSlider" class="volume-label">音量</label>
                            <input type="range" id="volumeSlider" class="volume-slider"
                                   min="0" max="100" value="50" aria-label="音量调节">
                            <span id="volumeValue" class="volume-value">50%</span>
                        </div>
                    </div>
                </section>

                <section class="instructions">
                    <h3>操作说明</h3>
                    <div class="instruction-list">
                        <div class="instruction-item">
                            <span class="key">←</span>
                            <span>左移</span>
                        </div>
                        <div class="instruction-item">
                            <span class="key">→</span>
                            <span>右移</span>
                        </div>
                        <div class="instruction-item">
                            <span class="key">↓</span>
                            <span>加速下落</span>
                        </div>
                        <div class="instruction-item">
                            <span class="key">↑</span>
                            <span>顺时针旋转</span>
                        </div>
                        <div class="instruction-item">
                            <span class="key">Ctrl/Z</span>
                            <span>逆时针旋转</span>
                        </div>
                        <div class="instruction-item">
                            <span class="key">空格</span>
                            <span>直接落下</span>
                        </div>
                        <div class="instruction-item">
                            <span class="key">C/Shift</span>
                            <span>保留方块</span>
                        </div>
                        <div class="instruction-item">
                            <span class="key">P</span>
                            <span>暂停/继续</span>
                        </div>
                    </div>
                </section>

                <section class="game-stats">
                    <h3>游戏统计</h3>
                    <div class="stat-item">
                        <span>最高分:</span>
                        <span id="highScore" aria-label="最高分数">0</span>
                    </div>
                    <div class="stat-item">
                        <span>游戏时间:</span>
                        <span id="gameTime" aria-label="游戏时间">00:00</span>
                    </div>
                    <div class="stat-item">
                        <span>方块数:</span>
                        <span id="pieceCount" aria-label="已放置方块数">0</span>
                    </div>
                    <div class="stat-item">
                        <span>连击数:</span>
                        <span id="comboCount" aria-label="连击次数">0</span>
                    </div>
                </section>

                <section class="difficulty-selector">
                    <h3>难度设置</h3>
                    <div class="difficulty-buttons">
                        <button class="difficulty-btn" data-level="1">简单</button>
                        <button class="difficulty-btn active" data-level="2">普通</button>
                        <button class="difficulty-btn" data-level="3">困难</button>
                        <button class="difficulty-btn" data-level="4">极限</button>
                    </div>
                </section>
            </aside>
        </main>

        <footer class="game-footer">
            <div class="game-status" id="gameStatus" role="status" aria-live="polite">准备开始</div>
            <div class="footer-info">
                <span>使用方向键或WASD控制 | 按P键暂停</span>
            </div>
        </footer>
    </div>

    <!-- 音量控制 -->
    <div class="volume-control">
        <button id="volumeToggle" aria-label="切换音量" title="切换音效">
            <span id="volumeIcon">🔊</span>
        </button>
    </div>

    <!-- 音效支持 -->
    <audio id="moveSound" preload="auto"></audio>
    <audio id="rotateSound" preload="auto"></audio>
    <audio id="dropSound" preload="auto"></audio>
    <audio id="clearSound" preload="auto"></audio>
    <audio id="gameOverSound" preload="auto"></audio>

    <script src="game.js"></script>
</body>
</html>