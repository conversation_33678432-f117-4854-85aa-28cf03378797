* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    overflow-x: hidden;
}

.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    max-width: 1200px;
    width: 100%;
    overflow: hidden;
}

.game-header {
    background: linear-gradient(45deg, #2c3e50, #3498db);
    color: white;
    text-align: center;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.game-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.game-header h1 {
    font-size: 2.5em;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.game-subtitle {
    font-size: 0.9em;
    opacity: 0.8;
    margin-top: 5px;
    position: relative;
    z-index: 1;
}

.game-main {
    display: flex;
    padding: 20px;
    gap: 30px;
    flex-wrap: wrap;
}

.game-board-section {
    flex: 1;
    min-width: 350px;
}

.game-info {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.score-display, .level-display, .lines-display {
    background: linear-gradient(45deg, #34495e, #2c3e50);
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    text-align: center;
    min-width: 100px;
}

.score-display h3, .level-display h3, .lines-display h3 {
    font-size: 0.9em;
    margin-bottom: 5px;
    opacity: 0.8;
}

.score-value, .level-value, .lines-value {
    font-size: 1.8em;
    font-weight: bold;
    color: #f39c12;
}

.game-board-wrapper {
    background: #2c3e50;
    border-radius: 10px;
    padding: 10px;
    display: inline-block;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
}

.game-board {
    border: 2px solid #34495e;
    border-radius: 5px;
    background: #1a252f;
    display: block;
    transition: box-shadow 0.3s ease;
}

.game-board:hover {
    box-shadow: 0 0 20px rgba(52, 152, 219, 0.3);
}

.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    border-radius: 5px;
    backdrop-filter: blur(5px);
}

.overlay-content {
    text-align: center;
    color: white;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.overlay-content h2 {
    font-size: 2em;
    margin-bottom: 10px;
    color: #3498db;
}

.overlay-content p {
    font-size: 1.1em;
    opacity: 0.9;
    margin-bottom: 15px;
}

.overlay-tips {
    font-size: 0.9em;
    opacity: 0.7;
    margin-top: 10px;
    padding: 8px 12px;
    background: rgba(52, 152, 219, 0.2);
    border-radius: 5px;
    border: 1px solid rgba(52, 152, 219, 0.3);
}

/* 移动端控制 - 全面重新设计 */
.mobile-controls {
    display: none;
    margin-top: 20px;
    padding: 20px 0;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(155, 89, 182, 0.1));
    border-radius: 15px;
    backdrop-filter: blur(10px);
    position: sticky;
    bottom: 0;
    z-index: 100;
}

.mobile-control-row {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-bottom: 12px;
    padding: 0 20px;
}

.mobile-control-row:last-child {
    margin-bottom: 0;
}

.mobile-btn {
    width: 70px;
    height: 70px;
    border: none;
    border-radius: 15px;
    background: linear-gradient(145deg, #3498db, #2980b9);
    color: white;
    font-size: 1.8em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    user-select: none;
    -webkit-user-select: none;
    -webkit-tap-highlight-color: transparent;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.mobile-btn:active::before {
    width: 100px;
    height: 100px;
}

.mobile-btn:hover {
    background: linear-gradient(145deg, #2980b9, #21618c);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.mobile-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
    transition: all 0.05s ease;
}

/* 特殊按键样式 */
.mobile-btn[data-action="rotate"] {
    background: linear-gradient(145deg, #9b59b6, #8e44ad);
    box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
}

.mobile-btn[data-action="rotate"]:hover {
    background: linear-gradient(145deg, #8e44ad, #7d3c98);
    box-shadow: 0 6px 20px rgba(155, 89, 182, 0.4);
}

.mobile-btn[data-action="drop"] {
    background: linear-gradient(145deg, #e74c3c, #c0392b);
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.mobile-btn[data-action="drop"]:hover {
    background: linear-gradient(145deg, #c0392b, #a93226);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

.mobile-btn[data-action="hold"] {
    background: linear-gradient(145deg, #f39c12, #e67e22);
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.mobile-btn[data-action="hold"]:hover {
    background: linear-gradient(145deg, #e67e22, #d68910);
    box-shadow: 0 6px 20px rgba(243, 156, 18, 0.4);
}

.mobile-btn[data-action="pause"] {
    background: linear-gradient(145deg, #34495e, #2c3e50);
    box-shadow: 0 4px 15px rgba(52, 73, 94, 0.3);
}

.mobile-btn[data-action="pause"]:hover {
    background: linear-gradient(145deg, #2c3e50, #1a252f);
    box-shadow: 0 6px 20px rgba(52, 73, 94, 0.4);
}

/* 按钮反馈动画 */
.mobile-btn.pressed {
    animation: buttonPress 0.2s ease;
}

@keyframes buttonPress {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

/* 长按效果 */
.mobile-btn.long-press {
    animation: longPress 0.5s ease infinite alternate;
}

@keyframes longPress {
    from {
        background: linear-gradient(145deg, #3498db, #2980b9);
        transform: scale(1);
    }
    to {
        background: linear-gradient(145deg, #f39c12, #e67e22);
        transform: scale(1.05);
    }
}

.game-sidebar {
    width: 300px;
    min-width: 280px;
}

.next-piece-section {
    background: #ecf0f1;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    text-align: center;
}

.next-piece-section h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.next-piece {
    border: 2px solid #bdc3c7;
    border-radius: 5px;
    background: #34495e;
}

.game-controls {
    background: #ecf0f1;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.game-controls h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.control-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.control-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 5px;
    font-size: 1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.control-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    transform: translate(-50%, -50%);
    transition: width 0.5s, height 0.5s;
}

.control-btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn-icon {
    font-size: 1.2em;
}

.primary-btn {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
    color: white;
}

.primary-btn:hover {
    background: linear-gradient(45deg, #229954, #27ae60);
    transform: translateY(-2px);
}

.secondary-btn {
    background: linear-gradient(45deg, #95a5a6, #bdc3c7);
    color: #2c3e50;
}

.secondary-btn:hover {
    background: linear-gradient(45deg, #7f8c8d, #95a5a6);
    transform: translateY(-2px);
}

.control-btn:active {
    transform: translateY(0);
}

.instructions {
    background: #ecf0f1;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.instructions h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.instruction-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.instruction-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px 0;
}

.key {
    background: #34495e;
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-weight: bold;
    min-width: 40px;
    text-align: center;
}

.game-stats {
    background: #ecf0f1;
    border-radius: 10px;
    padding: 20px;
}

.game-stats h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 5px 0;
    border-bottom: 1px solid #bdc3c7;
}

.stat-item:last-child {
    border-bottom: none;
}

.game-footer {
    background: #34495e;
    color: white;
    text-align: center;
    padding: 15px;
}

.game-status {
    font-size: 1.1em;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .game-main {
        flex-direction: column;
        align-items: center;
    }

    .game-sidebar {
        width: 100%;
        max-width: 400px;
    }

    .game-info {
        justify-content: center;
    }

    .game-header h1 {
        font-size: 2em;
    }

    .control-buttons {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .control-btn {
        flex: 1;
        min-width: 120px;
    }
}

@media (max-width: 768px) {
    .game-main {
        flex-direction: column;
        align-items: center;
    }

    .game-sidebar {
        width: 100%;
        max-width: 400px;
    }

    .game-info {
        justify-content: center;
    }

    .game-header h1 {
        font-size: 2em;
    }

    .control-buttons {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .control-btn {
        flex: 1;
        min-width: 120px;
    }

    /* 移动端优化显示 */
    .mobile-controls {
        display: block;
    }

    .mobile-btn {
        width: 65px;
        height: 65px;
        font-size: 1.6em;
    }
}

@media (max-width: 480px) {
    .game-container {
        margin: 5px;
        border-radius: 15px;
        padding: 0;
    }

    .game-header {
        padding: 15px;
    }

    .game-header h1 {
        font-size: 1.6em;
    }

    .game-subtitle {
        font-size: 0.8em;
    }

    .game-main {
        padding: 10px;
        gap: 15px;
    }

    .game-board-section {
        min-width: auto;
        width: 100%;
    }

    .game-info {
        gap: 8px;
        margin-bottom: 15px;
    }

    .score-display, .level-display, .lines-display {
        min-width: 70px;
        padding: 8px 12px;
        border-radius: 8px;
    }

    .score-display h3, .level-display h3, .lines-display h3 {
        font-size: 0.8em;
    }

    .score-value, .level-value, .lines-value {
        font-size: 1.3em;
    }

    /* 移动端控制优化 */
    .mobile-controls {
        margin-top: 15px;
        padding: 15px 0;
        border-radius: 12px;
    }

    .mobile-control-row {
        gap: 10px;
        padding: 0 15px;
        margin-bottom: 10px;
    }

    .mobile-btn {
        width: 60px;
        height: 60px;
        font-size: 1.4em;
        border-radius: 12px;
    }

    .game-sidebar {
        width: 100%;
        max-width: 100%;
    }

    .game-sidebar section {
        margin-bottom: 15px;
        padding: 15px;
        border-radius: 10px;
    }

    .difficulty-buttons {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }

    .difficulty-btn {
        padding: 8px;
        font-size: 0.85em;
    }

    /* 控制按钮优化 */
    .control-buttons {
        gap: 8px;
    }

    .control-btn {
        padding: 10px 15px;
        font-size: 0.9em;
        min-width: 100px;
    }

    .btn-icon {
        font-size: 1em;
    }

    /* 音效控制移动端优化 */
    .sound-controls {
        padding: 15px;
        margin-bottom: 15px;
    }

    .sound-btn {
        padding: 8px 16px;
        font-size: 0.9em;
    }

    .volume-control {
        padding: 10px;
        gap: 10px;
    }

    .volume-label, .volume-value {
        font-size: 0.8em;
        min-width: 30px;
    }

    /* 游戏画布优化 */
    .game-board-wrapper {
        padding: 6px;
        border-radius: 8px;
    }

    #gameBoard {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
    }

    /* 操作说明优化 */
    .instructions {
        padding: 15px;
    }

    .instruction-item {
        font-size: 0.85em;
        padding: 3px 0;
    }

    .key {
        padding: 3px 8px;
        font-size: 0.8em;
        min-width: 35px;
    }

    /* 统计信息优化 */
    .game-stats {
        padding: 15px;
    }

    .game-stats h3 {
        font-size: 0.95em;
    }

    .stat-item {
        font-size: 0.85em;
        padding: 3px 0;
    }

    /* 页脚优化 */
    .game-footer {
        padding: 10px;
    }

    .game-status {
        font-size: 1em;
    }

    .footer-info {
        font-size: 0.8em;
    }

    /* 音量控制位置调整 */
    .volume-control {
        top: 10px;
        right: 10px;
    }

    .volume-control button {
        font-size: 1em;
        padding: 8px;
    }
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
    .game-header h1 {
        font-size: 1.4em;
    }

    .mobile-btn {
        width: 55px;
        height: 55px;
        font-size: 1.2em;
    }

    .mobile-control-row {
        gap: 8px;
        padding: 0 10px;
    }

    .control-btn {
        min-width: 85px;
        padding: 8px 12px;
        font-size: 0.85em;
    }

    .score-value, .level-value, .lines-value {
        font-size: 1.1em;
    }
}

/* 横屏模式适配 */
@media screen and (orientation: landscape) and (max-height: 600px) {
    .game-container {
        margin: 5px;
        max-height: 95vh;
        overflow-y: auto;
    }

    .game-header {
        padding: 10px;
    }

    .game-header h1 {
        font-size: 1.8em;
    }

    .game-subtitle {
        display: none;
    }

    .game-main {
        padding: 10px;
        gap: 15px;
        flex-direction: row;
        align-items: flex-start;
    }

    .game-board-section {
        flex: 1;
        min-width: auto;
    }

    .game-info {
        margin-bottom: 10px;
        gap: 5px;
    }

    .score-display, .level-display, .lines-display {
        padding: 8px 12px;
        min-width: 60px;
    }

    .score-display h3, .level-display h3, .lines-display h3 {
        font-size: 0.75em;
    }

    .score-value, .level-value, .lines-value {
        font-size: 1.1em;
    }

    /* 横屏模式的移动端控制 */
    .mobile-controls {
        position: fixed;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);
        width: auto;
        max-width: 400px;
        padding: 10px;
        background: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(15px);
        border-radius: 20px;
        z-index: 1000;
        margin: 0;
    }

    .mobile-control-row {
        gap: 8px;
        padding: 0;
        margin-bottom: 8px;
    }

    .mobile-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2em;
        border-radius: 10px;
    }

    .game-sidebar {
        width: 280px;
        min-width: 250px;
        max-height: 80vh;
        overflow-y: auto;
    }

    .game-sidebar section {
        margin-bottom: 10px;
        padding: 12px;
    }

    .game-sidebar h3 {
        font-size: 0.9em;
        margin-bottom: 8px;
    }

    .control-buttons {
        flex-direction: column;
        gap: 6px;
    }

    .control-btn {
        padding: 8px 12px;
        font-size: 0.85em;
        min-width: auto;
    }

    .difficulty-buttons {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
    }

    .difficulty-btn {
        padding: 6px;
        font-size: 0.8em;
    }

    .instructions {
        display: none;
    }

    .game-stats {
        padding: 12px;
    }

    .stat-item {
        font-size: 0.8em;
        padding: 2px 0;
    }

    .game-footer {
        padding: 8px;
        display: none;
    }
}

/* 大屏横屏模式 */
@media screen and (orientation: landscape) and (min-width: 1024px) and (max-height: 600px) {
    .game-main {
        padding: 20px;
        gap: 30px;
    }

    .game-sidebar {
        width: 350px;
        min-width: 320px;
    }

    .mobile-controls {
        max-width: 500px;
        padding: 15px;
    }

    .mobile-btn {
        width: 60px;
        height: 60px;
        font-size: 1.4em;
    }
}

/* 触屏设备优化 */
@media (hover: none) and (pointer: coarse) {
    .mobile-btn {
        transition: background 0.1s ease;
    }

    .mobile-btn:hover {
        transform: none;
    }

    .mobile-btn:active {
        transform: scale(0.95);
    }

    .control-btn:hover {
        transform: none;
    }

    .control-btn:active {
        transform: scale(0.98);
    }
}

/* 手势指示器样式 */
.gesture-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 15px 25px;
    border-radius: 25px;
    font-size: 1.2em;
    z-index: 10000;
    pointer-events: none;
    user-select: none;
    -webkit-user-select: none;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

@keyframes fadeInOut {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    20% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.05);
    }
    80% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
}

/* 防止意外操作的保护机制 */
.game-board {
    touch-action: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
}

/* 移动端游戏区域保护 */
.game-board-wrapper {
    position: relative;
    overflow: hidden;
}

/* 意外操作确认对话框 */
.confirmation-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 20000;
    backdrop-filter: blur(5px);
}

.confirmation-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    max-width: 80%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.confirmation-content h3 {
    margin-bottom: 15px;
    color: #2c3e50;
}

.confirmation-content p {
    margin-bottom: 20px;
    color: #7f8c8d;
}

.confirmation-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.confirmation-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
}

.confirmation-btn.confirm {
    background: #e74c3c;
    color: white;
}

.confirmation-btn.confirm:hover {
    background: #c0392b;
}

.confirmation-btn.cancel {
    background: #95a5a6;
    color: white;
}

.confirmation-btn.cancel:hover {
    background: #7f8c8d;
}

/* 移动端安全区域适配 */
@supports (padding: max(0px)) {
    .game-container {
        padding-left: max(20px, env(safe-area-inset-left));
        padding-right: max(20px, env(safe-area-inset-right));
        padding-bottom: max(20px, env(safe-area-inset-bottom));
    }

    .mobile-controls {
        padding-bottom: max(20px, env(safe-area-inset-bottom));
    }

    /* 横屏模式安全区域适配 */
    @media screen and (orientation: landscape) and (max-height: 600px) {
        .mobile-controls {
            bottom: max(10px, env(safe-area-inset-bottom));
        }
    }
}

/* 游戏状态样式 */
.game-status.playing {
    color: #2ecc71;
}

.game-status.paused {
    color: #f39c12;
}

.game-status.game-over {
    color: #e74c3c;
}

.game-status.ready {
    color: #3498db;
}

/* 难度选择器 */
.difficulty-selector {
    background: #ecf0f1;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.difficulty-selector h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.difficulty-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.difficulty-btn {
    padding: 10px;
    border: 2px solid #bdc3c7;
    border-radius: 5px;
    background: white;
    color: #2c3e50;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.difficulty-btn:hover {
    background: #3498db;
    color: white;
    border-color: #3498db;
    transform: translateY(-2px);
}

.difficulty-btn.active {
    background: #3498db;
    color: white;
    border-color: #2980b9;
}

/* 页脚信息 */
.footer-info {
    margin-top: 10px;
    font-size: 0.9em;
    opacity: 0.8;
}

/* 动画效果 */
.score-value, .level-value, .lines-value {
    transition: all 0.3s ease;
}

.score-value.updated, .level-value.updated, .lines-value.updated {
    animation: pulse 0.5s ease;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); color: #f39c12; }
    100% { transform: scale(1); }
}

/* 焦点样式 */
.control-btn:focus, .mobile-btn:focus, .difficulty-btn:focus {
    outline: 3px solid #3498db;
    outline-offset: 2px;
}

/* 加载动画 */
.loading {
    animation: loading 1s infinite alternate;
}

@keyframes loading {
    from { opacity: 0.5; }
    to { opacity: 1; }
}

/* 响应式增强 */
@media (max-width: 1200px) {
    .game-container {
        max-width: 100%;
        margin: 10px;
    }
}

/* 保留方块区域样式 */
.hold-piece-section {
    background: #ecf0f1;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    text-align: center;
}

.hold-piece-section h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.hold-piece {
    border: 2px solid #bdc3c7;
    border-radius: 5px;
    background: #34495e;
    margin-bottom: 10px;
}

.hold-instruction {
    font-size: 0.8em;
    color: #7f8c8d;
    margin-top: 8px;
    padding: 5px;
    background: rgba(52, 73, 94, 0.1);
    border-radius: 3px;
}

/* 游戏画布焦点样式 */
.game-board:focus {
    outline: 3px solid #3498db;
    outline-offset: 2px;
}

/* 音量控制样式 */
.volume-control {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50px;
    padding: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.volume-control button {
    background: none;
    border: none;
    font-size: 1.2em;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.volume-control button:hover {
    background: rgba(52, 152, 219, 0.2);
}

/* 游戏性能优化样式 */
.game-board {
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    .game-container {
        background: rgba(30, 30, 30, 0.95);
    }

    .game-sidebar section {
        background: #2c3e50 !important;
    }

    .game-sidebar section h3 {
        color: #ecf0f1 !important;
    }

    .difficulty-btn {
        background: #34495e !important;
        color: #ecf0f1 !important;
        border-color: #7f8c8d !important;
    }

    .difficulty-btn:hover {
        background: #3498db !important;
        border-color: #3498db !important;
    }
}

/* 音效控制样式 */
.sound-controls {
    background: #fff;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.sound-controls h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.2em;
    text-align: center;
}

.sound-controls-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.sound-toggle {
    display: flex;
    justify-content: center;
}

.sound-btn {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1em;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 10px rgba(46, 204, 113, 0.3);
}

.sound-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(46, 204, 113, 0.4);
}

.sound-btn.muted {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    box-shadow: 0 2px 10px rgba(231, 76, 60, 0.3);
}

.sound-btn.muted:hover {
    box-shadow: 0 4px 20px rgba(231, 76, 60, 0.4);
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 15px;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 12px;
    border: 2px solid #e9ecef;
}

.volume-label {
    color: #495057;
    font-weight: 600;
    font-size: 0.9em;
    min-width: 40px;
}

.volume-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: #dee2e6;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(45deg, #3498db, #2980b9);
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.volume-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 3px 10px rgba(52, 152, 219, 0.4);
}

.volume-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(45deg, #3498db, #2980b9);
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    border: none;
    transition: all 0.3s ease;
}

.volume-slider::-moz-range-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 3px 10px rgba(52, 152, 219, 0.4);
}

.volume-value {
    color: #495057;
    font-weight: 600;
    font-size: 0.9em;
    min-width: 40px;
    text-align: right;
}

/* 移动端音效控制适配 */
@media (max-width: 768px) {
    .sound-controls-container {
        gap: 10px;
    }

    .sound-btn {
        padding: 10px 20px;
        font-size: 0.9em;
    }

    .volume-control {
        padding: 12px;
        gap: 10px;
    }

    .volume-label, .volume-value {
        font-size: 0.8em;
        min-width: 35px;
    }
}
}